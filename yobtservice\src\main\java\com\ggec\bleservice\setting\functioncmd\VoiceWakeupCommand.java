package com.ggec.bleservice.setting.functioncmd;

import com.ggec.bleservice.setting.Command;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 语音唤醒命令
 * 负责控制耳机的语音唤醒功能，开启后可通过语音指令激活语音助手
 */
public class VoiceWakeupCommand extends Command {
    private static final String TAG = "VoiceWakeupCommand";
    
    // 命令前缀，用于确认是语音唤醒命令
    private static final String COMMAND_PREFIX = "99EC880001";
    
    // 开启语音唤醒命令
    private static final String COMMAND_ON = "99EC88000101"; 
    
    // 关闭语音唤醒命令
    private static final String COMMAND_OFF = "99EC88000100";
    
    // 命令校验和后缀
    private static final String COMMAND_SUFFIX = "1234";
    
    // 是否开启
    private final boolean isEnabled;
    
    /**
     * 构造方法
     * @param isEnabled 是否开启语音唤醒
     */
    public VoiceWakeupCommand(boolean isEnabled) {
        super();
        this.isEnabled = isEnabled;
        // 设置命令前缀
        setCommandPrefix("99EC88");
    }
    
    @Override
    public String getCommandData() {
        return (isEnabled ? COMMAND_ON : COMMAND_OFF) + COMMAND_SUFFIX;
    }
    
    @Override
    public String parseResponse(String responseData) {
        YoBTSDKLog.d(TAG, "解析响应数据: " + responseData);
        
        if (responseData == null || responseData.length() < 12) {
            return "响应数据格式错误";
        }
        
        // 获取命令状态字节
        String statusByte = responseData.substring(10, 12);
        
        // 判断是开启还是关闭状态
        boolean status = "01".equals(statusByte);
        int resultValue = status ? 1 : 0;
        
        // 判断设置是否成功
        boolean isSuccess = status == isEnabled;
        
        String result = isSuccess ? 
                "语音唤醒" + (isEnabled ? "开启" : "关闭") + "成功" : 
                "语音唤醒" + (isEnabled ? "开启" : "关闭") + "失败";
        
        // 通知命令完成（使用带结果值的回调）
        notifyCompletion(isSuccess ? ResultCode.SUCCESS : ResultCode.FAILED, resultValue, result);
        
        return result;
    }
    
    @Override
    public boolean isResponseMatch(String responseData) {
        // 检查响应前缀是否匹配
        if (responseData != null && responseData.length() >= 10) {
            String prefix = responseData.substring(0, 10);
            return COMMAND_PREFIX.equals(prefix);
        }
        return false;
    }
} 