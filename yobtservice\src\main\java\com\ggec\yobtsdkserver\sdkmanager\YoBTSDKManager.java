package com.ggec.yobtsdkserver.sdkmanager;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * YoBT SDK 管理类
 * 专门负责SDK版本管理
 */
public final class YoBTSDKManager {
    
    // SDK版本信息
    private static final String SDK_VERSION = "V1.0.0.4.0730";
    private static final int SDK_VERSION_CODE = 10004;
    private static final String SDK_BUILD_DATE = "2025-07-30";
    
    /**
     * 私有构造函数，防止实例化
     */
    private YoBTSDKManager() {
        // no-op
    }
    
    /**
     * 获取SDK版本
     * @return SDK版本字符串
     */
    public static String getSDKVersion() {
        return SDK_VERSION;
    }
    
    /**
     * 获取SDK版本号
     * @return SDK版本号
     */
    public static int getSDKVersionCode() {
        return SDK_VERSION_CODE;
    }
    
    /**
     * 获取SDK构建日期
     * @return SDK构建日期
     */
    public static String getSDKBuildDate() {
        return SDK_BUILD_DATE;
    }
} 