package com.bes.sdk.utils;

/**
 * Enum of protocol might be used, mainly use BLE, SPP for now.
 */
public enum DeviceProtocol
{
    PROTOCOL_UNKNOWN     (0,            "Unknown"),
    PROTOCOL_BLE         (0x00000001,   "Bluetooth Low Energy"),
    PROTOCOL_SPP         (0x00000010,   "Serial Port Profile"),
    PROTOCOL_WIFI        (0x00000100,   "Wi-Fi"),
    PROTOCOL_CABLE       (0x00001000,   "Cable"),
    PROTOCOL_USB         (0x00010000,   "USB"),
    PROTOCOL_GATT_BR_EDR (0x00100000,   "GATT_BR_EDR");

    private int mValue;
    private String mName;

    DeviceProtocol(int value, String name) {
        mValue = value;
        mName = name;
    }

    public int getValue() {
        return mValue;
    }

    public String getName() {
        return mName;
    }
}
