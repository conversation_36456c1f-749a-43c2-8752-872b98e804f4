package com.ggec.bleservice.setting;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.ggec.bleservice.core.BleDataCallback;
import com.ggec.bleservice.core.BleDataService;
import com.ggec.bleservice.setting.CommandManager;
import com.ggec.bleservice.setting.CommandCallbackManager;
import com.ggec.bleservice.setting.aggregatorcmd.AboutDeviceStatusAggregator;
import com.ggec.bleservice.setting.batterycmd.BatteryInfoAggregatorBlock;
import com.ggec.bleservice.setting.devicecmd.GlobalStatusCommand;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.Queue;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-29
 * Description: 
 * 统一命令队列中心
 * 负责所有命令的排队和执行，确保命令按顺序执行，并严格控制命令周期
 * 支持单个命令和命令块(CommandBlock)的执行
 */
public class CommandQueueCenter implements BleDataCallback {
    private static final String TAG = "CommandQueueCenter";
    
    private static CommandQueueCenter instance;
    
    private final Queue<IQueueable> commandQueue = new LinkedList<>();
    private IQueueable currentWorkItem = null;
    private Command currentCommand = null;
    
    // 是否正在执行命令
    private boolean isExecuting = false;
    
    // 设备是否就绪（服务发现和通知启用完成）
    private static volatile boolean isDeviceReady = false;
    
    // 默认命令超时时间（毫秒）- 增加到800ms以应对蓝牙通信延迟
    private static final long DEFAULT_COMMAND_TIMEOUT = 800;
    
    // 命令之间的执行间隔（毫秒）
    private static final long COMMAND_INTERVAL = 500;
    
    // 电量监听启动后的延迟时间（毫秒）
    private static final long BATTERY_MONITOR_DELAY = 500;
    
    // 临时暂停标志，用于电量监听启动后的延迟
    private boolean isTemporarilyPaused = false;
    
    // 主线程Handler
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    // 蓝牙数据服务
    private final BleDataService bleDataService;
    
    // 命令管理器
    private final CommandManager commandManager;
    
    // 命令回调管理器
    private final CommandCallbackManager callbackManager;

    // 用于存储命令块执行过程中的数据
    private HashMap<String, Object> blockResults;

    public static synchronized CommandQueueCenter getInstance() {
        if (instance == null) {
            instance = new CommandQueueCenter();
        }
        return instance;
    }
    
    /**
     * 私有构造方法
     */
    private CommandQueueCenter() {
        bleDataService = BleDataService.getInstance();
        commandManager = CommandManager.getInstance();
        callbackManager = CommandCallbackManager.getInstance();
        
        // 注册蓝牙数据回调
        bleDataService.registerCallback(this);
    }
    
    /**
     * 设置设备是否就绪
     * @param isReady true 表示设备已准备好接收命令，false 表示设备未连接或未准备好
     */
    public static synchronized void setDeviceReady(boolean isReady) {
        if (isDeviceReady != isReady) {
            isDeviceReady = isReady;
            //YoBTSDKLog.i(TAG, "设备就绪状态改变: " + isReady);
            if (isReady) {
                // 设备就绪时，触发命令队列执行
                getInstance().triggerExecution();
            }
        }
    }
    
    /**
     * 检查是否有命令正在执行
     * @return true 如果有命令正在执行，false 如果没有
     */
    public boolean isExecuting() {
        return isExecuting;
    }
    
    /**
     * 将命令加入队列
     * @param command 要执行的命令
     */
    public void enqueueCommand(Command command) {
        enqueue(command);
    }

    public void enqueueBlock(CommandBlock block) {
        enqueue(block);
    }

    private synchronized void enqueue(IQueueable item) {
        if (item == null) {
            return;
        }
        String id = item instanceof Command ? ((Command) item).getCommandId() : item.getClass().getSimpleName();
        YoBTSDKLog.d(TAG, "项已添加到队列: " + id);
        commandQueue.offer(item);
        if (!isExecuting) {
            executeNextCommand();
        }
    }
    
    /**
     * 执行下一个命令
     */
    private void executeNextCommand() {
        if (isExecuting) {
            YoBTSDKLog.d(TAG, "命令队列正在执行中，等待当前命令完成");
            return;
        }
        
        // 检查是否临时暂停
        if (isTemporarilyPaused) {
            YoBTSDKLog.d(TAG, "命令队列临时暂停中，等待恢复");
            return;
        }

        if (currentWorkItem == null) {
            if (commandQueue.isEmpty()) {
                YoBTSDKLog.d(TAG, "命令队列为空，无需执行");
                return;
            }
            currentWorkItem = commandQueue.poll();
            YoBTSDKLog.d(TAG, "从命令队列取一条命令执行: " + currentWorkItem.getClass().getSimpleName());
            if (currentWorkItem instanceof CommandBlock) {
                blockResults = new HashMap<>(); // 初始化结果集
            }
        }

        if (!isDeviceReady) {
            if (!commandQueue.isEmpty() || currentWorkItem != null) {
                YoBTSDKLog.w(TAG, "设备未就绪，命令将保留在队列中等待");
            }
            return;
        }
        if (currentWorkItem instanceof CommandBlock) {
            CommandBlock block = (CommandBlock) currentWorkItem;
            Command nextInBlock = block.nextCommand(currentCommand);
            if (nextInBlock != null) {
                currentCommand = nextInBlock;
                sendCommand(currentCommand.getCommandData());
            } else {
                // 命令块完成
                YoBTSDKLog.d(TAG, "命令块[" + block.getClass().getSimpleName() + "]执行完成");
                block.onBlockCompleted(blockResults);
                currentWorkItem = null;
                currentCommand = null;
                blockResults = null;
                
                // 检查队列是否变为空闲状态
                boolean isNowIdle = isQueueEmptyAndIdle();
                if (isNowIdle) {
                    YoBTSDKLog.d(TAG, "命令块完成后队列变为空闲，通知监听器");
                    notifyQueueStatusChanged(true);
                }
                
                mainHandler.postDelayed(this::executeNextCommand, COMMAND_INTERVAL);
            }
        } else if (currentWorkItem instanceof Command) {
            currentCommand = (Command) currentWorkItem;
            sendCommand(currentCommand.getCommandData());
        }
    }
    
    private void sendCommand(String commandData) {
        boolean wasIdle = isQueueEmptyAndIdle();
        isExecuting = true;
        currentCommand.setState(Command.State.SENDING);
        YoBTSDKLog.d(TAG, "正在发送命令: " + commandData);
        
        // 如果之前是空闲状态，现在变为忙碌状态，通知监听器
        if (wasIdle) {
            notifyQueueStatusChanged(false);
        }
        
        boolean sent = bleDataService.sendData(commandData);
        if (sent) {
            currentCommand.setState(Command.State.SENT);
            // 使用命令的自定义超时时间
            long timeoutMs = currentCommand.getTimeoutMs();
            mainHandler.postDelayed(this::handleCommandTimeout, timeoutMs);
        } else {
            notifyCommandFailed(Command.ResultCode.FAILED, "命令发送失败");
        }
    }
    
    private void handleCommandTimeout() {
        if (currentCommand != null && currentCommand.getState() == Command.State.SENT) {
            YoBTSDKLog.e(TAG, "命令执行超时: " + currentCommand.getCommandId());
            notifyCommandFailed(Command.ResultCode.TIMEOUT, "命令执行超时");
        }
    }

    private void notifyCommandFailed(int code, String reason) {
        isExecuting = false;
        mainHandler.removeCallbacksAndMessages(null);

        if (currentCommand != null) {
            currentCommand.setState(Command.State.FAILED);
            YoBTSDKLog.e(TAG, "命令执行失败: " + reason);

            // 命令失败后也需要清理该命令的回调
            cleanupCommandCallbacks(currentCommand);

            if (currentWorkItem instanceof CommandBlock) {
                // 命令块中的命令失败，检查是否是最后一个命令或者需要终止整个块
                CommandBlock block = (CommandBlock) currentWorkItem;
                Command nextCommand = block.nextCommand(currentCommand);
                
                if (nextCommand == null) {
                    // 如果没有下一个命令了，通知命令块失败
                    block.onBlockFailed(code, reason);
                    currentWorkItem = null;
                    currentCommand = null;
                    blockResults = null;
                    mainHandler.postDelayed(this::executeNextCommand, COMMAND_INTERVAL);
                } else {
                    // 继续执行下一个命令（如果有的话），使用CommandBlock的间隔
                    long interval = Math.max(block.getCommandInterval(), 30);
                    mainHandler.postDelayed(this::executeNextCommand, interval);
                }
            } else {
                // 独立的命令失败
                currentCommand.notifyCompletion(code, reason);
                // 清理引用
                currentWorkItem = null;
                currentCommand = null;
                blockResults = null;
                // 独立命令失败使用标准间隔
                mainHandler.postDelayed(this::executeNextCommand, COMMAND_INTERVAL);
            }
        } else {
            // 如果当前命令为null，使用标准间隔恢复
            mainHandler.postDelayed(this::executeNextCommand, COMMAND_INTERVAL);
        }
        
        // 检查队列状态变化，如果变为空闲状态，通知监听器
        boolean isNowIdle = isQueueEmptyAndIdle();
        if (isNowIdle) {
            notifyQueueStatusChanged(true);
        }
    }
    
    public void triggerExecution() {
        YoBTSDKLog.d(TAG, "收到执行触发，检查命令队列");
        executeNextCommand();
    }
    
    /**
     * 清理命令相关的回调
     * @param command 要清理回调的命令
     */
    private void cleanupCommandCallbacks(Command command) {
        if (command != null) {
            String commandId = command.getCommandId();
            
            // 特殊处理：对于聚合命令，不要过早清理回调
            // 聚合命令会在真正完成时自行通知回调并清理
            if (command instanceof GlobalStatusCommand) {
                GlobalStatusCommand globalCommand =
                    (GlobalStatusCommand) command;
                
                // 检查聚合命令是否真正完成
                if (globalCommand.getCurrentState() != null && 
                    globalCommand.getCurrentState() != 
                    GlobalStatusCommand.AggregationState.FINISHED) {
                    YoBTSDKLog.d(TAG, "GlobalStatusCommand[" + commandId + "]尚未完成聚合，保留回调不清理");
                    return;
                }
            }
            
            // 检查其他类型的聚合命令
            String className = command.getClass().getSimpleName();
            if (className.contains("Aggregator") || className.contains("聚合")) {
                YoBTSDKLog.d(TAG, "聚合类型命令[" + className + "][" + commandId + "]，谨慎清理回调");
                // 对于其他聚合命令，也可以考虑延迟清理或特殊处理
            }
            
            YoBTSDKLog.d(TAG, "清理命令回调，命令ID: " + commandId);
            callbackManager.removeCommandCallbacks(commandId);
        }
    }
    
    public void release() {
        mainHandler.removeCallbacksAndMessages(null);
        commandQueue.clear();
        currentWorkItem = null;
        currentCommand = null;
        isExecuting = false;
        isTemporarilyPaused = false; // 重置临时暂停状态
        bleDataService.unregisterCallback(this);
        // 释放回调管理器资源
        callbackManager.release();
        instance = null;
    }

    public void reset(){
        Log.d(TAG,"reset");
        currentWorkItem = null;
        currentCommand = null;
        isExecuting = false;
    }

    public void clearCommandQueue() {
        mainHandler.removeCallbacksAndMessages(null);
        commandQueue.clear();
        currentWorkItem = null;
        currentCommand = null;
        isExecuting = false;
        isTemporarilyPaused = false; // 重置临时暂停状态
    }
    
    @Override
    public void onDataSent(String data) {}
    
    @Override
    public void onDataReceived(String data) {
        if (data == null) return;
        
        YoBTSDKLog.d(TAG, "收到数据: " + data);
        
        if (currentCommand == null) {
            commandManager.handleResponse(data);
            return;
        }
        
        if (currentCommand.isResponseMatch(data)) {
            // 收到匹配的响应，清除超时定时器
            mainHandler.removeCallbacksAndMessages(null);
            currentCommand.setState(Command.State.RECEIVING);
            String result = currentCommand.parseResponse(data);
            YoBTSDKLog.i(TAG, "命令执行解析数据结果: " + result + "，命令是否接收结束: " + currentCommand.isCommandFinish());

            // 由命令自己决定是否已经处理结束
            if(currentCommand.isCommandFinish()){
                isExecuting = false;
                currentCommand.setState(Command.State.COMPLETED);
            }

            if (currentWorkItem instanceof CommandBlock && blockResults != null && currentCommand != null) {
                // 将单个命令的结果存入块的结果集
                blockResults.put(currentCommand.getClass().getSimpleName(), result);
            }

            // 命令完成后自动清理该命令的回调
            if (currentCommand != null && currentCommand.getState() == Command.State.COMPLETED) {
                cleanupCommandCallbacks(currentCommand);
            }

            if(currentWorkItem instanceof CommandBlock){
                // 对于命令块，保持currentWorkItem不被清理，直到整个块完成
                YoBTSDKLog.d(TAG, "命令块内的单个命令完成，继续执行块内下一个命令");
                // CommandBlock内的命令间隔，添加最小间隔保护
                long interval = Math.max(((CommandBlock) currentWorkItem).getCommandInterval(), 30);
                mainHandler.postDelayed(this::executeNextCommand, interval);
            } else {
                // 对于独立命令，检查是否真正完成
                if (currentCommand != null &&
                        (currentCommand.getState() == Command.State.COMPLETED ||
                                currentCommand.getState() == Command.State.FAILED)) {
                    // 命令真正完成，清空引用
                    currentWorkItem = null;
                    currentCommand = null;
                    YoBTSDKLog.d(TAG, "独立命令真正完成");
                    // 单个命令使用标准间隔
                    mainHandler.postDelayed(this::executeNextCommand, COMMAND_INTERVAL);
                } else {
                    // 聚合命令还在处理中，保持引用但清空workItem
                    currentWorkItem = null;
                    YoBTSDKLog.d(TAG, "聚合命令仍在处理中，保持命令引用");
                }
            }

            // 检查队列状态变化，如果变为空闲状态，通知监听器
            boolean isNowIdle = isQueueEmptyAndIdle();
            if (isNowIdle) {
                notifyQueueStatusChanged(true);
            }
        } else {
            // 收到不匹配的数据，交给命令管理器处理，但不重置超时定时器
            // 这样可以避免其他设备主动上报的数据干扰当前命令的超时计时
            // 这种情况通常是设备主动上报数据，属于正常现象
            YoBTSDKLog.d(TAG, "收到设备主动上报数据，交给管理器处理: " + data);
            commandManager.handleResponse(data);
            // 注意：这里不再重新设置超时定时器，保持原有的超时计时不被干扰
        }
    }
    
    @Override
    public void onError(String errorMsg) {
        YoBTSDKLog.e(TAG, "蓝牙数据错误: " + errorMsg);
        if (currentCommand != null) {
            notifyCommandFailed(Command.ResultCode.FAILED, errorMsg);
        }
    }

    /**
     * 临时暂停命令队列执行（用于电量监听启动延迟）
     * @param delayMs 暂停时间（毫秒）
     */
    public void pauseTemporarily(long delayMs) {
        YoBTSDKLog.d(TAG, "临时暂停命令队列执行，延迟: " + delayMs + "ms");
        isTemporarilyPaused = true;
        
        // 延迟后恢复执行
        mainHandler.postDelayed(() -> {
            YoBTSDKLog.d(TAG, "恢复命令队列执行");
            isTemporarilyPaused = false;
            executeNextCommand();
        }, delayMs);
    }
    
    /**
     * 检查是否临时暂停
     * @return true 如果临时暂停，false 如果没有暂停
     */
    public boolean isTemporarilyPaused() {
        return isTemporarilyPaused;
    }
    
    /**
     * 检查队列中是否存在特定类型的命令
     * @param commandClass 命令类型
     * @return true 如果存在，false 如果不存在
     */
    public boolean hasCommandOfType(Class<?> commandClass) {
        // 检查当前正在执行的命令
        if (currentCommand != null && commandClass.isInstance(currentCommand)) {
            Log.d(TAG,"commandClass.isInstance(currentCommand)");
            return true;
        }
        
        // 检查队列中等待的命令
        for (IQueueable item : commandQueue) {
            if (commandClass.isInstance(item)) {
                return true;
            }
            // 如果是命令块，检查其中的命令
            if (item instanceof CommandBlock) {
                // 这里暂时不深入检查命令块内部，因为命令块的内部命令是动态生成的
                // 可以根据需要扩展此功能
            }
        }
        
        return false;
    }
    
    /**
     * 检查队列中是否存在 GlobalStatusCommand
     * @return true 如果存在，false 如果不存在
     */
    public boolean hasGlobalStatusCommand() {
        return hasCommandOfType(GlobalStatusCommand.class);
    }

    /**
     * 检查命令队列是否为空且没有正在执行的命令
     * @return true 如果队列为空且没有正在执行的命令，false 如果有命令在队列中或正在执行
     */
    public boolean isQueueEmptyAndIdle() {
        // 关键修复：检查队列中是否有聚合命令等待执行
        if (!commandQueue.isEmpty()) {
            // 检查队列中是否有聚合命令或聚合命令块
            for (IQueueable item : commandQueue) {
                String itemName = item.getClass().getSimpleName();
                
                // 检查GlobalStatusCommand（聚合命令）
                if (item instanceof GlobalStatusCommand) {
                    YoBTSDKLog.d(TAG, "队列中有GlobalStatusCommand等待执行，队列不空闲");
                    return false;
                }
                
                // 检查电池信息聚合命令块
                if (item instanceof BatteryInfoAggregatorBlock) {
                    YoBTSDKLog.d(TAG, "队列中有BatteryInfoAggregatorBlock等待执行，队列不空闲");
                    return false;
                }
                
                // 检查关于设备页面聚合器
                if (item instanceof AboutDeviceStatusAggregator) {
                    YoBTSDKLog.d(TAG, "队列中有AboutDeviceStatusAggregator等待执行，队列不空闲");
                    return false;
                }
                
                // 对于其他可能的聚合类型，使用类名模式匹配
                if (itemName.contains("Aggregator") || itemName.contains("聚合")) {
                    YoBTSDKLog.d(TAG, "队列中有聚合类型命令[" + itemName + "]等待执行，队列不空闲");
                    return false;
                }
            }
            
            // 如果队列中有其他普通命令，也不算空闲
            YoBTSDKLog.d(TAG, "队列中有" + commandQueue.size() + "个命令等待执行");
            return false;
        }
        
        // 关键修复：如果有当前工作项（命令或命令块），说明不空闲
        if (currentWorkItem != null) {
            YoBTSDKLog.d(TAG, "当前工作项[" + currentWorkItem.getClass().getSimpleName() + "]仍在执行中");
            return false;
        }
        
        // 检查是否有命令正在执行
        if (isExecuting) {
            return false;
        }
        
        // 特殊检查：如果当前命令是聚合命令且仍在聚合中，则不算空闲
        if (currentCommand != null) {
            // 检查命令状态，如果不是COMPLETED或FAILED，说明还在处理中
            Command.State state = currentCommand.getState();
            if (state != Command.State.COMPLETED && state != Command.State.FAILED) {
                YoBTSDKLog.d(TAG, "当前命令[" + currentCommand.getCommandId() + "]仍在处理中，状态: " + state);
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 队列状态监听器接口
     */
    public interface QueueStatusListener {
        /**
         * 当队列变为空闲状态时回调
         */
        void onQueueBecameIdle();
        
        /**
         * 当队列开始执行命令时回调
         */
        void onQueueBecameBusy();
    }
    
    // 队列状态监听器列表
    private final java.util.List<QueueStatusListener> queueStatusListeners = new java.util.ArrayList<>();
    
    /**
     * 添加队列状态监听器
     * @param listener 监听器
     */
    public void addQueueStatusListener(QueueStatusListener listener) {
        if (listener != null && !queueStatusListeners.contains(listener)) {
            queueStatusListeners.add(listener);
            YoBTSDKLog.d(TAG, "添加队列状态监听器");
        }
    }
    
    /**
     * 移除队列状态监听器
     * @param listener 监听器
     */
    public void removeQueueStatusListener(QueueStatusListener listener) {
        if (listener != null) {
            queueStatusListeners.remove(listener);
            YoBTSDKLog.d(TAG, "移除队列状态监听器");
        }
    }
    
    /**
     * 通知队列状态变化
     * @param isIdle true 如果队列变为空闲，false 如果队列变为忙碌
     */
    private void notifyQueueStatusChanged(boolean isIdle) {
        for (QueueStatusListener listener : queueStatusListeners) {
            try {
                if (isIdle) {
                    listener.onQueueBecameIdle();
                } else {
                    listener.onQueueBecameBusy();
                }
            } catch (Exception e) {
                YoBTSDKLog.e(TAG, "通知队列状态监听器时出错: " + e.getMessage());
            }
        }
    }
    
    /**
     * 当聚合命令真正完成时被调用，用于清理当前命令引用
     */
    public void onCommandReallyCompleted() {
//        mainHandler.post(this::executeNextCommand);
//        if (currentCommand != null && currentCommand.equals(command)) {
            // 检查队列是否变为空闲状态
            boolean isNowIdle = isQueueEmptyAndIdle();
            if (isNowIdle) {
                YoBTSDKLog.d(TAG, "队列变为空闲状态，通知监听器");
                notifyQueueStatusChanged(true);
            }

            mainHandler.post(this::executeNextCommand);
//        }
    }
} 