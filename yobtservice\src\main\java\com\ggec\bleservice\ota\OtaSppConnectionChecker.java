package com.ggec.bleservice.ota;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;

import com.ggec.bleservice.YoCommandApi;
import com.ggec.bleservice.setting.Command;
import com.ggec.bleservice.setting.devicecmd.ClassicBtAddressCommand;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;
import com.ggec.sppservice.check.YoSPPMacDeviceChecker;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * OTA SPP连接检测器
 * 专门用于检测设备是否连接经典蓝牙SPP
 */
public class OtaSppConnectionChecker {

    private static final String TAG = "OtaSppConnectionChecker";
    
    // 增加命令超时时间，给予更充足的等待时间
    private static final long COMMAND_TIMEOUT = 3000; // 3秒

    // 主线程Handler
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    // 上下文
    private final Context context;
    
    // SPP MAC设备检查器
    private final YoSPPMacDeviceChecker sppMacDeviceChecker;
    
    // 单例实例
    private static OtaSppConnectionChecker instance;
    
    /**
     * 获取单例实例
     * @param context 应用上下文
     * @return OtaSppConnectionChecker实例
     */
    public static synchronized OtaSppConnectionChecker getInstance(Context context) {
        if (instance == null) {
            instance = new OtaSppConnectionChecker(context.getApplicationContext());
        }
        return instance;
    }
    
    /**
     * 私有构造方法
     * @param context 应用上下文
     */
    private OtaSppConnectionChecker(Context context) {
        this.context = context.getApplicationContext();
        this.sppMacDeviceChecker = YoSPPMacDeviceChecker.getInstance(context);
        YoBTSDKLog.d(TAG, "OtaSppConnectionChecker已初始化");
    }

    /**
     * 检查设备是否连接经典蓝牙SPP
     * 首先获取耳机的经典蓝牙地址，然后检查是否已连接到该地址
     * @return 是否已连接经典蓝牙SPP
     */
    public boolean checkSppConnection() {
        YoBTSDKLog.d(TAG, "开始检查经典蓝牙SPP连接状态");

        // 1. 获取耳机经典蓝牙地址
        String deviceMacAddress = getClassicBtAddress();
        if (deviceMacAddress == null || deviceMacAddress.isEmpty()) {
            YoBTSDKLog.e(TAG, "获取耳机经典蓝牙地址失败");
            return false;
        }
        
        YoBTSDKLog.d(TAG, "获取到耳机经典蓝牙地址: " + deviceMacAddress);
        
        // 2. 获取当前连接的经典蓝牙设备MAC地址
        YoSPPMacDeviceChecker.DeviceConnectionResult result = sppMacDeviceChecker.checkConnectedDevice();
        if (!result.isConnected() || result.getMacAddress() == null) {
            YoBTSDKLog.d(TAG, "当前没有连接经典蓝牙设备");
            return false;
        }
        
        String connectedMacAddress = result.getMacAddress();
        YoBTSDKLog.d(TAG, "正在连接的蓝牙设备MAC地址: " + connectedMacAddress);
        
        // 3. 比较两个MAC地址是否一致
        boolean isMatched = deviceMacAddress.equalsIgnoreCase(connectedMacAddress);
        
        YoBTSDKLog.d(TAG, "SPP连接检查结果: " + isMatched + 
                " (目标OTA对象MAC=" + deviceMacAddress + 
                ", 正在连接的设备MAC=" + connectedMacAddress + ")");
        
        return isMatched;
    }
    
    /**
     * 获取经典蓝牙MAC地址
     * @return 经典蓝牙MAC地址，获取失败返回null
     */
    private String getClassicBtAddress() {
        final Object lock = new Object();
        final boolean[] completed = {false};
        final String[] macAddress = {null};
        final int[] retryCount = {0};
        final int MAX_RETRIES = 2;
        
        YoBTSDKLog.d(TAG, "开始获取经典蓝牙地址");
        
        do {
            completed[0] = false;
            macAddress[0] = null;
            
            // 使用ClassicBtAddressCommand获取经典蓝牙地址
            YoCommandApi.getInstance().getDeviceMacAddress(new YoCommandApi.ClassicBtAddressCallback() {
                @Override
                public void onClassicBtAddressResult(int code, String address) {
                    if (code == Command.ResultCode.SUCCESS && address != null && !address.isEmpty()) {
                        // 从响应结果中提取MAC地址（通常是格式为"经典蓝牙MAC地址: XX:XX:XX:XX:XX:XX"的字符串）
                        String[] parts = address.split(":");
                        if (parts.length >= 2) {
                            // 提取最后6个部分组成MAC地址
                            StringBuilder sb = new StringBuilder();
                            for (int i = parts.length - 6; i < parts.length; i++) {
                                if (i > parts.length - 6) {
                                    sb.append(":");
                                }
                                sb.append(parts[i].trim());
                            }
                            macAddress[0] = sb.toString();
                        } else {
                            // 如果不符合预期格式，直接使用原始地址
                            macAddress[0] = address;
                        }
                    }
                    
                    completed[0] = true;
                    synchronized (lock) {
                        lock.notify();
                    }
                }
            });
            
            // 等待获取完成，延长等待时间
            synchronized (lock) {
                if (!completed[0]) {
                    try {
                        YoBTSDKLog.d(TAG, "等待MAC地址获取完成，尝试次数: " + (retryCount[0] + 1));
                        lock.wait(COMMAND_TIMEOUT);
                    } catch (InterruptedException e) {
                        // 忽略中断异常
                        YoBTSDKLog.e(TAG, "获取经典蓝牙地址时被中断", e);
                    }
                }
            }
            
            // 如果获取成功，跳出循环
            if (macAddress[0] != null) {
                    break;
            }
            
            // 增加重试计数
            retryCount[0]++;
            
        } while (retryCount[0] < MAX_RETRIES);
        
        if (macAddress[0] != null) {
            YoBTSDKLog.d(TAG, "获取到目标OTA对象的MAC地址: " + macAddress[0]);
        } else {
            YoBTSDKLog.e(TAG, "未能获取目标OTA对象的MAC地址");
        }
        
        return macAddress[0];
    }
    
    /**
     * SPP连接状态回调接口
     */
    public interface SppConnectionCallback {
        /**
         * SPP连接状态检查结果回调
         * @param connected 是否已连接到设备的经典蓝牙
         * @param deviceMacAddress 设备的经典蓝牙MAC地址，如果获取失败则为null
         * @param connectedMacAddress 当前连接的经典蓝牙MAC地址，如果未连接则为null
         */
        void onSppConnectionResult(boolean connected, String deviceMacAddress, String connectedMacAddress);
    }
    
    /**
     * 异步检查设备是否连接经典蓝牙SPP
     * @param callback 连接状态回调
     */
    public void checkSppConnectionAsync(final SppConnectionCallback callback) {
        if (callback == null) {
            return;
        }
        
        new Thread(() -> {
            try {
                // 1. 获取耳机经典蓝牙地址
                final String deviceMacAddress = getClassicBtAddress();
                if (deviceMacAddress == null || deviceMacAddress.isEmpty()) {
                    YoBTSDKLog.e(TAG, "未能获取目标OTA对象的MAC地址");
                    mainHandler.post(() -> callback.onSppConnectionResult(false, null, null));
                    return;
                }
                
                // 2. 获取当前连接的经典蓝牙设备MAC地址
                YoSPPMacDeviceChecker.DeviceConnectionResult result = sppMacDeviceChecker.checkConnectedDevice();
                final String connectedMacAddress = result.isConnected() ? result.getMacAddress() : null;
                
                if (connectedMacAddress == null) {
                    YoBTSDKLog.d(TAG, "当前没有连接经典蓝牙设备");
                    mainHandler.post(() -> callback.onSppConnectionResult(false, deviceMacAddress, null));
                    return;
                }
                
                // 3. 比较两个MAC地址是否一致
                final boolean isMatched = deviceMacAddress.equalsIgnoreCase(connectedMacAddress);
                
                // 4. 回调结果
                mainHandler.post(() -> callback.onSppConnectionResult(isMatched, deviceMacAddress, connectedMacAddress));
                
            } catch (Exception e) {
                YoBTSDKLog.e(TAG, "异步检查SPP连接异常", e);
                mainHandler.post(() -> callback.onSppConnectionResult(false, null, null));
            }
        }).start();
    }
    
    /**
     * 释放资源
     */
    public void release() {
        instance = null;
    }
} 