package com.ggec.bleservice.setting.aggregatorcmd;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import com.ggec.bleservice.YoBLEApi;
import com.ggec.bleservice.YoCommandApi;
import com.ggec.bleservice.YoCommandApi.CommandResultCode;
import com.ggec.bleservice.setting.CommandQueueCenter;
import com.ggec.bleservice.setting.CommandCallbackManager;
import com.ggec.bleservice.setting.Command;
import com.ggec.bleservice.setting.devicecmd.GlobalStatusCommand;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 首页状态聚合器 (V9 - 修复回调注册和超时问题)
 *
 * 通过直接创建和监听 GlobalStatusCommand 来获取首页所需的所有状态。
 * 使用命令特定回调避免ID匹配问题，优化超时处理机制。
 */
public class HomePageStatusAggregator {
    private static final String TAG = "HomePageStatus";
    
    // 聚合超时时间（毫秒）- 增加到5秒以应对网络延迟
    private static final long AGGREGATION_TIMEOUT_MS = 5000;
    
    // 主线程Handler，用于超时保护
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());

    private final CommandQueueCenter commandQueueCenter;

    public HomePageStatusAggregator(YoCommandApi commandApi) {
        this.commandQueueCenter = CommandQueueCenter.getInstance();
    }

    /**
     * 开始获取所有状态
     * @param finalCallback 上层调用者提供的最终回调，它将被包装成一次性回调。
     */
    public void fetchStatus(YoCommandApi.HomePageStatusCallback finalCallback) {
        // 检查设备是否就绪，如果未就绪则直接返回失败
        if (!YoBLEApi.getInstance().isDeviceReady()) {
            Log.w(TAG, "设备未就绪，无法执行聚合命令。");
            if (finalCallback != null) {
                finalCallback.onHomePageStatusResult(CommandResultCode.FAILED, new LinkedHashMap<>());
            }
            return;
        }

        if (finalCallback == null) {
            Log.w(TAG, "未提供回调，取消任务。");
            return;
        }
        
        // 防抖处理：检查队列中是否已有GlobalStatusCommand，如果有则直接拒绝
        if (commandQueueCenter.hasGlobalStatusCommand()) {
            Log.w(TAG, "队列中已存在GlobalStatusCommand，本次请求被拒绝（防抖处理）");
            finalCallback.onHomePageStatusResult(CommandResultCode.FAILED, new LinkedHashMap<>());
            return;
        }
        
        Log.d(TAG, "开始获取首页聚合状态...");

        // 创建一个一次性的回调包装器，带超时保护
        OneShotCallback oneShotCallback = new OneShotCallback(finalCallback);

        try {
            // 直接创建GlobalStatusCommand并注册回调
            GlobalStatusCommand command = new GlobalStatusCommand();
            
            // 使用命令特定回调，避免ID匹配问题
            command.registerOneTimeCallback(new Command.CommandCallback() {
                @Override
                public void onCommandCompleted(Command cmd, int code, String result) {
                    Log.d(TAG, "收到GlobalStatusCommand回调，状态码: " + code + ", 结果: " + result);
                    handleCommandResult(oneShotCallback, code, null, result);
                }
                
                @Override
                public void onCommandCompleted(Command cmd, int code, Object yobackdata, String result) {
                    Log.d(TAG, "收到GlobalStatusCommand回调（带数据），状态码: " + code + ", 数据: " + (yobackdata != null) + ", 结果: " + result);
                    handleCommandResult(oneShotCallback, code, yobackdata, result);
                }
                
                @Override
                public void onCommandCompleted(Command cmd, int code, int resultValue, String result) {
                    Log.d(TAG, "收到GlobalStatusCommand回调（带结果值），状态码: " + code + ", 结果值: " + resultValue + ", 结果: " + result);
                    handleCommandResult(oneShotCallback, code, null, result);
                }
            });
            
            // 将命令加入队列执行
            commandQueueCenter.enqueueCommand(command);
            
        } catch (Exception e) {
            Log.e(TAG, "启动聚合命令时发生异常", e); 
            oneShotCallback.onResult(CommandResultCode.FAILED, new LinkedHashMap<>());
        }
    }
    
    /**
     * 处理命令完成结果
     */
    private void handleCommandResult(OneShotCallback oneShotCallback, int code, Object yobackdata, String result) {
        try {
            // 忽略中间状态的通知
            if (result != null && result.contains("数据块聚合中")) {
                Log.d(TAG, "忽略中间状态通知: " + result);
                return;
            }
            
            // 只有当命令成功且返回了数据时，才认为是有效回调
            if (code == CommandResultCode.SUCCESS) {
                if (yobackdata != null && yobackdata instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, String> statusMap = (Map<String, String>) yobackdata;
                    
                    if (!statusMap.isEmpty()) {
                        Log.i(TAG, "底层聚合命令完成，结果状态: " + code + ", 数据项数: " + statusMap.size());
                        oneShotCallback.onResult(code, statusMap);
                    } else {
                        Log.w(TAG, "聚合命令成功但返回空数据，视为失败");
                        oneShotCallback.onResult(CommandResultCode.FAILED, new LinkedHashMap<>());
                    }
                } else {
                    Log.w(TAG, "聚合命令成功但数据格式不正确，视为失败");
                    oneShotCallback.onResult(CommandResultCode.FAILED, new LinkedHashMap<>());
                }
            } else {
                // 如果是失败/超时等情况，也需要通知上层
                Log.w(TAG, "底层聚合命令执行失败或超时，状态码: " + code);
                oneShotCallback.onResult(code, new LinkedHashMap<>());
            }
        } catch (Exception e) {
            Log.e(TAG, "处理聚合回调时发生异常", e);
            oneShotCallback.onResult(CommandResultCode.FAILED, new LinkedHashMap<>());
        }
    }

    /**
     * 一个辅助内部类，用于确保回调逻辑只被执行一次，并提供超时保护。
     */
    private static class OneShotCallback {
        private volatile YoCommandApi.HomePageStatusCallback originalCallback;
        private final AtomicBoolean hasBeenCalled = new AtomicBoolean(false);
        private final Runnable timeoutTask;
        
        OneShotCallback(YoCommandApi.HomePageStatusCallback callback) {
            this.originalCallback = callback;
            
            // 创建超时保护任务
            this.timeoutTask = () -> {
                Log.e(TAG, "聚合操作超时，强制执行回调");
                onResult(CommandResultCode.TIMEOUT, new LinkedHashMap<>());
            };
            
            // 启动超时保护
            mainHandler.postDelayed(timeoutTask, AGGREGATION_TIMEOUT_MS);
        }
        
        void onResult(int code, Map<String, String> statusMap) {
            // 通过原子操作确保只执行一次
            if (!hasBeenCalled.compareAndSet(false, true)) {
                Log.d(TAG, "聚合回调已被处理过，忽略重复调用");
                return;
            }
            
            // 取消超时任务
            mainHandler.removeCallbacks(timeoutTask);
            
            try {
                YoCommandApi.HomePageStatusCallback callback = originalCallback;
                if (callback != null) {
                    Map<String, String> resultMap = (statusMap != null) ? new LinkedHashMap<>(statusMap) : new LinkedHashMap<>();
                    callback.onHomePageStatusResult(code, resultMap);
                    Log.d(TAG, "聚合任务完成，结果已通知上层");
                } else {
                    Log.w(TAG, "回调为null，可能已被清理");
                }
            } catch (Exception e) {
                Log.e(TAG, "执行聚合回调时发生异常", e);
            } finally {
                // 清理回调引用
                this.originalCallback = null;
            }
        }
    }
} 