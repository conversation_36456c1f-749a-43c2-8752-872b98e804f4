package com.bes.bessdk.utils;

import java.nio.ByteBuffer;

public class CmdInfo {

    public static final int  HEAD_LEN = 4 ;
    short cmdType ;
    short dataLen ;
    byte[] extData ;

    public CmdInfo(short cmdType , byte[] extData){
        if(extData != null){
            dataLen = (short) extData.length ;
        }
        this.extData = extData ;
        this.cmdType = cmdType;
    }

    public short getCmdType() {
        return cmdType;
    }

    public void setCmdType(short cmdType) {
        this.cmdType = cmdType;
    }

    public short getDataLen() {
        return dataLen;
    }

    public void setDataLen(short dataLen) {
        this.dataLen = dataLen;
    }

    public byte[] getExtData() {
        return extData;
    }

    public void setExtData(byte[] extData) {
        this.extData = extData;
    }

    public byte[] toBytes() {
        if(cmdType != 0 && dataLen != 0 && extData != null){
            int totalLen = 2 + 2 + extData.length ;
            ByteBuffer byteBuffer = ByteBuffer.allocate(totalLen);
            byteBuffer.put((byte)(cmdType & 0xff));
            byteBuffer.put((byte)((cmdType >> 8 )& 0xff));
            byteBuffer.put((byte)(extData.length & 0xff));
            byteBuffer.put((byte)((extData.length >> 8 )& 0xff));
            byteBuffer.put(extData);
            return byteBuffer.array();
        } else if (cmdType != 0 && dataLen == 0){
            int totalLen = 2 + 2 ;
            ByteBuffer byteBuffer = ByteBuffer.allocate(totalLen);
            byteBuffer.put((byte)(cmdType & 0xff));
            byteBuffer.put((byte)((cmdType >> 8 )& 0xff));
            byteBuffer.put((byte)(dataLen & 0xff));
            byteBuffer.put((byte)((dataLen >> 8 )& 0xff));
            return byteBuffer.array();
        }
        return  null ;
    }

}
