package com.ggec.hs01.activity;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.Toast;

import android.util.Log;
import android.util.TypedValue;
import android.graphics.Typeface;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;

import com.ggec.hs01.R;
import com.ggec.bleservice.YoBLEApi;
import com.ggec.bleservice.YoCommandApi;
import com.ggec.hs01.GGECHSApplication;
import com.ggec.hs01.view.HMButton;
import com.ggec.hs01.fragment.ControlTabFragment;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;
import android.content.Intent;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 控制设置界面
 * 允许用户自定义耳机的按键控制行为
 */
public class ControlSettingsActivity extends AppCompatActivity {
    private static final String TAG = "ControlSettingsActivity";
    
    // UI组件
    private TabLayout tabLayoutControls;
    private ViewPager2 viewPagerControls;
    private HMButton btnResetControls;
    private HMButton btnGotoTest;
    
    // YoBLEApi 实例
    private YoBLEApi bleApi;
    
    // YoCommandApi 实例
    private YoCommandApi commandApi;
    
    // 标签页标题
    private final String[] tabTitles = {"左耳双击", "右耳双击", "左耳三击", "右耳三击"};

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_control_settings);
        
        // 获取YoBLEApi实例
        bleApi = ((GGECHSApplication) getApplication()).getBleApi();
        
        // 获取YoCommandApi实例
        commandApi = ((GGECHSApplication) getApplication()).getCommandApi();
        
        // 初始化返回按钮
        initBackButton();
        
        // 初始化标签页
        initTabs();
        
        // 初始化重置按钮
        initResetButton();
        
        // 初始化跳转按钮
        initGotoTestButton();
        
        // 设置内容区域适应系统UI，保留内边距防止内容被状态栏遮挡
//        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.control_settings_main), (v, insets) -> {
//            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
//            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
//            return insets;
//        });
    }
    
    @Override
    protected void onStart() {
        super.onStart();
        
        // 确保已连接设备，如果没有则返回连接页面
        if (!bleApi.isDeviceConnected()) {
            Toast.makeText(this, "请先连接设备", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        
        // 清理全局回调，防止污染其他Activity的命令执行
        if (commandApi != null) {
            commandApi.clearAllGlobalCallbacks();
            Log.d(TAG, "已清理ControlSettingsActivity的全局回调");
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        // 确保Activity销毁时清理所有回调
        if (commandApi != null) {
            commandApi.clearAllGlobalCallbacks();
            Log.d(TAG, "Activity销毁时已清理全局回调");
        }
    }
    
    /**
     * 初始化返回按钮
     */
    private void initBackButton() {
        ImageView btnBack = findViewById(R.id.btn_back);
        btnBack.setOnClickListener(v -> finish());
    }
    
    /**
     * 初始化标签页
     */
    private void initTabs() {
        tabLayoutControls = findViewById(R.id.tab_layout_controls);
        viewPagerControls = findViewById(R.id.view_pager_controls);
        
        // 创建ViewPager适配器
        ControlTabsAdapter adapter = new ControlTabsAdapter(this);
        viewPagerControls.setAdapter(adapter);
        
        // 将TabLayout与ViewPager连接
        new TabLayoutMediator(tabLayoutControls, viewPagerControls,
                (tab, position) -> tab.setText(tabTitles[position])
        ).attach();
        
        // 添加选择监听器来处理标签页样式变化
        tabLayoutControls.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                // 设置选中标签页的样式：加粗+增加2sp
                TextView tabView = (TextView) tab.getCustomView();
                if (tabView == null) {
                    // 如果没有自定义视图，获取默认的TextView
                    tabView = getTabTextView(tab);
                }
                if (tabView != null) {
                    tabView.setTypeface(Typeface.DEFAULT_BOLD);
                    tabView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16); // 14sp + 2sp = 16sp
                }
            }
            
            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                // 恢复未选中标签页的样式：正常字体
                TextView tabView = (TextView) tab.getCustomView();
                if (tabView == null) {
                    tabView = getTabTextView(tab);
                }
                if (tabView != null) {
                    tabView.setTypeface(Typeface.DEFAULT);
                    tabView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14); // 默认14sp
                }
            }
            
            @Override
            public void onTabReselected(TabLayout.Tab tab) {
                // 重选时保持选中样式
            }
        });
        
        // 设置初始选中状态的样式
        tabLayoutControls.post(() -> {
            TabLayout.Tab selectedTab = tabLayoutControls.getTabAt(tabLayoutControls.getSelectedTabPosition());
            if (selectedTab != null) {
                TextView tabView = getTabTextView(selectedTab);
                if (tabView != null) {
                    tabView.setTypeface(Typeface.DEFAULT_BOLD);
                    tabView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
                }
            }
        });
    }
    
    /**
     * 获取Tab的TextView
     * @param tab Tab对象
     * @return TextView或null
     */
    private TextView getTabTextView(TabLayout.Tab tab) {
        if (tab.view != null && tab.view.getChildCount() > 1) {
            android.view.View tabTextView = tab.view.getChildAt(1);
            if (tabTextView instanceof TextView) {
                return (TextView) tabTextView;
            }
        }
        return null;
    }
    
    /**
     * 初始化重置按钮
     */
    private void initResetButton() {
        btnResetControls = findViewById(R.id.btn_reset_controls);
        btnResetControls.setOnClickListener(v -> showResetConfirmDialog());
    }

    private void initGotoTestButton() {
        btnGotoTest = findViewById(R.id.btn_goto_test);
        btnGotoTest.setOnClickListener(v -> {
            Intent intent = new Intent(ControlSettingsActivity.this, ControlTestActivity.class);
            startActivity(intent);
        });
    }
    
    /**
     * 显示重置确认对话框
     */
    private void showResetConfirmDialog() {
        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("恢复默认设置")
                .setMessage("确定要恢复所有控制设置为默认值吗？")
                .setPositiveButton("确定", (dialog, which) -> resetToDefaults())
                .setNegativeButton("取消", null)
                .setIcon(android.R.drawable.ic_dialog_alert)
                .show();
    }
    
    /**
     * 重置为默认设置
     */
    private void resetToDefaults() {
        // 检查设备连接状态
        if (!bleApi.isDeviceConnected()) {
            Toast.makeText(this, "请先连接设备", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 重置所有标签页为默认选项
        ControlTabsAdapter adapter = (ControlTabsAdapter) viewPagerControls.getAdapter();
        if (adapter != null) {
            // 重置每个Fragment的选择
            for (int i = 0; i < tabTitles.length; i++) {
                ControlTabFragment fragment = adapter.getFragment(i);
                if (fragment != null) {
                    fragment.loadDefaultSelection();
                }
            }
        }
        
        // 发送默认控制设置命令
        sendDefaultControlCommands();
    }
    
    /**
     * 发送默认控制设置命令
     */
    private void sendDefaultControlCommands() {
        // 使用统一的恢复默认控制设置命令
        commandApi.resetControlSettings((code, resultValue) -> {
            runOnUiThread(() -> {
                String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? 
                        "恢复默认设置成功" : "恢复默认设置失败";
                Toast.makeText(ControlSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
            });
        });
    }
    
    /**
     * ViewPager适配器，用于管理标签页Fragment
     */
    private static class ControlTabsAdapter extends FragmentStateAdapter {
        private final ControlTabFragment[] fragments = new ControlTabFragment[4];
        
        public ControlTabsAdapter(@NonNull FragmentActivity fragmentActivity) {
            super(fragmentActivity);
        }
        
        @NonNull
        @Override
        public Fragment createFragment(int position) {
            ControlTabFragment fragment = ControlTabFragment.newInstance(position);
            fragments[position] = fragment;
            return fragment;
        }
        
        @Override
        public int getItemCount() {
            return 4; // 四个标签页
        }
        
        /**
         * 获取指定位置的Fragment
         * @param position 位置
         * @return Fragment实例
         */
        public ControlTabFragment getFragment(int position) {
            if (position >= 0 && position < fragments.length) {
                return fragments[position];
            }
            return null;
        }
    }
}