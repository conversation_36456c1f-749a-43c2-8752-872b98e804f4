package com.ggec.sppservice.scanner;

import android.bluetooth.BluetoothDevice;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * SPP扫描回调接口，用于通知SPP设备扫描事件
 */
public interface SppScanCallback {
    /**
     * 发现SPP设备回调
     * @param device 蓝牙设备
     * @param rssi 信号强度
     */
    void onDeviceFound(BluetoothDevice device, int rssi);
    
    /**
     * 扫描开始回调
     */
    void onScanStart();
    
    /**
     * 扫描结束回调
     */
    void onScanFinish();
} 