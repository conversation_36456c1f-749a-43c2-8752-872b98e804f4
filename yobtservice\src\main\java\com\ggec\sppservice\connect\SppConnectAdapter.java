package com.ggec.sppservice.connect;

import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;

/** 
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * SPP连接回调适配器
 * 提供SppConnectCallback接口的空实现，方便使用者只实现关心的回调方法
 */
public class SppConnectAdapter implements SppConnectCallback {

    @Override
    public void onConnectStart(BluetoothDevice device) {
        // 空实现
    }

    @Override
    public void onPairingRequest(BluetoothDevice device) {
        // 空实现
    }

    @Override
    public void onPaired(BluetoothDevice device) {
        // 空实现
    }

    @Override
    public void onConnected(BluetoothDevice device, BluetoothSocket socket) {
        // 空实现
    }

    @Override
    public void onConnectFailed(BluetoothDevice device, String errorMsg) {
        // 空实现
    }

    @Override
    public void onDisconnected(BluetoothDevice device) {
        // 空实现
    }
} 