package com.ggec.sppservice.scanner;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * SPP扫描器接口，定义SPP扫描功能的基本行为
 */
public interface ISppScanner {
    /**
     * 设置扫描配置
     * @param config SPP扫描配置
     */
    void setScanConfig(SppScanConfig config);
    
    /**
     * 开始扫描SPP设备
     * @param callback 扫描回调
     */
    void startScan(SppScanCallback callback);
    
    /**
     * 停止扫描
     */
    void stopScan();
    
    /**
     * 释放资源
     */
    void close();
} 