package com.ggec.yobtsdkserver.utils;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 十六进制工具类
 * 提供十六进制字符串与字节数组之间的转换
 */
public class HexUtil {
    
    private static final char[] HEX_CHARS = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
    
    /**
     * 将十六进制字符串转换为字节数组
     * @param hexString 十六进制字符串，可以包含空格
     * @return 字节数组
     */
    public static byte[] hexStringToBytes(String hexString) {
        if (hexString == null || hexString.length() == 0) {
            return new byte[0];
        }
        
        // 移除所有空格
        hexString = hexString.replaceAll("\\s", "");
        
        // 确保字符串长度是偶数
        if (hexString.length() % 2 != 0) {
            hexString = "0" + hexString;
        }
        
        int length = hexString.length() / 2;
        byte[] bytes = new byte[length];
        
        for (int i = 0; i < length; i++) {
            int high = Character.digit(hexString.charAt(i * 2), 16);
            int low = Character.digit(hexString.charAt(i * 2 + 1), 16);
            
            if (high == -1 || low == -1) {
                throw new IllegalArgumentException("包含非法的十六进制字符: " + hexString);
            }
            
            bytes[i] = (byte) ((high << 4) | low);
        }
        
        return bytes;
    }
    
    /**
     * 将字节数组转换为十六进制字符串
     * @param bytes 字节数组
     * @return 十六进制字符串，每个字节由两个字符表示
     */
    public static String bytesToHexString(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }
        
        char[] chars = new char[bytes.length * 2];
        
        for (int i = 0; i < bytes.length; i++) {
            int value = bytes[i] & 0xFF;
            chars[i * 2] = HEX_CHARS[value >>> 4];
            chars[i * 2 + 1] = HEX_CHARS[value & 0x0F];
        }
        
        return new String(chars);
    }
    
    /**
     * 将字节数组转换为带空格分隔的十六进制字符串
     * @param bytes 字节数组
     * @return 带空格分隔的十六进制字符串
     */
    public static String bytesToHexStringWithSpace(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() == 1) {
                sb.append('0');
            }
            sb.append(hex.toUpperCase());
            
            if (i < bytes.length - 1) {
                sb.append(' ');
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 打印字节数组的十六进制表示（用于调试）
     * @param bytes 字节数组
     * @return 带前缀和索引的格式化十六进制字符串
     */
    public static String formatBytesForLog(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "Empty byte array";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("Hex dump [").append(bytes.length).append(" bytes]:\n");
        
        for (int i = 0; i < bytes.length; i += 16) {
            sb.append(String.format("%04X: ", i));
            
            for (int j = 0; j < 16; j++) {
                int index = i + j;
                if (index < bytes.length) {
                    sb.append(String.format("%02X ", bytes[index]));
                } else {
                    sb.append("   ");
                }
                
                if (j == 7) {
                    sb.append(" ");
                }
            }
            
            sb.append(" ");
            
            for (int j = 0; j < 16; j++) {
                int index = i + j;
                if (index < bytes.length) {
                    char c = (char) bytes[index];
                    if (c >= 32 && c < 127) {
                        sb.append(c);
                    } else {
                        sb.append('.');
                    }
                }
            }
            
            sb.append("\n");
        }
        
        return sb.toString();
    }
} 