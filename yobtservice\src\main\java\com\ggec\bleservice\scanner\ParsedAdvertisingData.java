package com.ggec.bleservice.scanner;

import java.util.UUID;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-17
 * Description:     
 * 解析后的BLE广播数据。
 * 包含从设备广播中解析出的关键信息。
 */
public class ParsedAdvertisingData {
    private final String featureCode;
    private final String color;
    private final String macAddress;
    private final UUID serviceUuid;

    public ParsedAdvertisingData(String featureCode, String color, String macAddress, UUID serviceUuid) {
        this.featureCode = featureCode;
        this.color = color;
        this.macAddress = macAddress;
        this.serviceUuid = serviceUuid;
    }

    public String getFeatureCode() {
        return featureCode;
    }

    public String getColor() {
        return color;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public UUID getServiceUuid() {
        return serviceUuid;
    }

    @Override
    public String toString() {
        return "ParsedAdvertisingData{" +
                "macAddress='" + macAddress + '\'' +
                ", color='" + color + '\'' +
                ", featureCode='" + featureCode + '\'' +
                ", serviceUuid=" + serviceUuid +
                '}';
    }
} 