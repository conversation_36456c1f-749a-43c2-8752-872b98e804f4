<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/comp_background_gray"
    android:fitsSystemWindows="false"
    tools:context=".activity.MainActivity">

    <!-- 按钮水平排列容器 -->
    <LinearLayout
        android:id="@+id/layout_buttons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 蓝牙连接按钮 -->
        <com.ggec.hs01.view.HMButton
            android:id="@+id/btn_custom"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="蓝牙连接"
            android:layout_marginEnd="8dp" />

        <!-- 电池监听开关 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp">
            
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="自动监听"
                android:textColor="@android:color/black"
                android:textSize="14sp"
                android:layout_marginEnd="6dp" />
                
            <com.ggec.hs01.view.HMSwitch
                android:id="@+id/switch_battery_monitor"
                android:layout_width="40dp"
                android:layout_height="20dp"/>
        </LinearLayout>
            
        <!-- 获取状态按钮 -->
        <com.ggec.hs01.view.HMButton
            android:id="@+id/btn_get_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="获取状态"
            android:layout_marginStart="8dp" />
    </LinearLayout>

    <!-- 蓝牙连接状态和电量显示区域 -->
    <LinearLayout
        android:id="@+id/layout_bluetooth_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:layout_marginTop="8dp"
        android:background="@color/comp_background_gray"
        app:layout_constraintTop_toBottomOf="@+id/layout_buttons">
        
        <TextView
            android:id="@+id/tv_bluetooth_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="未连接"
            android:textColor="@android:color/black"
            android:textSize="13sp" />
        
        <TextView
            android:id="@+id/tv_case_charging_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=""
            android:textColor="@android:color/holo_green_dark"
            android:textSize="16sp" />
    </LinearLayout>
    
    <!-- 电池电量显示区域 - 左耳、右耳、盒子 -->
    <LinearLayout
        android:id="@+id/layout_battery_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:layout_marginTop="8dp"
        android:background="@color/comp_background_gray"
        app:layout_constraintTop_toBottomOf="@+id/layout_bluetooth_status">
        
        <!-- 左耳电量 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">   
            
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="左耳"
                android:textColor="@android:color/black"
                android:textSize="16sp" />
                
            <TextView
                android:id="@+id/tv_battery_left"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="--%" 
                android:textColor="@android:color/black"
                android:textSize="24sp"
                android:textStyle="bold"
                android:layout_marginTop="8dp" />
        </LinearLayout>
        
        <!-- 右耳电量 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">
            
        <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="右耳"
                android:textColor="@android:color/black"
                android:textSize="16sp" />
                
            <TextView
                android:id="@+id/tv_battery_right"
            android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="--%" 
                android:textColor="@android:color/black"
                android:textSize="24sp"
                android:textStyle="bold"
                android:layout_marginTop="8dp" />
        </LinearLayout>
        
        <!-- 盒子电量 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">
            
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="盒子"
            android:textColor="@android:color/black"
            android:textSize="16sp" />
                
            <TextView
                android:id="@+id/tv_battery_case"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="--%" 
                android:textColor="@android:color/black"
                android:textSize="24sp"
                android:textStyle="bold"
                android:layout_marginTop="8dp" />
        </LinearLayout>
    </LinearLayout>
    
    <!-- vidda2图片视图 -->
    <ImageView
        android:id="@+id/iv_vidda2"
        android:layout_width="match_parent"
        android:layout_height="230dp"
        android:layout_marginTop="16dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:src="@drawable/vidda2"
        android:scaleType="fitCenter"
        android:contentDescription="vidda2图片"
        app:layout_constraintTop_toBottomOf="@+id/layout_battery_status" />
    
    <!-- 四个卡片的网格布局 -->
    <GridLayout
        android:id="@+id/grid_settings"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:columnCount="2"
        android:rowCount="2"
        app:layout_constraintTop_toBottomOf="@+id/iv_vidda2"
        app:layout_constraintBottom_toBottomOf="parent">
        
        <!-- 音乐设置卡片 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_music_settings"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="8dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">
            
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="音乐设置"
                android:textSize="18sp"
                android:textColor="@android:color/black"
                android:layout_gravity="center" />
        </androidx.cardview.widget.CardView>
        
        <!-- 控制设置卡片 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_control_settings"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="8dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">
            
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="控制设置"
                android:textSize="18sp"
                android:textColor="@android:color/black"
                android:layout_gravity="center" />
        </androidx.cardview.widget.CardView>
        
        <!-- 功能设置卡片 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_function_settings"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="8dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">
            
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="功能设置"
                android:textSize="18sp"
                android:textColor="@android:color/black"
                android:layout_gravity="center" />
        </androidx.cardview.widget.CardView>
        
        <!-- 应用设置卡片 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_app_settings"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="8dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">
            
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="应用设置"
                android:textSize="18sp"
                android:textColor="@android:color/black"
                android:layout_gravity="center" />
        </androidx.cardview.widget.CardView>
    </GridLayout>

</androidx.constraintlayout.widget.ConstraintLayout>