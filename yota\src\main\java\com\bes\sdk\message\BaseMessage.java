package com.bes.sdk.message;

import com.bes.sdk.utils.MessageID;

import java.io.Serializable;

/**
 * Message is similar with command but it's used on high-level application layer.
 *
 * Message can be delivered by a single command, like most of commands; and also can be
 * delivered by multiple commands like <PERSON>ceIn<PERSON> returned from device.
 */
public class BaseMessage implements Serializable
{
    private MessageID messageId;

    private Object msgContent;

    private boolean isPush;

    /**
     * Message ID definition.
     * @return
     */
    public MessageID getMsgID()  {
        return messageId;
    }

    /**
     * Different message will contain different types of content.
     *
     * @return object. Return value must can be serialized.
     */
    public Object getMsgContent()  {
        return msgContent;
    }

    /**
     * Check if message pushed from device.
     * @return true pushed from device, is not response to app request; otherwise indicates this message is response to app request.
     */
    public boolean isPush() {
        return isPush;
    }

    public void setPush(boolean push) {
        isPush = push;
    }

    /**
     * @see #getMsgID()
     * @param messageId
     */
    public void setMessageId(MessageID messageId) {
        this.messageId = messageId;
    }

    /**
     * @see #getMsgContent()
     * @param msgContent
     */
    public void setMsgContent(Object msgContent) {
        this.msgContent = msgContent;
    }
}
