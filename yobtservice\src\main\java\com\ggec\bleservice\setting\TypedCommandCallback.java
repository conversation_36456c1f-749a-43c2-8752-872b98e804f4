package com.ggec.bleservice.setting;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-29
 * Description: 
 * 类型化命令回调接口
 * 提供类型安全的命令回调机制，只有特定类型的命令才会触发此回调
 */
public abstract class TypedCommandCallback<T extends Command> implements Command.CommandCallback {
    
    private final Class<T> commandType;
    
    /**
     * 构造方法
     * @param commandType 命令类型Class对象
     */
    public TypedCommandCallback(Class<T> commandType) {
        this.commandType = commandType;
    }
    
    /**
     * 获取目标命令类型
     */
    public Class<T> getCommandType() {
        return commandType;
    }
    
    /**
     * 获取目标命令类型名称
     */
    public String getCommandTypeName() {
        return commandType.getSimpleName();
    }
    
    /**
     * 检查命令是否匹配此回调的类型
     */
    public boolean isCommandTypeMatch(Command command) {
        return commandType.isInstance(command);
    }
    
    // ========== Command.CommandCallback接口实现 ==========
    
    @Override
    public final void onCommandCompleted(Command command, int code, String result) {
        if (isCommandTypeMatch(command)) {
            @SuppressWarnings("unchecked")
            T typedCommand = (T) command;
            onTypedCommandCompleted(typedCommand, code, result);
        }
    }
    
    @Override
    public final void onCommandCompleted(Command command, int code, Object yobackdata, String result) {
        if (isCommandTypeMatch(command)) {
            @SuppressWarnings("unchecked")
            T typedCommand = (T) command;
            onTypedCommandCompleted(typedCommand, code, yobackdata, result);
        }
    }
    
    @Override
    public final void onCommandCompleted(Command command, int code, int resultValue, String result) {
        if (isCommandTypeMatch(command)) {
            @SuppressWarnings("unchecked")
            T typedCommand = (T) command;
            onTypedCommandCompleted(typedCommand, code, resultValue, result);
        }
    }
    
    // ========== 子类需要实现的类型安全回调方法 ==========
    
    /**
     * 类型安全的命令完成回调
     * @param command 类型安全的命令对象
     * @param code 结果状态码
     * @param result 执行结果数据
     */
    public void onTypedCommandCompleted(T command, int code, String result) {
        // 默认实现为空，子类可选择性覆盖
    }
    
    /**
     * 类型安全的命令完成回调（带数据）
     * @param command 类型安全的命令对象
     * @param code 结果状态码
     * @param yobackdata 返回的原始数据，失败时为null
     * @param result 执行结果数据
     */
    public void onTypedCommandCompleted(T command, int code, Object yobackdata, String result) {
        // 默认调用无数据版本，保持向后兼容
        onTypedCommandCompleted(command, code, result);
    }
    
    /**
     * 类型安全的命令完成回调（带结果值）
     * @param command 类型安全的命令对象
     * @param code 结果状态码
     * @param resultValue 结果值，表示具体的命令完成状态
     * @param result 执行结果数据
     */
    public void onTypedCommandCompleted(T command, int code, int resultValue, String result) {
        // 默认调用无数据版本，保持向后兼容
        onTypedCommandCompleted(command, code, result);
    }
} 