package com.bes.bessdk.utils.sha;


import android.util.Log;

import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.ShortBufferException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class aes {

    /**解密
     * @param encrypted  待解密内容
     * @param key 解密密钥
     * @return
     */
    public static byte[] decrypt(byte[] encrypted, byte[] key) {
        SecretKeySpec skeySpec = new SecretKeySpec(key, "AES");
        Cipher cipher = null;
        try {
            cipher = Cipher.getInstance("AES/CBC/NoPadding");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (NoSuchPaddingException e) {
            e.printStackTrace();
        }
        try {
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, new IvParameterSpec(new byte[]{0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f}));
        } catch (InvalidAlgorithmParameterException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        }
        byte[] decrypted = new byte[encrypted.length];
        try {
            decrypted = cipher.doFinal(encrypted);
        } catch (BadPaddingException e) {
            e.printStackTrace();
        } catch (IllegalBlockSizeException e) {
            e.printStackTrace();
        }
        return decrypted;
    }

    /**
     * 加密
     *
     * @param content 需要加密的内容
     * @param password  加密密码
     * @return
     */
    public static byte[] encrypt(byte[] content, byte[] password) {
        int inLen = content.length;
        int finLen = inLen % 16 != 0 ? inLen + 16 - (inLen % 16) : inLen;
        byte[] finByte = new byte[finLen];
        System.arraycopy(content, 0, finByte, 0, inLen);
        try {
            SecretKeySpec key = new SecretKeySpec(password, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            byte[] byteContent = finByte;
            try {
                cipher.init(Cipher.ENCRYPT_MODE, key, new IvParameterSpec(new byte[]{0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f}));// 初始化
            } catch (InvalidAlgorithmParameterException e) {
                e.printStackTrace();
            }

            byte[] resultBytes = new byte[byteContent.length];
            try {
                int result = cipher.doFinal(byteContent, 0, byteContent.length, resultBytes);
            } catch (ShortBufferException e) {
                e.printStackTrace();
            }

            return resultBytes; // 加密
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (NoSuchPaddingException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (IllegalBlockSizeException e) {
            e.printStackTrace();
        } catch (BadPaddingException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void demo() {
        byte[] content = new byte[]{0x11, 0x22, 0x33, 0x11, 0x22, 0x33, 0x11, 0x11, 0x22, 0x33, 0x11, 0x22, 0x33, 0x11, 0x11, 0x22, 0x33, 0x11, 0x22, 0x33, 0x11};
        byte[] password = new byte[]{0x11, 0x22, 0x33, 0x11, 0x22, 0x33, 0x11, 0x22, 0x33, 0x11, 0x22, 0x33, 0x33, 0x11, 0x22, 0x33};
        //加密
        byte[] encryptResult = encrypt(content, password);
        //解密
        byte[] decryptResult = decrypt(encryptResult, password);

    }

}
