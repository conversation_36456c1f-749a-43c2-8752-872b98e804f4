<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <layer-list>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/background_primary" />
                    <corners android:radius="4dp" />
                    <stroke android:width="1dp" android:color="@color/background_tertiary" />
                    <padding android:right="12dp" />
                </shape>
            </item>
            <item android:gravity="end|center_vertical">
                <rotate android:fromDegrees="90" android:toDegrees="90"
                    android:pivotX="50%" android:pivotY="50%">
                    <shape android:shape="line">
                        <stroke android:width="1.5dp" android:color="@color/brand" />
                        <size android:width="12dp" />
                    </shape>
                </rotate>
            </item>
            <item android:gravity="end|center_vertical">
                <rotate android:fromDegrees="180" android:toDegrees="180"
                    android:pivotX="47%" android:pivotY="50%">
                    <shape android:shape="line">
                        <stroke android:width="1.5dp" android:color="@color/brand" />
                        <size android:width="12dp" />
                    </shape>
                </rotate>
            </item>
        </layer-list>
    </item>
</selector> 