package com.bes.sdk.core;

import androidx.annotation.NonNull;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/16 14:13
 */

public class BesOtaEntity {

    public List<OtaItem> otaDataList;

    public static class OtaItem {

        public static final int TYPE_FW = 0;


        //0 是固件， 其他的是资源1 radio_button_file_type_online  2 自定义表盘    3  radio_button_file_type_picture   4 radio_button_file_type_font
        // 5 radio_button_file_type_tp
        //6 radio_button_file_type_heart_rate  7 radio_button_file_type_language  8 radio_button_file_type_ota_boot
        public int type;

        /**
         * >.升级 boot 的 bin 文件 boot.bin
         * >.应用固件的 bin 文件 app.bin
         * >.字库打包 bin 文件 font_lib.bin
         * >.图片打包 bin 文件 image_lib.bin
         * >.触摸 tp 固件 bin 文件 tp.bin
         * >.出厂数据 bin 文件 factory.bin
         */
        public String name;
        public byte[] bytes;

        public byte[] param = new byte[]{};
        public OtaItem() {

        }

        public OtaItem(int i, String s, byte[] readFile) {
            this.type = i;
            name = s;
            bytes = readFile;

        }

        @Override
        public String toString() {
            return "OtaItem{" +
                    "type=" + type +
                    ", name='" + name + '\'' +
                    ", bytesSize=" + bytes.length +
                    '}';
        }
    }
}
