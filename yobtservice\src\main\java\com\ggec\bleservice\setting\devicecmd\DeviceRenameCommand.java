package com.ggec.bleservice.setting.devicecmd;

import com.ggec.bleservice.setting.Command;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import java.io.UnsupportedEncodingException;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 修改设备经典蓝牙名称命令
 * 负责设置耳机的自定义蓝牙名称，最大支持210字节
 * 设置了3.5秒的超时时间以适应设备重命名操作的实际响应时间
 */
public class DeviceRenameCommand extends Command {
    private static final String TAG = "DeviceRenameCommand";
    
    // 命令前缀，用于确认是修改耳机名称命令
    private static final String COMMAND_PREFIX = "99EC92";
    
    // 命令校验和后缀
    private static final String COMMAND_SUFFIX = "1234";
    
    // 最大支持的设备名称字节长度
    private static final int MAX_NAME_BYTES = 168;
    
    // 设备重命名命令的超时时间（毫秒）
    private static final long RENAME_COMMAND_TIMEOUT = 3500;
    
    // 设置的设备名称
    private final String deviceName;
    
    /**
     * 构造方法
     * @param deviceName 要设置的设备名称
     */
    public DeviceRenameCommand(String deviceName) {
        super();
        this.deviceName = deviceName != null ? deviceName : "";
        // 设置命令前缀
        setCommandPrefix("99EC92");
    }
    
    @Override
    public String getCommandData() {
        try {
            // 将设备名称转换为UTF-8编码的字节数组
            byte[] nameBytes = deviceName.getBytes("UTF-8");
            
            // 检查字节长度是否超过最大限制
            if (nameBytes.length > MAX_NAME_BYTES) {
                YoBTSDKLog.w(TAG, "设备名称过长，将被截断");
                byte[] truncatedBytes = new byte[MAX_NAME_BYTES];
                System.arraycopy(nameBytes, 0, truncatedBytes, 0, MAX_NAME_BYTES);
                nameBytes = truncatedBytes;
            }
            
            // 获取名称字节的长度 (2字节)
            String lengthHex = String.format("%04X", nameBytes.length);
            YoBTSDKLog.d(TAG, "设备名称UTF-8编码后字节长度: " + nameBytes.length + ", 十六进制: " + lengthHex);

            // 将字节数组转换为十六进制字符串
            StringBuilder hexBuilder = new StringBuilder();
            for (byte b : nameBytes) {
                hexBuilder.append(String.format("%02X", b & 0xff));
            }
            
            // 组合完整命令
            String command = COMMAND_PREFIX + lengthHex + hexBuilder.toString() + COMMAND_SUFFIX;
            YoBTSDKLog.d(TAG, "生成的命令: " + command);
            return command;
        } catch (UnsupportedEncodingException e) {
            YoBTSDKLog.e(TAG, "编码设备名称失败", e);
            return COMMAND_PREFIX + "0000" + COMMAND_SUFFIX;
        }
    }
    
    @Override
    public String parseResponse(String responseData) {
        YoBTSDKLog.d(TAG, "解析响应数据: " + responseData);
        
        if (responseData == null || responseData.length() < 16) {
            return "响应数据格式错误";
        }
        
        // 检查前缀
        String prefix = responseData.substring(0, 6);
        if (!COMMAND_PREFIX.equals(prefix)) {
            return "响应数据前缀不匹配";
        }
        
        // 获取命令状态字节 (第7个字节，响应格式中的第6个字节)
        // 在十六进制字符串中，位置为10-12
        String statusByte = responseData.substring(10, 12);
        
        int resultCode = ResultCode.FAILED;
        int resultValue = 0;
        String result;
        
        if ("00".equals(statusByte)) {
            resultCode = ResultCode.SUCCESS;
            result = "修改耳机名称成功: " + deviceName;
            resultValue = 1;
        } else if ("01".equals(statusByte)) {
            result = "修改耳机名称失败: 名称长度超过限制";
            resultValue = 304;
        } else if ("02".equals(statusByte)) {
            result = "修改耳机名称失败: TWS未配对";
            resultValue = 305;
        } else {
            result = "修改耳机名称失败: 未知错误，状态码: " + statusByte;
            resultValue = 404;
        }
        
        // 通知命令完成
        notifyCompletion(resultCode, resultValue, result);
        
        return result;
    }
    
    @Override
    public boolean isResponseMatch(String responseData) {
        // 检查响应前缀是否匹配
        if (responseData != null && responseData.length() >= 6) {
            String prefix = responseData.substring(0, 6);
            return COMMAND_PREFIX.equals(prefix);
        }
        return false;
    }
    
    @Override
    public long getTimeoutMs() {
        return RENAME_COMMAND_TIMEOUT;
    }
} 