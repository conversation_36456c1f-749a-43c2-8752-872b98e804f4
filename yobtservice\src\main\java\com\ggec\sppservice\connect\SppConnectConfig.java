package com.ggec.sppservice.connect;

import java.util.UUID;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * SPP连接配置类
 */
public class SppConnectConfig {
    // SPP服务UUID
    public static final UUID SPP_UUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB");
    
    // 默认连接超时时间（毫秒）
    private static final long DEFAULT_TIMEOUT = 30000;
    
    // 连接超时时间（毫秒）
    private final long timeoutMillis;
    
    // 是否自动配对
    private final boolean autoPair;
    
    // 是否重试连接
    private final boolean retryConnect;
    
    // 重试次数
    private final int retryCount;
    
    /**
     * 私有构造方法
     * @param builder 配置构建器
     */
    private SppConnectConfig(Builder builder) {
        this.timeoutMillis = builder.timeoutMillis;
        this.autoPair = builder.autoPair;
        this.retryConnect = builder.retryConnect;
        this.retryCount = builder.retryCount;
    }
    
    /**
     * 获取连接超时时间
     * @return 超时时间（毫秒）
     */
    public long getTimeoutMillis() {
        return timeoutMillis;
    }
    
    /**
     * 是否自动配对
     * @return 是否自动配对
     */
    public boolean isAutoPair() {
        return autoPair;
    }
    
    /**
     * 是否重试连接
     * @return 是否重试连接
     */
    public boolean isRetryConnect() {
        return retryConnect;
    }
    
    /**
     * 获取重试次数
     * @return 重试次数
     */
    public int getRetryCount() {
        return retryCount;
    }
    
    /**
     * 创建新的构建器
     * @return 配置构建器
     */
    public static Builder newBuilder() {
        return new Builder();
    }
    
    /**
     * SPP连接配置构建器
     */
    public static class Builder {
        private long timeoutMillis = DEFAULT_TIMEOUT;
        private boolean autoPair = true;
        private boolean retryConnect = true;
        private int retryCount = 3;
        
        /**
         * 设置连接超时时间
         * @param timeoutMillis 超时时间（毫秒）
         * @return 构建器
         */
        public Builder timeout(long timeoutMillis) {
            this.timeoutMillis = timeoutMillis;
            return this;
        }
        
        /**
         * 设置是否自动配对
         * @param autoPair 是否自动配对
         * @return 构建器
         */
        public Builder autoPair(boolean autoPair) {
            this.autoPair = autoPair;
            return this;
        }
        
        /**
         * 设置是否重试连接
         * @param retryConnect 是否重试连接
         * @return 构建器
         */
        public Builder retryConnect(boolean retryConnect) {
            this.retryConnect = retryConnect;
            return this;
        }
        
        /**
         * 设置重试次数
         * @param retryCount 重试次数
         * @return 构建器
         */
        public Builder retryCount(int retryCount) {
            this.retryCount = retryCount;
            return this;
        }
        
        /**
         * 构建SPP连接配置
         * @return SPP连接配置
         */
        public SppConnectConfig build() {
            return new SppConnectConfig(this);
        }
    }
} 