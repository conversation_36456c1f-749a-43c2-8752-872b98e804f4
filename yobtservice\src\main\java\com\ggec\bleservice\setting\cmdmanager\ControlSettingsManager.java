package com.ggec.bleservice.setting.cmdmanager;

import com.ggec.bleservice.setting.controlcmd.ControlCommand;
import com.ggec.bleservice.setting.controlcmd.ResetControlCommand;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 控制设置管理器
 * 负责管理所有控制相关的设置，包括触控、重置等
 */
public class ControlSettingsManager extends BaseCommandManager {
    private static final String TAG = "ControlSettingsManager";
    
    // 单例实例
    private static ControlSettingsManager instance;
    
    /**
     * 获取单例实例
     */
    public static synchronized ControlSettingsManager getInstance() {
        if (instance == null) {
            instance = new ControlSettingsManager();
        }
        return instance;
    }
    
    /**
     * 私有构造方法
     */
    private ControlSettingsManager() {
        init(); // 调用父类初始化方法
    }
    
    /**
     * 释放资源
     */
    @Override
    public void release() {
        super.release();
        instance = null;
    }
    
    /**
     * 设置控制功能
     * @param earSide 耳机侧（左耳、右耳）
     * @param tapType 触击类型（双击、三击）
     * @param functionType 功能类型
     */
    public void setControl(int earSide, int tapType, int functionType) {
        YoBTSDKLog.i(TAG, "设置控制: 耳机侧=" + earSide + ", 触击类型=" + tapType + ", 功能类型=" + functionType);
        
        // 创建控制命令
        ControlCommand command = new ControlCommand(earSide, tapType, functionType);
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 恢复默认控制设置
     * @param isConfirm 是否确认（true: 确认, false: 取消）
     */
    public void resetControls(boolean isConfirm) {
        YoBTSDKLog.i(TAG, "恢复默认控制: " + (isConfirm ? "确认" : "取消"));
        
        // 取消无需数据收发，直接返回
        if (!isConfirm) {
            YoBTSDKLog.i(TAG, "取消恢复默认控制设置，无需发送命令");
            return;
        }
        
        // 创建重置控制命令
        ResetControlCommand command = new ResetControlCommand(isConfirm);
        
        // 执行命令
        createAndEnqueueCommand(command);
    }

    @Override
    protected String getManagerTag() {
        return TAG;
    }
    
    @Override
    protected boolean isDataRelevant(String data) {
        // 检查数据前缀是否与控制设置相关
        if (data != null && data.length() >= 6) {
            String prefix = data.substring(0, 6); // 取前6个字符 (99ECxx)
            
            // 自定义按键命令 99EC85
            // 恢复默认控制 99EC86
            return "99EC85".equals(prefix) || "99EC86".equals(prefix);
        }
        return false;
    }
} 