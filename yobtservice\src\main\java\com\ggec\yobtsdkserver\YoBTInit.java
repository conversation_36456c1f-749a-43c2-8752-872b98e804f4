package com.ggec.yobtsdkserver;

import android.app.Activity;
import android.app.Application;
import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.content.Intent;

import com.ggec.bleservice.YoBLEApi;
import com.ggec.bleservice.YoCommandApi;
import com.ggec.yobtsdkserver.sdkmanager.BTPermissionManager;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;
import com.ggec.sppservice.YoSPPApi;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * YoBT SDK 初始化类
 * 集中管理SDK初始化、权限检查等功能
 */
public class YoBTInit {
    private static final String TAG = "YoBTInit";
    
    public static final int REQUEST_ENABLE_BT = 2001;
    
    private static YoBTInit instance;
    
    private boolean initialized = false;
    private Application application;
    
    private BluetoothEnableCallback bluetoothEnableCallback;
    private PermissionResultCallback permissionResultCallback;
    
    public interface BluetoothEnableCallback {
        void onBluetoothEnableResult(boolean enabled);
    }
    
    public interface PermissionResultCallback {
        void onPermissionResult(boolean allGranted);
    }
    
    public static YoBTInit getInstance() {
        if (instance == null) {
            synchronized (YoBTInit.class) {
                if (instance == null) {
                    instance = new YoBTInit();
                }
            }
        }
        return instance;
    }
    
    private YoBTInit() {
        // 私有构造函数
    }
    
    public void init(Application application) {
        if (initialized) return;
        
        this.application = application;
        
        // 初始化日志系统，不再设置控制台日志开关（默认始终开启）
        YoBTSDKLog.init(application); // 仅提供上下文
        
        YoBTSDKLog.i(TAG, "YoBT SDK 初始化开始");
        
        // 初始化BLE模块
        YoBLEApi.getInstance().init(application);
        YoCommandApi.getInstance();

        // 初始化SPP模块
        YoSPPApi.getInstance(application);
        
        initialized = true;
        YoBTSDKLog.i(TAG, "YoBT SDK 初始化完成");
    }
    
    public boolean isInitialized() {
        return initialized;
    }
    
    public void release() {
        if (!initialized) return;
        
        YoBTSDKLog.i(TAG, "YoBT SDK 开始释放资源");
        
        // 释放BLE资源
        YoBLEApi.getInstance().release();
        YoCommandApi.getInstance().release();
        
        // 释放SPP资源
        YoSPPApi.getInstance(application).release();
        
        initialized = false;
        instance = null;
        application = null;
        
        YoBTSDKLog.i(TAG, "YoBT SDK 资源释放完成");
    }
    
    public YoBLEApi getBleApi() {
        if (!initialized) {
            throw new IllegalStateException("YoBT SDK未初始化，请先调用init()方法");
        }
        return YoBLEApi.getInstance();
    }
    
    public YoCommandApi getCommandApi() {
        if (!initialized) {
            throw new IllegalStateException("YoBT SDK未初始化，请先调用init()方法");
        }
        return YoCommandApi.getInstance();
    }

    /*
     * SPP API 暂不暴露给外部
    public YoSPPApi getSppApi() {
        if (!initialized) {
            throw new IllegalStateException("YoBT SDK未初始化，请先调用init()方法");
        }
        return YoSPPApi.getInstance(application);
    }
    */
    
    public void setBluetoothEnableCallback(BluetoothEnableCallback callback) {
        this.bluetoothEnableCallback = callback;
    }
    
    public void setPermissionResultCallback(PermissionResultCallback callback) {
        this.permissionResultCallback = callback;
    }
    
    public boolean handleActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQUEST_ENABLE_BT) {
            boolean enabled = resultCode == Activity.RESULT_OK;
            if (bluetoothEnableCallback != null) {
                bluetoothEnableCallback.onBluetoothEnableResult(enabled);
            }
            return true;
        }
        return false;
    }
    
    public boolean handlePermissionResult(int requestCode, String[] permissions, int[] grantResults) {
        if (requestCode == BTPermissionManager.REQUEST_BLUETOOTH_PERMISSIONS) {
            boolean allGranted = BTPermissionManager.handlePermissionResult(permissions, grantResults);
            if (permissionResultCallback != null) {
                permissionResultCallback.onPermissionResult(allGranted);
            }
            return true;
        }
        return false;
    }
    
    public boolean checkBluetoothEnabled(Activity activity) {
        BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
        if (adapter == null) {
            return false; // 设备不支持蓝牙
        }
        
        if (!adapter.isEnabled()) {
            try {
                Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
                activity.startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT);
            } catch (SecurityException e) {
                // Android 12+上需要蓝牙权限才能执行此操作
                if (!BTPermissionManager.hasBluetoothPermissions(activity)) {
                    BTPermissionManager.checkAndRequestPermissions(activity);
                }
                return false;
            } catch (Exception e) {
                // 其他异常情况
                return false;
            }
            return false;
        }
        
        return true;
    }
    
    public boolean hasBluetoothPermissions(Context context) {
        return BTPermissionManager.hasBluetoothPermissions(context);
    }
    
    public boolean checkAndRequestPermissions(Activity activity) {
        return BTPermissionManager.checkAndRequestPermissions(activity);
    }
} 