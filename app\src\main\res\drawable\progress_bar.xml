<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:id="@android:id/background">
        <shape>
            <corners android:radius="5dp"/>
            <stroke
                android:width="1dp"
                android:color="@color/colorPrimaryDark"/>
            <solid android:color="#FFFFFF"/>
        </shape>
    </item>
    <!-- 第二条进度条颜色 -->
    <!--<item android:id="@android:id/secondaryProgress">
        <clip>
            <shape>
                <corners android:radius="10dp"/>

                <gradient
                    android:angle="90.0"
                    android:centerColor="@color/btn_option_bg"
                    android:centerY="0.45"
                    android:endColor="@color/colorAccent"
                    android:startColor="@color/btn_option_bg"/>
            </shape>
        </clip>
    </item>-->
    <!-- 进度条 -->
    <item android:id="@android:id/progress">
        <clip>
            <shape>
                <corners android:radius="5dp"/>
                <stroke
                    android:width="1dp"
                    android:color="@color/colorPrimaryDark"/>
                <solid android:color="@color/colorAccent"/>
            </shape>
        </clip>
    </item>

</layer-list>