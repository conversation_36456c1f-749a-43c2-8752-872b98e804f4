package com.ggec.hs01.utils;

import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.ggec.hs01.R;
import com.ggec.bleservice.YoBLEApi;
import com.ggec.sppservice.YoSPPApi;
import com.ggec.hs01.GGECHSApplication;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 蓝牙设备适配器，用于在RecyclerView中显示扫描到的蓝牙设备
 */
public class BluetoothDeviceAdapter extends RecyclerView.Adapter<BluetoothDeviceAdapter.DeviceViewHolder> {

    private final List<BluetoothDevice> deviceList = new ArrayList<>();
    private final List<Integer> rssiList = new ArrayList<>();
    private OnDeviceClickListener listener;
    private final ExecutorService executor = Executors.newSingleThreadExecutor();
    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    private String connectedMac;

    private Context context;

    public interface OnDeviceClickListener {
        void onDeviceClick(BluetoothDevice device,String classicMac);
    }

    public BluetoothDeviceAdapter(Context context) {
        this.context = context;
    }

    public void setOnDeviceClickListener(OnDeviceClickListener listener) {
        this.listener = listener;
    }

    public void setConnectedMac(String connectedMac){
        this.connectedMac = connectedMac;
    }

    @NonNull
    @Override
    public DeviceViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_ble_device, parent, false);
        return new DeviceViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull DeviceViewHolder holder, int position) {
        BluetoothDevice device = deviceList.get(position);
        int rssi = rssiList.get(position);
        
        // 设置设备名称，如果为空则显示MAC地址
        String deviceName = device.getName();
        if (deviceName == null || deviceName.isEmpty()) {
            deviceName = device.getAddress();
        }
        holder.tvDeviceName.setText(deviceName);
        
        // 设置设备MAC地址
        if(device.getAddress().equals(connectedMac)){
            holder.tvDeviceAddress.setTextColor(ContextCompat.getColor(context, R.color.warning));
        } else {
            holder.tvDeviceAddress.setTextColor(ContextCompat.getColor(context, R.color.black));
        }
        holder.tvDeviceAddress.setText(device.getAddress());
        
        // 设置信号强度
        holder.tvSignalStrength.setText(String.format("%d dBm", rssi));
        
        // 获取SPP API实例
        YoSPPApi sppApi = ((GGECHSApplication) holder.itemView.getContext().getApplicationContext()).getSppApi();
        
        // 获取并显示解析的广播数据
        String parsedDataString = YoBLEApi.getInstance().getParsedAdvertisingDataAsString(device);
        if (parsedDataString != null && !parsedDataString.isEmpty()) {
            // 解析字符串
            String classicMac = parseValue(parsedDataString, "macAddress");
            String color = parseValue(parsedDataString, "color");
            String featureCode = parseValue(parsedDataString, "featureCode");

            // 显示经典蓝牙MAC地址
            if (classicMac != null && !classicMac.isEmpty()) {
                holder.tvClassicMac.setText(String.format("BT_MAC地址：%s", classicMac));
                holder.tvClassicMac.setVisibility(View.VISIBLE);
                
                // 异步检查经典蓝牙连接状态
                holder.tvSppConnectionStatus.setText("BT连接状态：检查中...");
                holder.tvSppConnectionStatus.setVisibility(View.VISIBLE);
                
                final String macToCheck = classicMac;
                executor.execute(() -> {
                    final boolean isConnected = sppApi != null && sppApi.isSppDeviceConnected(macToCheck);
                    mainHandler.post(() -> {
                        if (holder.getAdapterPosition() == position) {
                            holder.tvSppConnectionStatus.setText(String.format("经典蓝牙连接状态：%s", isConnected ? "已连接" : "未连接"));
                        }
                    });
                });

                // 设置点击事件
                holder.itemView.setOnClickListener(v -> {
                    if (listener != null) {
                        listener.onDeviceClick(device,classicMac);
                    }
                });

            } else {
                holder.tvClassicMac.setText("BT_MAC：-");
                holder.tvClassicMac.setVisibility(View.VISIBLE);
                holder.tvSppConnectionStatus.setText("BT连接状态：-");
                holder.tvSppConnectionStatus.setVisibility(View.VISIBLE);
            }
            
            // 显示设备颜色
            /*
            if (color != null && !color.isEmpty()) {
                holder.tvColor.setText(String.format("颜色：%s", color));
                holder.tvColor.setVisibility(View.VISIBLE);
            } else {
                holder.tvColor.setText("颜色：-");
                holder.tvColor.setVisibility(View.VISIBLE);
            }
            */
            
            // 显示特征码
            /*
            if (featureCode != null && !featureCode.isEmpty()) {
                holder.tvFeatureCode.setText(String.format("featurecode：%s", featureCode));
                holder.tvFeatureCode.setVisibility(View.VISIBLE);
            } else {
                holder.tvFeatureCode.setText("featurecode：-");
                holder.tvFeatureCode.setVisibility(View.VISIBLE);
            }
            */
        } else {
            // 没有解析数据，隐藏或显示默认值
            holder.tvClassicMac.setText("经典蓝牙MAC地址：-");
            // holder.tvColor.setText("颜色：-");
            // holder.tvFeatureCode.setText("featurecode：-");
            holder.tvSppConnectionStatus.setText("经典蓝牙连接状态：-");
            holder.tvSppConnectionStatus.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public int getItemCount() {
        return deviceList.size();
    }

    /**
     * 添加设备到列表
     * @param device 蓝牙设备
     * @param rssi 信号强度
     */
    public void addDevice(BluetoothDevice device, int rssi) {
        // 检查设备是否已在列表中
        for (int i = 0; i < deviceList.size(); i++) {
            if (deviceList.get(i).getAddress().equals(device.getAddress())) {
                // 更新信号强度
                rssiList.set(i, rssi);
                notifyItemChanged(i);
                return;
            }
        }
        
        // 添加新设备
        deviceList.add(device);
        rssiList.add(rssi);
        notifyItemInserted(deviceList.size() - 1);
    }

    /**
     * 清空设备列表
     */
    public void clearDevices() {
        deviceList.clear();
        rssiList.clear();
        notifyDataSetChanged();
    }

    private String parseValue(String source, String key) {
        String searchKey = key + "='";
        int startIndex = source.indexOf(searchKey);
        if (startIndex == -1) {
            return null;
        }
        startIndex += searchKey.length();
        int endIndex = source.indexOf('\'', startIndex);
        if (endIndex == -1) {
            return null;
        }
        return source.substring(startIndex, endIndex);
    }

    /**
     * 获取设备列表
     */
    public List<BluetoothDevice> getDevices() {
        return new ArrayList<>(deviceList);
    }

    static class DeviceViewHolder extends RecyclerView.ViewHolder {
        TextView tvDeviceName;
        TextView tvDeviceAddress;
        TextView tvSignalStrength;
        TextView tvClassicMac;
        // TextView tvColor;
        // TextView tvFeatureCode;
        TextView tvSppConnectionStatus;

        DeviceViewHolder(View itemView) {
            super(itemView);
            tvDeviceName = itemView.findViewById(R.id.tv_ble_device_name);
            tvDeviceAddress = itemView.findViewById(R.id.tv_ble_device_address);
            tvSignalStrength = itemView.findViewById(R.id.tv_ble_device_rssi);
            tvClassicMac = itemView.findViewById(R.id.tv_ble_classic_mac);
            // tvColor = itemView.findViewById(R.id.tv_ble_color);
            // tvFeatureCode = itemView.findViewById(R.id.tv_ble_feature_code);
            tvSppConnectionStatus = itemView.findViewById(R.id.tv_spp_connection_status);
        }
    }
} 