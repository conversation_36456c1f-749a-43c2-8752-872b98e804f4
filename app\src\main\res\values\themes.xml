<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.HS01" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- 设置主色调为品牌色（宇宙蓝） -->
        <!-- 设置状态栏背景颜色 -->
        <item name="colorPrimaryDark">@android:color/white</item>
        <item name="colorPrimary">@color/brand</item>
        <item name="colorPrimaryVariant">@color/brand</item>
        <item name="colorAccent">@color/brand</item>
        <item name="colorSecondary">@color/brand</item>
        <!-- 禁用Material按钮的样式覆盖 -->
        <item name="materialButtonStyle">@null</item>
        
        <!-- 设置窗口背景色为雪域灰，这会影响启动页面的背景色 -->
        <item name="android:windowBackground">@android:color/white</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:statusBarColor">@android:color/white</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>

    <style name="Theme.HS01" parent="Base.Theme.HS01" />
</resources>