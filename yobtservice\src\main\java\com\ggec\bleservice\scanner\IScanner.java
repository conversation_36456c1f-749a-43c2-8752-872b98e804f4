package com.ggec.bleservice.scanner;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 蓝牙扫描器接口，定义扫描功能的基本行为
 */
public interface IScanner {
    /**
     * 设置扫描配置
     * @param config 扫描配置
     */
    void setScanConfig(ScanConfig config);
    
    /**
     * 开始扫描
     * @param callback 扫描回调
     */
    void startScan(ScanCallback callback);
    
    /**
     * 停止扫描
     */
    void stopScan();
    
    /**
     * 释放资源
     */
    void close();
} 