package com.ggec.bleservice.setting;

import android.os.Handler;
import android.os.Looper;

import com.ggec.bleservice.YoCommandApi;
import com.ggec.bleservice.setting.cmdmanager.BaseCommandManager;
import com.ggec.bleservice.setting.cmdmanager.BatterySettingsManager;
import com.ggec.bleservice.setting.Command;
import com.ggec.bleservice.setting.cmdmanager.ControlSettingsManager;
import com.ggec.bleservice.setting.cmdmanager.FunctionSettingsManager;
import com.ggec.bleservice.setting.cmdmanager.MusicSettingsManager;
import com.ggec.bleservice.setting.cmdmanager.DeviceSettingsManager;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 统一命令管理器
 * 负责所有命令的中央调度和结果处理
 */
public class CommandManager {
    private static final String TAG = "CommandManager";
    
    // 单例实例
    private static CommandManager instance;
    
    // 各种设置管理器
    private final MusicSettingsManager musicSettingsManager;
    private final ControlSettingsManager controlSettingsManager;
    private final FunctionSettingsManager functionSettingsManager;
    private final BatterySettingsManager batterySettingsManager;
    private final DeviceSettingsManager deviceSettingsManager;
    
    // 管理器映射表 - 命令前缀 -> 管理器
    private final Map<String, BaseCommandManager> managerMap = new HashMap<>();
    
    // 统一回调管理器
    private final CommandCallbackManager callbackManager;
    
    // 主线程Handler，用于确保回调在主线程执行
    private final Handler mainHandler;
    
    /**
     * 命令执行结果回调接口
     */
    public interface CommandCallback {
        /**
         * 命令执行完成回调
         * @param code 结果状态码
         */
        void onCommandResult(int code);
    }
    
    /**
     * 增强命令执行结果回调接口
     */
    public interface CommandDataCallback {
        /**
         * 命令执行完成回调
         * @param code 结果状态码
         * @param yobackdata 返回的数据对象（可能是String, Integer等），失败时为null
         * @param result 命令执行结果
         */
        void onCommandResult(int code, Object yobackdata, String result);
    }

    /**
     * 带结果值的命令执行结果回调接口
     */
    public interface CommandResultValueCallback {
        /**
         * 命令执行完成回调
         * @param code 结果状态码
         * @param resultValue 结果值，表示具体的命令完成状态
         * @param result 命令执行结果
         */
        void onCommandResult(int code, int resultValue, String result);
    }
    
    /**
     * 电池电量信息回调接口
     */
    public interface BatteryInfoCallback {
        /**
         * 电池电量更新回调
         * @param code 结果状态码
         * @param leftLevel 左耳电量（百分比）
         * @param rightLevel 右耳电量（百分比）
         * @param caseLevel 充电仓电量（百分比）
         */
        void onBatteryInfoUpdated(int code, String leftLevel, String rightLevel, String caseLevel);
    }
    
    /**
     * 充电盒充电状态回调接口
     */
    public interface CaseChargingCallback {
        /**
         * 充电盒充电状态更新回调
         * @param code 结果状态码
         * @param isCharging 是否正在充电
         */
        void onCaseChargingUpdated(int code, boolean isCharging);
    }
    
    /**
     * 耳机在充电盒内状态回调接口
     */
    public interface EarInCaseStatusCallback {
        /**
         * 耳机在充电盒内状态更新回调
         * @param code 结果状态码
         * @param status 状态值 (0:都不在，1:左耳在，2:右耳在，3:都在)
         * @param leftInCase 左耳是否在充电盒内
         * @param rightInCase 右耳是否在充电盒内
         */
        void onEarInCaseStatusUpdated(int code, int status, boolean leftInCase, boolean rightInCase);
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized CommandManager getInstance() {
        if (instance == null) {
            instance = new CommandManager();
        }
        return instance;
    }
    
    /**
     * 私有构造方法
     */
    private CommandManager() {
        // 获取主线程Handler
        mainHandler = new Handler(Looper.getMainLooper());
        
        // 获取统一回调管理器
        callbackManager = CommandCallbackManager.getInstance();
        
        // 获取各种设置管理器实例
        musicSettingsManager = MusicSettingsManager.getInstance();
        controlSettingsManager = ControlSettingsManager.getInstance();
        functionSettingsManager = FunctionSettingsManager.getInstance();
        batterySettingsManager = BatterySettingsManager.getInstance();
        deviceSettingsManager = DeviceSettingsManager.getInstance();
        
        // 创建通用命令结果监听器 - 这些回调将直接通过CommandCallbackManager分发
        BaseCommandManager.CommandResultListener commandResultListener = new BaseCommandManager.CommandResultListener() {
            @Override
            public void onCommandResult(Command command, int code, String result) {
                // 不再需要单独处理，Command.notifyCompletion已经会通过CallbackManager分发
            }
            
            @Override
            public void onCommandResult(Command command, int code, Object yobackdata, String result) {
                // 不再需要单独处理，Command.notifyCompletion已经会通过CallbackManager分发
            }

            @Override
            public void onCommandResult(Command command, int code, int resultValue, String result) {
                // 不再需要单独处理，Command.notifyCompletion已经会通过CallbackManager分发
            }
        };
        
        // 设置命令结果监听器
        musicSettingsManager.setCommandResultListener(commandResultListener);
        controlSettingsManager.setCommandResultListener(commandResultListener);
        functionSettingsManager.setCommandResultListener(commandResultListener);
        batterySettingsManager.setCommandResultListener(commandResultListener);
        deviceSettingsManager.setCommandResultListener(commandResultListener);
        
        // 设置电池电量监听器
        batterySettingsManager.setBatteryLevelListener(this::handleBatteryInfo);
        
        // 设置充电盒充电状态监听器
        batterySettingsManager.setCaseChargingStatusListener(this::handleCaseCharging);
        
        // 设置耳机在充电盒内状态监听器
        batterySettingsManager.setEarInCaseStatusListener(this::handleEarInCaseStatus);
        
        // 注册管理器到映射表
        registerManagers();
    }
    
    /**
     * 注册各个管理器到映射表
     */
    private void registerManagers() {
        // 音乐设置管理器
        registerPrefixes(musicSettingsManager, "99EC81", "99EC82", "99EC83", "99EC84");
        
        // 控制设置管理器
        registerPrefixes(controlSettingsManager, "99EC85", "99EC86");
        
        // 功能设置管理器
        registerPrefixes(functionSettingsManager, "99EC87", "99EC88", "99EC89", "99EC8A", "99EC8B", "99EC8C");
        
        // 电池设置管理器
        registerPrefixes(batterySettingsManager, "99EC80","99EC95","99EC96");
        
        // 设备设置管理器
        registerPrefixes(deviceSettingsManager, "99EC90", "99EC91", "99EC92", "99EC93", "99EC94");
    }
    
    /**
     * 注册管理器与多个前缀
     */
    private void registerPrefixes(BaseCommandManager manager, String... prefixes) {
        for (String prefix : prefixes) {
            managerMap.put(prefix, manager);
        }
    }
    
    // ========== 专用回调处理方法 ==========
    
    /**
     * 创建通用命令回调适配器
     */
    private Command.CommandCallback createCommandCallbackAdapter(CommandCallback callback) {
        return new Command.CommandCallback() {
            @Override
            public void onCommandCompleted(Command command, int code, String result) {
                mainHandler.post(() -> callback.onCommandResult(code));
            }
            
            @Override
            public void onCommandCompleted(Command command, int code, Object yobackdata, String result) {
                mainHandler.post(() -> callback.onCommandResult(code));
            }
            
            @Override
            public void onCommandCompleted(Command command, int code, int resultValue, String result) {
                mainHandler.post(() -> callback.onCommandResult(code));
            }
        };
    }
    
    /**
     * 创建数据命令回调适配器
     */
    private Command.CommandCallback createCommandDataCallbackAdapter(CommandDataCallback callback) {
        return new Command.CommandCallback() {
            @Override
            public void onCommandCompleted(Command command, int code, String result) {
                mainHandler.post(() -> callback.onCommandResult(code, null, result));
            }
            
            @Override
            public void onCommandCompleted(Command command, int code, Object yobackdata, String result) {
                mainHandler.post(() -> callback.onCommandResult(code, yobackdata, result));
            }
            
            @Override
            public void onCommandCompleted(Command command, int code, int resultValue, String result) {
                mainHandler.post(() -> callback.onCommandResult(code, null, result));
            }
        };
    }
    
    /**
     * 创建带结果值命令回调适配器
     */
    private Command.CommandCallback createCommandResultValueCallbackAdapter(CommandResultValueCallback callback) {
        return new Command.CommandCallback() {
            @Override
            public void onCommandCompleted(Command command, int code, String result) {
                mainHandler.post(() -> callback.onCommandResult(code, 0, result));
            }
            
            @Override
            public void onCommandCompleted(Command command, int code, Object yobackdata, String result) {
                mainHandler.post(() -> callback.onCommandResult(code, 0, result));
            }
            
            @Override
            public void onCommandCompleted(Command command, int code, int resultValue, String result) {
                mainHandler.post(() -> callback.onCommandResult(code, resultValue, result));
            }
        };
    }
    
    // ========== 专用回调存储 ==========
    
    private BatteryInfoCallback batteryInfoCallback;
    private CaseChargingCallback caseChargingCallback;
    private EarInCaseStatusCallback earInCaseStatusCallback;

    /**
     * 监听游戏模式变化
     * @param callback 游戏模式变化回调
     */
    public void listenGameMode(YoCommandApi.GameModeCallback callback) {
        functionSettingsManager.listenGameMode(callback);
    }

    /**
     * 监听EQ模式变化
     *  @param callback EQ模式变化回调
     **/
    public void listenEQMode(YoCommandApi.EQModeCallback callback) {
        musicSettingsManager.listenEQMode(callback);
    }

    /**
     * 处理电池电量信息更新
     */
    private void handleBatteryInfo(int code, String leftLevel, String rightLevel, String caseLevel) {
        if (batteryInfoCallback != null) {
            // 将字符串电量转换为整数用于回调，因为原有的BatteryInfoCallback接口使用int
//            int leftLevelInt = parseBatteryLevel(leftLevel);
//            int rightLevelInt = parseBatteryLevel(rightLevel);
//            int caseLevelInt = parseBatteryLevel(caseLevel);
            
            mainHandler.post(() -> batteryInfoCallback.onBatteryInfoUpdated(code, leftLevel, rightLevel, caseLevel));
        }
    }
    
    /**
     * 解析电量字符串为整数
     * @param batteryStr 电量字符串（数字或"--"）
     * @return 电量整数值，如果是"--"或解析失败则返回0
     */
    private int parseBatteryLevel(String batteryStr) {
        if (batteryStr == null || "--".equals(batteryStr)) {
            return 0;
        }
        try {
            return Integer.parseInt(batteryStr);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
    
    /**
     * 处理充电盒充电状态信息
     */
    private void handleCaseCharging(int code, boolean isCharging) {
        if (caseChargingCallback != null) {
            mainHandler.post(() -> caseChargingCallback.onCaseChargingUpdated(code, isCharging));
        }
    }
    
    /**
     * 处理耳机在充电盒内状态信息
     */
    private void handleEarInCaseStatus(int code, int status, boolean leftInCase, boolean rightInCase) {
        if (earInCaseStatusCallback != null) {
            mainHandler.post(() -> earInCaseStatusCallback.onEarInCaseStatusUpdated(code, status, leftInCase, rightInCase));
        }
    }
    
    /**
     * 处理蓝牙响应数据
     * 根据前缀将响应分发给对应的管理器
     * @param responseData 响应数据
     * @return 是否已处理
     */
    public boolean handleResponse(String responseData) {
        if (responseData == null || responseData.length() < 6) {
            return false;
        }
        
        // 获取前缀
        String prefix = responseData.substring(0, 6);
        
        // 查找对应的管理器
        BaseCommandManager manager = managerMap.get(prefix);
        if (manager != null) {
            // 将响应分发给对应的管理器
            return manager.handleResponse(responseData);
        }
        
        return false;
    }
    
    // ========== 新的ADD/REMOVE回调方法 ==========
    
    /**
     * 添加命令回调（推荐使用）
     * @param callback 回调接口
     */
    public void addCommandCallback(CommandCallback callback,String commandType) {
        if (callback != null) {
            callbackManager.registerGlobalCallback(createCommandCallbackAdapter(callback), false,commandType);
        }
    }
    
    /**
     * 移除命令回调
     * @param callback 回调接口
     */
    public void removeCommandCallback(CommandCallback callback) {
        if (callback != null) {
            callbackManager.removeGlobalCallback(createCommandCallbackAdapter(callback));
        }
    }
    
    /**
     * 添加数据命令回调（推荐使用）
     * @param callback 回调接口
     */
    public void addCommandDataCallback(CommandDataCallback callback,String commandType) {
        if (callback != null) {
            callbackManager.registerGlobalCallback(createCommandDataCallbackAdapter(callback), false,commandType);
        }
    }
    
    /**
     * 移除数据命令回调
     * @param callback 回调接口
     */
    public void removeCommandDataCallback(CommandDataCallback callback) {
        if (callback != null) {
            callbackManager.removeGlobalCallback(createCommandDataCallbackAdapter(callback));
        }
    }

    /**
     * 添加带结果值的命令回调（推荐使用）
     * @param callback 回调接口
     */
    public void addCommandResultValueCallback(CommandResultValueCallback callback,String commandType) {
        if (callback != null) {
            callbackManager.registerGlobalCallback(createCommandResultValueCallbackAdapter(callback), false,commandType);
        }
    }
    
    /**
     * 移除带结果值的命令回调
     * @param callback 回调接口
     */
    public void removeCommandResultValueCallback(CommandResultValueCallback callback) {
        if (callback != null) {
            callbackManager.removeGlobalCallback(createCommandResultValueCallbackAdapter(callback));
        }
    }
    
    /**
     * 清理所有全局回调
     * 用于防止回调污染，特别是在Activity切换时
     */
    public void clearAllGlobalCallbacks() {
        callbackManager.clearAllGlobalCallbacks();
    }
    
    /**
     * 添加电池信息回调（推荐使用）
     * @param callback 回调接口
     */
    public void addBatteryInfoCallback(BatteryInfoCallback callback) {
        this.batteryInfoCallback = callback;
    }
    
    /**
     * 移除电池信息回调
     * @param callback 回调接口
     */
    public void removeBatteryInfoCallback(BatteryInfoCallback callback) {
        if (this.batteryInfoCallback == callback) {
            this.batteryInfoCallback = null;
        }
    }
    
    /**
     * 添加充电盒充电状态回调（推荐使用）
     * @param callback 回调接口
     */
    public void addCaseChargingCallback(CaseChargingCallback callback) {
        this.caseChargingCallback = callback;
    }
    
    /**
     * 移除充电盒充电状态回调
     * @param callback 回调接口
     */
    public void removeCaseChargingCallback(CaseChargingCallback callback) {
        if (this.caseChargingCallback == callback) {
            this.caseChargingCallback = null;
        }
    }
    
    /**
     * 添加耳机在充电盒内状态回调（推荐使用）
     * @param callback 回调接口
     */
    public void addEarInCaseStatusCallback(EarInCaseStatusCallback callback) {
        this.earInCaseStatusCallback = callback;
    }
    
    /**
     * 移除耳机在充电盒内状态回调
     * @param callback 回调接口
     */
    public void removeEarInCaseStatusCallback(EarInCaseStatusCallback callback) {
        if (this.earInCaseStatusCallback == callback) {
            this.earInCaseStatusCallback = null;
        }
    }
    
    // ========== 保持兼容性的SET方法 ==========
    
    /**
     * 设置命令回调
     * @param callback 回调接口
     */
    public void setCommandCallback(CommandCallback callback,String commandType) {
        // 简化SET语义：直接设置为全局回调
        if (callback != null) {
            callbackManager.registerGlobalCallback(createCommandCallbackAdapter(callback), false,commandType);
        }
    }
    
    /**
     * 设置增强命令回调
     * @param callback 回调接口
     */
    public void setCommandDataCallback(CommandDataCallback callback,String commandType) {
        if (callback != null) {
            callbackManager.registerGlobalCallback(createCommandDataCallbackAdapter(callback), false,commandType);
        }
    }

    /**
     * 设置带结果值的命令回调
     * @param callback 回调接口
     */
    public void setCommandResultValueCallback(CommandResultValueCallback callback,String commandType) {
        if (callback != null) {
            callbackManager.registerGlobalCallback(createCommandResultValueCallbackAdapter(callback), false,commandType);
        }
    }
    
    /**
     * 设置电池信息回调
     * @param callback 回调接口
     */
    public void setBatteryInfoCallback(BatteryInfoCallback callback) {
        this.batteryInfoCallback = callback;
    }
    
    /**
     * 设置充电盒充电状态回调
     * @param callback 回调接口
     */
    public void setCaseChargingCallback(CaseChargingCallback callback) {
        this.caseChargingCallback = callback;
    }
    
    /**
     * 设置耳机在充电盒内状态回调
     * @param callback 回调接口
     */
    public void setEarInCaseStatusCallback(EarInCaseStatusCallback callback) {
        this.earInCaseStatusCallback = callback;
    }
    
    /**
     * 释放资源
     */
    public void release() {
        // 清除专用回调
        batteryInfoCallback = null;
        caseChargingCallback = null;
        earInCaseStatusCallback = null;
        
        // 释放回调管理器资源
        callbackManager.release();
        
        // 释放各管理器资源
        musicSettingsManager.release();
        controlSettingsManager.release();
        functionSettingsManager.release();
        batterySettingsManager.release();
        deviceSettingsManager.release();
        
        // 清空单例
        instance = null;
    }
    
    //=========== 音乐设置相关命令 ===========
    
    /**
     * 设置音量自适应开关
     * @param enabled 是否启用
     */
    public void setVolumeAdaptive(boolean enabled) {
        YoBTSDKLog.i(TAG, "命令: 设置音量自适应 " + (enabled ? "开启" : "关闭"));
        musicSettingsManager.setVolumeAdaptive(enabled);
    }
    
    /**
     * 设置音质模式
     * @param isHighQuality true表示高音质模式，false表示高续航模式
     */
    public void setSoundQualityMode(boolean isHighQuality) {
        YoBTSDKLog.i(TAG, "命令: 设置音质模式 " + (isHighQuality ? "高音质" : "高续航"));
        musicSettingsManager.setQualityMode(isHighQuality);
    }
    
    /**
     * 设置EQ模式
     * @param mode EQ模式
     *             0 - 音乐模式
     *             1 - 电影模式
     *             2 - 新闻模式
     *             3 - 老年人模式
     */
    public void setEQMode(int mode) {
        YoBTSDKLog.i(TAG, "命令: 设置EQ模式 " + mode);
        musicSettingsManager.setEQMode(mode);
    }
    
    /**
     * 设置音量提醒开关
     * @param enabled 是否启用
     */
    public void setVolumeRemind(boolean enabled) {
        musicSettingsManager.setVolumeRemind(enabled);
    }
    
    /**
     * 设置使用时长提醒开关
     * @param enabled 是否启用
     */
    public void setUsageRemind(boolean enabled) {
        musicSettingsManager.setUsageRemind(enabled);
    }
    
    //=========== 功能设置相关命令 ===========
    
    /**
     * 设置佩戴检测功能开关
     * @param enabled 是否启用
     */
    public void setWearDetection(boolean enabled) {
        YoBTSDKLog.i(TAG, "命令: 设置佩戴检测 " + (enabled ? "开启" : "关闭"));
        functionSettingsManager.setWearDetection(enabled);
    }
    
    /**
     * 设置语音唤醒功能开关
     * @param enabled 是否启用
     */
    public void setVoiceWakeup(boolean enabled) {
        YoBTSDKLog.i(TAG, "命令: 设置语音唤醒 " + (enabled ? "开启" : "关闭"));
        functionSettingsManager.setVoiceWakeup(enabled);
    }
    
    /**
     * 设置游戏模式开关
     * @param enabled 是否启用
     */
    public void setGameMode(boolean enabled) {
        YoBTSDKLog.i(TAG, "命令: 设置游戏模式 " + (enabled ? "开启" : "关闭"));
        functionSettingsManager.setGameMode(enabled);
    }
    
    /**
     * 设置掉落提醒功能开关
     * @param enabled 是否启用
     */
    public void setFallAlert(boolean enabled) {
        YoBTSDKLog.i(TAG, "命令: 设置掉落提醒 " + (enabled ? "开启" : "关闭"));
        functionSettingsManager.setFallAlert(enabled);
    }
    
    /**
     * 设置寻找耳机功能开关
     * @param enabled 是否启用
     */
    public void setEarLocation(boolean enabled) {
        YoBTSDKLog.i(TAG, "命令: 设置寻找耳机 " + (enabled ? "开启" : "关闭"));
        functionSettingsManager.setEarLocation(enabled);
    }
    
    /**
     * 触发左耳响铃或停止
     * @param start true为响铃, false为停止
     */
    public void findAlertLeft(boolean start) {
        functionSettingsManager.findAlertLeft(start);
    }
    
    /**
     * 触发右耳响铃或停止
     * @param start true为响铃, false为停止
     */
    public void findAlertRight(boolean start) {
        functionSettingsManager.findAlertRight(start);
    }
    
    //=========== 控制设置相关命令 ===========
    
    /**
     * 设置触控控制功能
     * @param earSide 耳机侧（0-左耳，1-右耳）
     * @param tapType 点击类型（0-双击，1-三击）
     * @param function 功能代码
     */
    public void setControlFunction(int earSide, int tapType, int function) {
        YoBTSDKLog.i(TAG, "命令: 设置触控控制 侧:" + earSide + " 点击:" + tapType + " 功能:" + function);
        controlSettingsManager.setControl(earSide, tapType, function);
    }
    
    /**
     * 恢复默认控制设置
     */
    public void resetControlSettings() {
        YoBTSDKLog.i(TAG, "命令: 恢复默认控制设置");
        controlSettingsManager.resetControls(true);
    }
    
    //=========== 电池电量相关命令 ===========
    
    /**
     * 获取所有电池电量信息
     */
    public void getBatteryInfo() {
        YoBTSDKLog.i(TAG, "命令: 获取所有电池电量");
        batterySettingsManager.getAllBatteryLevels();
    }
    
    /**
     * 获取充电盒充电状态
     */
    public void getCaseChargingStatus() {
        YoBTSDKLog.i(TAG, "命令: 获取充电盒充电状态");
        batterySettingsManager.requestCaseChargingStatus();
    }
    
    /**
     * 获取所有电池信息（包括电量和充电状态）
     */
    public void getFullBatteryInfo() {
        YoBTSDKLog.i(TAG, "命令: 获取完整电池信息");
        batterySettingsManager.getFullBatteryInfo();
    }
    
    /**
     * 获取耳机在充电盒内状态
     */
    public void requestEarInCaseStatus() {
        YoBTSDKLog.i(TAG, "命令: 获取耳机在充电盒内状态");
        batterySettingsManager.requestEarInCaseStatus();
    }
    
    
    /**
     * 获取左耳电池电量
     * @return 电池电量字符串（百分比数字或"--"）
     */
    public String getLeftBatteryLevel() {
        return batterySettingsManager.getLeftBatteryLevel();
    }
    
    /**
     * 获取右耳电池电量
     * @return 电池电量字符串（百分比数字或"--"）
     */
    public String getRightBatteryLevel() {
        return batterySettingsManager.getRightBatteryLevel();
    }
    
    /**
     * 获取充电仓电池电量
     * @return 电池电量字符串（百分比数字或"--"）
     */
    public String getCaseBatteryLevel() {
        return batterySettingsManager.getCaseBatteryLevel();
    }
    
    /**
     * 获取充电盒是否正在充电
     * @return 是否正在充电
     */
    public boolean isCaseCharging() {
        return batterySettingsManager.isCaseCharging();
    }
    
    /**
     * 获取耳机在充电盒内状态
     * @return 状态值 (0:都不在盒子内，1:左耳在，2:右耳在，3:都在盒子内)
     */
    public int getEarInCaseStatus() {
        return batterySettingsManager.getEarInCaseStatus();
    }
    
    /**
     * 左耳是否在充电盒内
     * @return 是否在充电盒内
     */
    public boolean isLeftEarInCase() {
        return batterySettingsManager.isLeftEarInCase();
    }
    
    /**
     * 右耳是否在充电盒内
     * @return 是否在充电盒内
     */
    public boolean isRightEarInCase() {
        return batterySettingsManager.isRightEarInCase();
    }
    
    //=========== 应用设置相关命令 ===========
    
    /**
     * 获取耳机颜色
     */
    public void getEarColor() {
        YoBTSDKLog.i(TAG, "命令: 获取耳机颜色");
        deviceSettingsManager.getEarColor();
    }
    
    /**
     * 清除配对记录
     */
    public void clearPairing() {
        YoBTSDKLog.i(TAG, "命令: 清除配对记录");
        deviceSettingsManager.clearPairing();
    }
    
    /**
     * 获取固件版本
     */
    public void getFirmwareVersion() {
        YoBTSDKLog.i(TAG, "命令: 获取固件版本");
        deviceSettingsManager.getFirmwareVersion();
    }
    
    /**
     * 修改耳机名称
     * @param deviceName 要设置的设备名称
     */
    public void setDeviceName(String deviceName) {
        YoBTSDKLog.i(TAG, "命令: 修改耳机名称 " + deviceName);
        deviceSettingsManager.setDeviceName(deviceName);
    }
    
    /**
     * 获取经典蓝牙（SPP）MAC地址
     */
    public void getClassicBtAddress() {
        YoBTSDKLog.i(TAG, "命令: 获取经典蓝牙MAC地址");
        deviceSettingsManager.getClassicBtAddress();
    }
    
    /**
     * 获取设备型号名称
     */
    public void getDeviceModelName() {
        YoBTSDKLog.i(TAG, "命令: 获取设备型号名称");
        deviceSettingsManager.getDeviceName();
    }
    
    /**
     * 获取设备全局状态
     * 一次获取耳机的所有功能状态，包括各种开关和模式设置
     */
    public void getGlobalStatus() {
        // 调用设备设置管理器获取全局状态
        deviceSettingsManager.getGlobalStatus();
    }
}