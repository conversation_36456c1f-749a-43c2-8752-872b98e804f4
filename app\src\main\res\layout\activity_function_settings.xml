<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/function_settings_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/comp_background_gray"
    android:fitsSystemWindows="false"
    tools:context=".activity.FunctionSettingsActivity">

    <!-- 顶部导航栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/nav_bar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/comp_background_gray"
        android:elevation="0dp"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="4dp"
            android:padding="12dp"
            android:src="@drawable/ic_public_back"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="功能设置"
            android:textColor="@color/font_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 内容区域 - 使用ScrollView确保所有内容可滚动 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@+id/nav_bar"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 功能设置标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="功能设置"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/font_primary"
                android:layout_marginBottom="16dp"/>

            <!-- 佩戴检测 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="佩戴检测"
                        android:textSize="16sp"
                        android:textColor="@color/font_primary"/>

                    <com.ggec.hs01.view.HMSwitch
                        android:id="@+id/switch_wear_detection"
                        style="@style/StandardSwitch" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 语音唤醒（VAD）-->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="语音唤醒"
                        android:textSize="16sp"
                        android:textColor="@color/font_primary"/>

                    <com.ggec.hs01.view.HMSwitch
                        android:id="@+id/switch_vad_wakeup"
                        style="@style/StandardSwitch" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设置: 开/关 (默认: 开)"
                android:textSize="12sp"
                android:textColor="@color/font_secondary"
                android:layout_marginTop="-8dp"
                android:layout_marginBottom="16dp"/>

            <!-- 游戏模式 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="游戏模式"
                        android:textSize="16sp"
                        android:textColor="@color/font_primary"/>

                    <com.ggec.hs01.view.HMSwitch
                        android:id="@+id/switch_game_mode"
                        style="@style/StandardSwitch" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设置: 开/关 (默认: 关)"
                android:textSize="12sp"
                android:textColor="@color/font_secondary"
                android:layout_marginTop="-8dp"
                android:layout_marginBottom="16dp"/>

            <!-- 掉落提醒 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="掉落提醒"
                        android:textSize="16sp"
                        android:textColor="@color/font_primary"/>

                    <com.ggec.hs01.view.HMSwitch
                        android:id="@+id/switch_fall_alert"
                        style="@style/StandardSwitch" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设置: 开/关 (默认: 开)"
                android:textSize="12sp"
                android:textColor="@color/font_secondary"
                android:layout_marginTop="-8dp"
                android:layout_marginBottom="16dp"/>

            <!-- 寻找耳机 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="寻找功能设置"
                        android:textSize="16sp"
                        android:textColor="@color/font_primary"
                        android:layout_marginBottom="12dp"/>

                    <!-- 寻找耳机 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="寻找耳机"
                            android:textSize="14sp"
                            android:textColor="@color/font_primary"/>

                        <com.ggec.hs01.view.HMSwitch
                            android:id="@+id/switch_ear_location"
                            style="@style/StandardSwitch" />
                    </LinearLayout>

                    <!-- 寻找提示音 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center_vertical"
                        android:layout_marginTop="10dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="寻找提示音"
                            android:textSize="16sp"
                            android:textColor="@color/font_primary"
                            android:layout_marginBottom="16dp"/>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="开启开关让对应耳机响铃"
                            android:textSize="14sp"
                            android:textColor="@color/font_secondary"
                            android:layout_marginBottom="12dp"/>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:paddingVertical="8dp">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="左耳响铃"
                                    android:textSize="14sp"
                                    android:textColor="@color/font_primary"/>

                                <com.ggec.hs01.view.HMSwitch
                                    android:id="@+id/switch_find_alert_left"
                                    style="@style/StandardSwitch" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:paddingVertical="8dp">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="右耳响铃"
                                    android:textSize="14sp"
                                    android:textColor="@color/font_primary"/>

                                <com.ggec.hs01.view.HMSwitch
                                    android:id="@+id/switch_find_alert_right"
                                    style="@style/StandardSwitch" />
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设置: 开/关 (默认: 开)"
                android:textSize="12sp"
                android:textColor="@color/font_secondary"
                android:layout_marginTop="-8dp"
                android:layout_marginBottom="16dp"/>

        </LinearLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout> 