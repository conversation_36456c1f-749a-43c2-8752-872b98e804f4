package com.ggec.bleservice.gatt;

import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattDescriptor;
import android.bluetooth.BluetoothGattService;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.os.Build;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import com.ggec.yobtsdkserver.sdkmanager.BTPermissionManager;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * GATT连接管理类
 * 处理GATT连接、断开连接、服务发现和特征值操作 处理蓝牙数据的发送和接收
 */
public class GattConnectionManager {
    private static final String TAG = "GattConnectionManager";
    private static final UUID CLIENT_CHARACTERISTIC_CONFIG = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb");
    
    private Context mContext;
    private BluetoothGatt mBluetoothGatt;
    private GattConnectionCallback mCallback;
    private int mCurrentMtu = 23; // 默认MTU

    private BluetoothDevice mBluetoothDevice;

    public GattConnectionManager(Context context) {
        this.mContext = context;
    }
    
    /**
     * 设置连接回调
     * @param callback 回调接口
     */
    public void setConnectionCallback(GattConnectionCallback callback) {
        this.mCallback = callback;
    }

    public BluetoothDevice getBluetoothDevice(){
        return mBluetoothDevice;
    }

    /**
     * 连接设备
     * @param device 蓝牙设备
     * @return 是否成功发起连接
     */
    public boolean connect(final BluetoothDevice device) {
        if (device == null) {
            YoBTSDKLog.w(TAG, "设备为空");
            return false;
        }
        
        // 如果已连接，先断开
        if (mBluetoothGatt != null) {
            disconnect();
        }
        
        // 检查权限
        if (!hasRequiredPermissions()) {
            return false;
        }
        mBluetoothDevice = device;
        // 连接GATT服务器
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                mBluetoothGatt = device.connectGatt(mContext, false, mGattCallback, BluetoothDevice.TRANSPORT_LE);
                YoBTSDKLog.d(TAG, "发起GATT连接设备: " + device.getAddress());
            } else {
                YoBTSDKLog.d(TAG, "发起GATT连接设备: " + device.getAddress());
                mBluetoothGatt = device.connectGatt(mContext, false, mGattCallback);
            }
        } catch (SecurityException e) {
            YoBTSDKLog.e(TAG, "连接GATT服务器时出现安全异常", e);
            mBluetoothGatt = null;
            return false;
        }
        
        YoBTSDKLog.d(TAG, "连接设备: " + device.getAddress());
        return true;
    }
    
    /**
     * 断开连接
     */
    public void disconnect() {
        if (!hasRequiredPermissions()) {
            return;
        }
        
        if (mBluetoothGatt != null) {
            try {
                mBluetoothGatt.disconnect();
                mBluetoothDevice = null;
            } catch (SecurityException e) {
                YoBTSDKLog.e(TAG, "断开连接时出现安全异常", e);
            }
        }
    }
    
    /**
     * 检查是否已连接
     * @return 是否已连接
     */
    public boolean isConnected() {
        return mBluetoothGatt != null;
    }
    
    /**
     * 关闭GATT客户端
     */
    public void close() {
        if (mBluetoothGatt != null) {
            try {
                if (hasRequiredPermissions()) {
                mBluetoothGatt.close();
                }
                mBluetoothGatt = null;
            } catch (SecurityException e) {
                YoBTSDKLog.e(TAG, "关闭GATT客户端时出现安全异常", e);
                mBluetoothGatt = null;
            }
        }
    }
    
    /**
     * 发现服务
     * @return 是否成功发起发现
     */
    public boolean discoverServices() {
        if (!hasRequiredPermissions() || mBluetoothGatt == null) {
            return false;
        }
        
            try {
                return mBluetoothGatt.discoverServices();
            } catch (SecurityException e) {
                YoBTSDKLog.e(TAG, "发现服务时出现安全异常", e);
                return false;
            }
    }
    
    /**
     * 获取服务列表
     * @return 服务列表
     */
    public List<BluetoothGattService> getServices() {
        if (!hasRequiredPermissions() || mBluetoothGatt == null) {
            return new ArrayList<>();
        }
        
            try {
                return mBluetoothGatt.getServices();
            } catch (SecurityException e) {
                YoBTSDKLog.e(TAG, "获取服务列表时出现安全异常", e);
                return new ArrayList<>();
            }
    }
    
    /**
     * 读取特征值
     * @param serviceUUID 服务UUID
     * @param characteristicUUID 特征值UUID
     * @return 是否成功发起读取
     */
    public boolean readCharacteristic(UUID serviceUUID, UUID characteristicUUID) {
        if (!hasRequiredPermissions() || mBluetoothGatt == null) {
            return false;
        }
        
        BluetoothGattService service = mBluetoothGatt.getService(serviceUUID);
        if (service == null) {
            YoBTSDKLog.e(TAG, "服务不存在: " + serviceUUID);
            return false;
        }
        
        BluetoothGattCharacteristic characteristic = service.getCharacteristic(characteristicUUID);
        if (characteristic == null) {
            YoBTSDKLog.e(TAG, "特征值不存在: " + characteristicUUID);
            return false;
        }
        
        try {
            return mBluetoothGatt.readCharacteristic(characteristic);
        } catch (SecurityException e) {
            YoBTSDKLog.e(TAG, "读取特征值时出现安全异常", e);
            return false;
        }
    }
    
    /**
     * 写入特征值
     * @param serviceUUID 服务UUID
     * @param characteristicUUID 特征值UUID
     * @param value 写入的数据
     * @return 是否成功发起写入
     */
    public boolean writeCharacteristic(UUID serviceUUID, UUID characteristicUUID, byte[] value) {
        if (!hasRequiredPermissions() || mBluetoothGatt == null) {
            return false;
        }
        
        BluetoothGattService service = mBluetoothGatt.getService(serviceUUID);
        if (service == null) {
            YoBTSDKLog.e(TAG, "服务不存在: " + serviceUUID);
            return false;
        }
        
        BluetoothGattCharacteristic characteristic = service.getCharacteristic(characteristicUUID);
        if (characteristic == null) {
            YoBTSDKLog.e(TAG, "特征值不存在: " + characteristicUUID);
            return false;
        }
        
        try {
            characteristic.setValue(value);
            
            // 在Android 12 (SDK 31)及以上版本中可能存在新的API，但我们先使用兼容性更好的方法
            return mBluetoothGatt.writeCharacteristic(characteristic);
        } catch (SecurityException e) {
            YoBTSDKLog.e(TAG, "写入特征值时出现安全异常", e);
            return false;
        }
    }
    
    /**
     * 获取当前MTU大小
     * @return 当前MTU大小
     */
    public int getCurrentMtu() {
        return mCurrentMtu;
    }
    
    /**
     * 请求更新MTU大小
     * @param mtu 请求的MTU大小
     * @return 是否成功发起请求
     */
    public boolean requestMtu(int mtu) {
        if (!hasRequiredPermissions() || mBluetoothGatt == null) {
            return false;
        }
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                return mBluetoothGatt.requestMtu(mtu);
            }
        } catch (SecurityException e) {
            YoBTSDKLog.e(TAG, "请求MTU时出现安全异常", e);
        }
        return false;
    }

    /**
     * 设置特征值通知
     * @param serviceUUID 服务UUID
     * @param characteristicUUID 特征值UUID
     * @param enable 是否启用通知
     * @return 是否成功设置
     */
    public boolean setCharacteristicNotification(UUID serviceUUID, UUID characteristicUUID, boolean enable) {
        if (!hasRequiredPermissions() || mBluetoothGatt == null) {
            return false;
        }
        
        BluetoothGattService service = mBluetoothGatt.getService(serviceUUID);
        if (service == null) {
            YoBTSDKLog.e(TAG, "服务不存在: " + serviceUUID);
            return false;
        }
        
        BluetoothGattCharacteristic characteristic = service.getCharacteristic(characteristicUUID);
        if (characteristic == null) {
            YoBTSDKLog.e(TAG, "特征值不存在: " + characteristicUUID);
            return false;
        }
        
        try {
            if (!mBluetoothGatt.setCharacteristicNotification(characteristic, enable)) {
                YoBTSDKLog.e(TAG, "设置特征值通知失败");
                return false;
            }
            
            // 配置描述符
            BluetoothGattDescriptor descriptor = characteristic.getDescriptor(CLIENT_CHARACTERISTIC_CONFIG);
            if (descriptor != null) {
                descriptor.setValue(enable ? BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE : BluetoothGattDescriptor.DISABLE_NOTIFICATION_VALUE);
                return mBluetoothGatt.writeDescriptor(descriptor);
            }
            
            return true;
        } catch (SecurityException e) {
            YoBTSDKLog.e(TAG, "设置特征值通知时出现安全异常", e);
            return false;
        }
    }
    
    /**
     * 检查是否具有所需的蓝牙权限
     * @return 是否具有权限
     */
    private boolean hasRequiredPermissions() {
        return BTPermissionManager.hasBluetoothPermissions(mContext);
    }
    
    /**
     * GATT回调
     */
    private final BluetoothGattCallback mGattCallback = new BluetoothGattCallback() {
        @Override
        public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
            BluetoothDevice device = gatt.getDevice();
            if (newState == BluetoothProfile.STATE_CONNECTED) {
                YoBTSDKLog.i(TAG, "设备已连接: " + device.getAddress());
            } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                YoBTSDKLog.i(TAG, "设备已断开: " + device.getAddress());
                close();
            }

            if (mCallback != null) {
                mCallback.onConnectionStateChange(device, status, newState);
            }
        }
        
        @Override
        public void onServicesDiscovered(BluetoothGatt gatt, int status) {
            mBluetoothGatt = gatt;
            if (mCallback != null) {
                BluetoothDevice device = gatt.getDevice();
                gatt.requestMtu(512);
                mCallback.onServicesDiscovered(device, gatt.getServices(), status);
            }
        }
        
        @Override
        public void onCharacteristicRead(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
            if (mCallback != null) {
                BluetoothDevice device = gatt.getDevice();
                UUID serviceUuid = characteristic.getService().getUuid();
                UUID characteristicUuid = characteristic.getUuid();
                byte[] value = characteristic.getValue();
                
                mCallback.onCharacteristicRead(device, serviceUuid, characteristicUuid, value, status);
            }
        }
        
        @Override
        public void onCharacteristicWrite(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
            if (mCallback != null) {
                BluetoothDevice device = gatt.getDevice();
                UUID serviceUuid = characteristic.getService().getUuid();
                UUID characteristicUuid = characteristic.getUuid();
                
                mCallback.onCharacteristicWrite(device, serviceUuid, characteristicUuid, status);
            }
        }
        
        @Override
        public void onCharacteristicChanged(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic) {
            if (mCallback != null) {
                mCallback.onCharacteristicChanged(
                    gatt.getDevice(), 
                    characteristic.getService().getUuid(), 
                    characteristic.getUuid(), 
                    characteristic.getValue());
            }
        }
        
        @Override
        public void onMtuChanged(BluetoothGatt gatt, int mtu, int status) {
            mCurrentMtu = mtu;
            YoBTSDKLog.d(TAG, "MTU changed: " + mtu + " status: " + status);
            if (mCallback != null) {
                mCallback.onMtuChanged(gatt.getDevice(), mtu, status);
            }
        }

        @Override
        public void onDescriptorWrite(BluetoothGatt gatt, BluetoothGattDescriptor descriptor, int status) {
            super.onDescriptorWrite(gatt, descriptor, status);
            if (mCallback != null) {
                BluetoothDevice device = gatt.getDevice();
                UUID serviceUuid = descriptor.getCharacteristic().getService().getUuid();
                UUID characteristicUuid = descriptor.getCharacteristic().getUuid();
                mCallback.onNotificationEnabled(device, serviceUuid, characteristicUuid, status);
            }
        }
    };
} 