# 这个文件定义了使用该SDK的应用需要添加的混淆规则

# 只保留三个主要API类
#-keep public class com.ggec.bleservice.YoBLEInitializer { public *; protected *; }
-keep public class com.ggec.bleservice.YoBLEApi { public *; protected *; }
-keep public class com.ggec.bleservice.YoCommandApi { public *; protected *; }
-keep public class com.ggec.sppservice.YoSPPApi { public *; protected *; }

# 保留这三个类中定义的接口和枚举
-keep public interface com.ggec.bleservice.YoBLEInitializer$* { *; }
-keep public interface com.ggec.bleservice.YoBLEApi$* { *; }
-keep public interface com.ggec.bleservice.YoCommandApi$* { *; }
-keep public interface com.ggec.sppservice.YoSPPApi$* { *; }
-keep public enum com.ggec.bleservice.YoBLEInitializer$* { *; }
-keep public enum com.ggec.bleservice.YoBLEApi$* { *; }
-keep public enum com.ggec.bleservice.YoCommandApi$* { *; }
-keep public enum com.ggec.sppservice.YoSPPApi$* { *; }

# 保留API类中的常量

-keepclassmembers class com.ggec.bleservice.YoBLEApi {
    public static final *;
}
-keepclassmembers class com.ggec.bleservice.YoCommandApi {
    public static final *;
}
-keepclassmembers class com.ggec.sppservice.YoSPPApi {
    public static final *;
}

# 保留蓝牙相关Android API
-keep class android.bluetooth.** { *; }
-keep interface android.bluetooth.** { *; }

# 保留ScanConfig类，因为它被YoBLEApi直接引用
-keep class com.ggec.bleservice.scanner.ScanConfig { *; }

# 保留SPP模块中API引用的配置类和回调接口
-keep class com.ggec.sppservice.check.YoSPPMacDeviceChecker$DeviceConnectionResult { *; }

# 保留CustomLogHandler接口，因为它被YoBLEApi直接引用
-keep class com.ggec.bleservice.manager.BleLogManager$CustomLogHandler { *; }

# 不混淆native方法
-keepclasseswithmembernames class * {
    native <methods>;
}

# 防止警告
-dontwarn com.ggec.bleservice.**
-dontwarn com.ggec.sppservice.**
-dontwarn android.bluetooth.**
