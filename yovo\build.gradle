plugins {
    alias(libs.plugins.android.library)
}

android {
    namespace 'com.yovo.yotheme'
    compileSdk 33

    defaultConfig {
        minSdk 24
        targetSdk 33
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro','consumer-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    lintOptions {
        baseline file("lint-baseline.xml")
        abortOnError false
    }
    
    // 将发布任务配置为生成AAR
    publishing {
        singleVariant("release") {
            withSourcesJar()
            withJavadocJar()
        }
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.2.1'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
}

// 配置发布AAR到本地的task
tasks.register('exportReleaseAar', Copy) {
    dependsOn("assembleRelease")
    from("${buildDir}/outputs/aar")
    include("yovo-release.aar")
    into("${rootProject.projectDir}/releases")
    rename { String fileName ->
        "yovo-sdk.aar"
    }
}