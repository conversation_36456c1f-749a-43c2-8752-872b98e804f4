# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# 默认混淆设置
-allowaccessmodification

# 保留对外暴露的公共API类
-keep public class com.ggec.yobtsdkserver.YoBTInit { public *; protected *; }
-keep public class com.ggec.bleservice.YoBLEApi { public *; protected *; }
-keep public class com.ggec.bleservice.YoCommandApi { public *; protected *; }
-keep public class com.ggec.sppservice.YoSPPApi { public *; protected *; }

# 保留公共API类中定义的内部接口和枚举，用于回调
-keep public class com.ggec.yobtsdkserver.YoBTInit$* { *; }
-keep public class com.ggec.bleservice.YoBLEApi$* { *; }
-keep public class com.ggec.bleservice.YoCommandApi$* { *; }
-keep public class com.ggec.sppservice.YoSPPApi$* { *; }

# 保留蓝牙相关Android API
-keep class android.bluetooth.** { *; }
-keep interface android.bluetooth.** { *; }

# 保留公共API类中引用到的常量
-keepclassmembers class com.ggec.yobtsdkserver.YoBTInit {
    public static final *;
}
-keepclassmembers class com.ggec.bleservice.YoBLEApi {
    public static final *;
}
-keepclassmembers class com.ggec.bleservice.YoCommandApi {
    public static final *;
}
-keepclassmembers class com.ggec.sppservice.YoSPPApi {
    public static final *;
}

# 保留注解
-keepattributes *Annotation*

# 保留序列化相关
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 保留枚举类及其值
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保留SDK内部使用的一些关键接口和回调
-keep class com.ggec.bleservice.scanner.IScanner { *; }
-keep class com.ggec.bleservice.scanner.ScanCallback { *; }
-keep class com.ggec.sppservice.scanner.SppScanCallback { *; }
-keep class com.ggec.sppservice.connect.SppConnectCallback { *; }
-keep class com.ggec.bleservice.gatt.GattConnectionCallback { *; }
-keep class com.ggec.bleservice.core.BleDataService$BleDataCallback { *; }
-keep class com.ggec.bleservice.core.BleEventDispatcher$BleEventListener { *; }

# 保留SDK中可能被公共API引用的数据模型和配置类
-keep class com.ggec.bleservice.scanner.ScanConfig { *; }
-keep class com.ggec.sppservice.scanner.SppScanConfig { *; }
-keep class com.ggec.sppservice.connect.SppConnectConfig { *; }

# 防止混淆可能通过反射访问的内部类方法
-keepclassmembers class com.ggec.bleservice.** {
    @android.annotation.** *;
}
-keepclassmembers class com.ggec.sppservice.** {
    @android.annotation.** *;
}
-keepclassmembers class com.ggec.yobtsdkserver.** {
    @android.annotation.** *;
}

# 保留行号信息，方便调试
-keepattributes SourceFile,LineNumberTable

# 保留异常信息
-renamesourcefileattribute SourceFile

# 不混淆native方法
-keepclasseswithmembernames class * {
    native <methods>;
}

# 不警告未引用的库
-dontwarn javax.**
-dontwarn org.apache.**
-dontwarn com.ggec.sppservice.**
-dontwarn android.bluetooth.**
-repackageclasses 'com.ggec.yobtservice'