package com.ggec.bleservice.core;

import java.util.UUID;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 蓝牙常量类
 * 定义蓝牙相关的UUID、超时时间和其他配置参数
 */
public class BleConstants {

    // 服务UUID
    //public static final UUID SERVICE_UUID = UUID.fromString("65786365-6c70-6f69-6e74-2e636f820000");
    public static final UUID SERVICE_UUID = UUID.fromString("65786365-6c70-6f69-6e74-2e636f820100");
    
    // 写特征值UUID
    //public static final UUID WRITE_CHARACTERISTIC_UUID = UUID.fromString("65786365-6c70-6f69-6e74-2e636f820004");
    public static final UUID WRITE_CHARACTERISTIC_UUID = UUID.fromString("65786365-6c70-6f69-6e74-2e636f820104");
    
    // 读特征值UUID
    //public static final UUID READ_CHARACTERISTIC_UUID = UUID.fromString("65786365-6c70-6f69-6e74-2e636f820005");
    public static final UUID READ_CHARACTERISTIC_UUID = UUID.fromString("65786365-6c70-6f69-6e74-2e636f820105");
    
    // 客户端特征配置描述符UUID
    public static final UUID CLIENT_CHARACTERISTIC_CONFIG = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb");
    
    // 扫描超时时间（毫秒）
    public static final int DEFAULT_SCAN_TIMEOUT = 10000;
    
    // 连接超时时间（毫秒）
    public static final int CONNECTION_TIMEOUT = 15000;
    
    // 数据重复过滤时间（毫秒）
    public static final long DATA_DUPLICATE_FILTER_TIME = 500;
    
    // 蓝牙状态
    public static final int STATE_DISCONNECTED = 0;
    public static final int STATE_CONNECTING = 1;
    public static final int STATE_CONNECTED = 2;
    
    // Android 12 SDK版本号
    public static final int ANDROID_12_SDK = 31;
    
    // 测试数据
    public static final String TEST_DATA = "99EC800001012345";
    
    // 禁止实例化
    private BleConstants() {
        throw new AssertionError("不能实例化BleConstants");
    }
} 