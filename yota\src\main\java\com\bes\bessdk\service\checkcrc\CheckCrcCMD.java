package com.bes.bessdk.service.checkcrc;

import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.CmdInfo;

public class
CheckCrcCMD {
    static String TAG = "CheckCrcCMD";
    static byte[] magicCode = new byte[]{0x42, 0x45, 0x53, 0x54};

    public static byte[] getCrcCMD() {
        CmdInfo getCrcInfo = new CmdInfo(CheckCrcConstants.OP_TOTA_CHECK_CRC_DATA, magicCode);
        return getCrcInfo.toBytes();
    }

    public static byte[] getFactoryReset() {
        CmdInfo getFactoryResetInfo = new CmdInfo(CheckCrcConstants.OP_TOTA_FACTORY_RESET, new byte[0]);
        return getFactoryResetInfo.toBytes();
    }

    public static String receiveData(byte[] data) {
        if (data.length > 4) {
            byte[] info = data;
                    //new byte[data.length - 4];
//            for (int i = 0; i < (data.length - 4); i ++) {
//                info[i] = data[4 + i];
//            }
            CmdInfo crc_rsp = new CmdInfo(CheckCrcConstants.OP_TOTA_CHECK_CRC_RSP, new byte[0]);
            CmdInfo crc_data = new CmdInfo(CheckCrcConstants.OP_TOTA_CHECK_CRC_DATA, new byte[0]);
            if (crc_rsp.toBytes()[0] == info[0] && crc_rsp.toBytes()[1] == info[1] && crc_data.toBytes()[0] == info[4] && crc_data.toBytes()[1] == info[5]) {
                byte[] crc = new byte[4];
                System.arraycopy(info, 10 + 0, crc, 0 , 4);
                byte[] version = new byte[4];
                System.arraycopy(info, 10 + 0 + 4, version, 0 , 4);
                byte[] buildData = new byte[32];
                System.arraycopy(info, 10 + 0 + 4 + 4, buildData, 0 , 32);

                String crcStr = ArrayUtil.toHex(crc).replace(",", "");
                String versionStr = ArrayUtil.toHex(version).replace(",", ".");
                String buildDataStr = ArrayUtil.toASCII(buildData).replace("-", " ");
                return "CRC: 0x" + crcStr + "\n"
                        + "Version: " + versionStr + "\n"
                        + "Build Data: " + buildDataStr;
            }
        }
       return "";
    }

}