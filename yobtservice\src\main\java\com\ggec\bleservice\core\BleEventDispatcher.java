package com.ggec.bleservice.core;

import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGattService;
import android.bluetooth.BluetoothProfile;
import android.os.Handler;
import android.os.Looper;

import com.ggec.yobtsdkserver.utils.HexUtil;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CopyOnWriteArrayList;

/** 
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 蓝牙事件分发器
 * 集中处理所有蓝牙相关事件，并向注册的监听器分发事件
 * 使用事件总线模式简化多层回调嵌套
 */
public class BleEventDispatcher {
    private static final String TAG = "BleEventDispatcher";
    
    private static BleEventDispatcher instance;
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    // 线程安全的监听器列表
    private final CopyOnWriteArrayList<BleEventListener> eventListeners = new CopyOnWriteArrayList<>();
    
    /**
     * 蓝牙事件监听器统一接口
     * 所有事件类型在此接口定义，减少多层回调嵌套
     */
    public interface BleEventListener {
        /**
         * 设备连接状态变更
         * @param device 蓝牙设备
         * @param status 状态码，0表示成功
         * @param newState 新连接状态，可参考BluetoothProfile.STATE_*常量
         */
        default void onConnectionStateChanged(BluetoothDevice device, int status, int newState) {}
        
        /**
         * 服务发现完成
         * @param device 蓝牙设备
         * @param services 发现的服务列表
         * @param status 状态码，0表示成功
         */
        default void onServicesDiscovered(BluetoothDevice device, List<BluetoothGattService> services, int status) {}
        
        /**
         * 发现蓝牙设备
         * @param device 发现的蓝牙设备
         * @param rssi 信号强度
         * @param scanRecord 广播数据记录
         */
        default void onDeviceFound(BluetoothDevice device, int rssi, byte[] scanRecord) {}
        
        /**
         * 扫描开始
         */
        default void onScanStarted() {}
        
        /**
         * 扫描结束
         */
        default void onScanFinished() {}
        
        /**
         * 特征值读取完成
         * @param device 蓝牙设备
         * @param serviceUUID 服务UUID
         * @param characteristicUUID 特征值UUID
         * @param value 读取到的字节数据
         * @param hexValue 读取到的十六进制字符串
         * @param status 状态码，0表示成功
         */
        default void onCharacteristicRead(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, 
                                         byte[] value, String hexValue, int status) {}
        
        /**
         * 特征值写入完成
         * @param device 蓝牙设备
         * @param serviceUUID 服务UUID
         * @param characteristicUUID 特征值UUID
         * @param status 状态码，0表示成功
         */
        default void onCharacteristicWrite(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, int status) {}
        
        /**
         * 特征值变化通知
         * @param device 蓝牙设备
         * @param serviceUUID 服务UUID
         * @param characteristicUUID 特征值UUID
         * @param value 变化的字节数据
         * @param hexValue 变化的十六进制字符串
         */
        default void onCharacteristicChanged(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, 
                                            byte[] value, String hexValue) {}
                                            
        /**
         * 设备就绪状态变化
         * 当设备连接、服务发现和特征值通知启用后调用
         * @param ready 设备是否就绪
         */
        default void onDeviceReady(boolean ready) {}
        
        /**
         * MTU变更事件
         * @param device 蓝牙设备
         * @param mtu 协商后的MTU大小
         * @param status 状态码，0表示成功
         */
        default void onMtuChanged(BluetoothDevice device, int mtu, int status) {}

        /**
         * 通知启用/禁用状态变化回调
         * 当setCharacteristicNotification成功写入描述符后触发
         * @param device 蓝牙设备
         * @param serviceUUID 服务UUID
         * @param characteristicUUID 特征值UUID
         * @param status 状态码
         */
        default void onNotificationEnabled(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, int status) {}

        /**
         * 系统蓝牙关闭事件
         */
        default void systemBluetoothClose() {}

        /**
         * 系统蓝牙开启事件
         */
        default void systemBluetoothOpen() {}
    }
    
    /**
     * 获取BleEventDispatcher单例
     */
    public static BleEventDispatcher getInstance() {
        if (instance == null) {
            synchronized (BleEventDispatcher.class) {
                if (instance == null) {
                    instance = new BleEventDispatcher();
                }
            }
        }
        return instance;
    }
    
    private BleEventDispatcher() {
        // 私有构造函数
    }
    
    /**
     * 初始化事件分发器
     */
    public void init() {
        YoBTSDKLog.i(TAG, "蓝牙事件分发器初始化完成");
    }
    
    /**
     * 释放资源
     */
    public void release() {
        eventListeners.clear();
        instance = null;
        mainHandler.removeCallbacksAndMessages(null);
        YoBTSDKLog.i(TAG, "蓝牙事件分发器资源已释放");
    }
    
    /**
     * 注册事件监听器
     * @param listener 监听器
     */
    public void registerEventListener(BleEventListener listener) {
        if (listener != null && !eventListeners.contains(listener)) {
            eventListeners.add(listener);
            
            // 如果已经连接，立即通知新注册的监听器
            BluetoothDevice device = BleManager.getInstance().getConnectedDevice();
            if (device != null) {
                mainHandler.post(() -> listener.onConnectionStateChanged(device, 0, BleConstants.STATE_CONNECTED));
            }
        }
    }
    
    /**
     * 注销事件监听器
     * @param listener 监听器
     */
    public void unregisterEventListener(BleEventListener listener) {
        if (listener != null) {
            eventListeners.remove(listener);
        }
    }
    
    /**
     * 检查是否包含指定的事件监听器
     * @param listener 监听器
     * @return 是否包含该监听器
     */
    public boolean hasEventListener(BleEventListener listener) {
        return listener != null && eventListeners.contains(listener);
    }
    
    /**
     * 发送事件 - 连接状态变化
     */
    public void dispatchConnectionStateChanged(BluetoothDevice device, int status, int newState) {
        // 记录日志
        if (status == 0) {
            if (newState == BluetoothProfile.STATE_CONNECTED) {
                YoBTSDKLog.i(TAG, "设备连接成功: " + device.getAddress());
            } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                YoBTSDKLog.i(TAG, "设备已断开连接: " + device.getAddress());
            }
        } else {
            //YoBTSDKLog.e(TAG, "连接状态变化出错，状态: " + status);
        }
        
        // 通知所有监听器
        for (BleEventListener listener : eventListeners) {
            mainHandler.post(() -> listener.onConnectionStateChanged(device, status, newState));
        }
    }
    
    /**
     * 发送事件 - 服务发现
     */
    public void dispatchServicesDiscovered(BluetoothDevice device, List<BluetoothGattService> services, int status) {
        // 记录日志
        if (status == 0) {
            YoBTSDKLog.i(TAG, "服务发现成功，共 " + services.size() + " 个服务");
        } else {
            YoBTSDKLog.e(TAG, "服务发现失败，状态: " + status);
        }
        
        // 通知所有监听器
        for (BleEventListener listener : eventListeners) {
            mainHandler.post(() -> listener.onServicesDiscovered(device, services, status));
        }
    }
    
    /**
     * 发送事件 - 设备发现
     */
    public void dispatchDeviceFound(BluetoothDevice device, int rssi, byte[] scanRecord) {
        // 通知所有监听器
        for (BleEventListener listener : eventListeners) {
            mainHandler.post(() -> listener.onDeviceFound(device, rssi, scanRecord));
        }
    }
    
    /**
     * 发送事件 - 扫描开始
     */
    public void dispatchScanStarted() {
        YoBTSDKLog.i(TAG, "蓝牙扫描开始");
        // 通知所有监听器
        for (BleEventListener listener : eventListeners) {
            mainHandler.post(listener::onScanStarted);
        }
    }
    
    /**
     * 发送事件 - 扫描结束
     */
    public void dispatchScanFinished() {
        YoBTSDKLog.i(TAG, "蓝牙扫描结束");
        // 通知所有监听器
        for (BleEventListener listener : eventListeners) {
            mainHandler.post(listener::onScanFinished);
        }
    }
    
    /**
     * 发送事件 - 特征值读取
     */
    public void dispatchCharacteristicRead(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, byte[] value, int status) {
        // 转换为十六进制字符串以便日志显示
        String hexValue = HexUtil.bytesToHexString(value);
        
        if (status == 0) {
            YoBTSDKLog.i(TAG, "特征值读取成功: " + characteristicUUID + ", 数据: " + hexValue);
        } else {
            YoBTSDKLog.e(TAG, "特征值读取失败: " + characteristicUUID + ", 状态: " + status);
        }
        
        // 通知所有监听器
        for (BleEventListener listener : eventListeners) {
            mainHandler.post(() -> listener.onCharacteristicRead(device, serviceUUID, characteristicUUID, value, hexValue, status));
        }
    }
    
    /**
     * 发送事件 - 特征值写入
     */
    public void dispatchCharacteristicWrite(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, int status) {
        // 通知所有监听器
        for (BleEventListener listener : eventListeners) {
            mainHandler.post(() -> listener.onCharacteristicWrite(device, serviceUUID, characteristicUUID, status));
        }
    }
    
    /**
     * 发送事件 - 特征值变更
     */
    public void dispatchCharacteristicChanged(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, byte[] value) {
        String hexValue = HexUtil.bytesToHexString(value);
        
        // 通知所有监听器
        for (BleEventListener listener : eventListeners) {
            mainHandler.post(() -> listener.onCharacteristicChanged(device, serviceUUID, characteristicUUID, value, hexValue));
        }
    }
    
    /**
     * 发送事件 - 设备就绪状态变化
     * @param ready 设备是否就绪
     */
    public void dispatchDeviceReady(boolean ready) {
        YoBTSDKLog.i(TAG, "设备就绪状态改变: " + ready);
        // 通知所有监听器
        for (BleEventListener listener : eventListeners) {
            mainHandler.post(() -> listener.onDeviceReady(ready));
        }
    }

    /**
     * 发送事件 - MTU变更
     */
    public void dispatchMtuChanged(BluetoothDevice device, int mtu, int status) {
        // 记录日志
        if (status == 0) {
            //YoBTSDKLog.i(TAG, "MTU变更成功: " + mtu);
        } else {
            YoBTSDKLog.e(TAG, "MTU变更失败，状态: " + status);
        }
        
        // 通知所有监听器
        for (BleEventListener listener : eventListeners) {
            mainHandler.post(() -> listener.onMtuChanged(device, mtu, status));
        }
    }

    /**
     * 分发通知启用状态变化事件
     * @param device 蓝牙设备
     * @param serviceUUID 服务UUID
     * @param characteristicUUID 特征值UUID
     * @param status 状态码
     */
    public void dispatchNotificationEnabled(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, int status) {
        // 记录日志
        if (status == 0) {
            //YoBTSDKLog.i(TAG, "通知启用成功: " + characteristicUUID);
        } else {
            YoBTSDKLog.e(TAG, "通知启用失败，状态: " + status);
        }
        
        // 通知所有监听器
        for (BleEventListener listener : eventListeners) {
            mainHandler.post(() -> listener.onNotificationEnabled(device, serviceUUID, characteristicUUID, status));
        }
    }

    /**
     * 发送事件 - 系统蓝牙关闭
     */
    public void dispatchSystemBluetoothClose() {
        YoBTSDKLog.w(TAG, "系统蓝牙已关闭");
        for (BleEventListener listener : eventListeners) {
            mainHandler.post(listener::systemBluetoothClose);
        }
    }

    /**
     * 发送事件 - 系统蓝牙开启
     */
    public void dispatchSystemBluetoothOpen() {
        YoBTSDKLog.i(TAG, "系统蓝牙已开启");
        for (BleEventListener listener : eventListeners) {
            mainHandler.post(listener::systemBluetoothOpen);
        }
    }
} 