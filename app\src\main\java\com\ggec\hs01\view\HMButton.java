package com.ggec.hs01.view;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.yovo.yotheme.YoButton;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 转发类，保持兼容性
 * 通过ThemeHelper间接访问，减少模块间耦合
 */
public class HMButton extends com.yovo.yotheme.YoButton {
    public HMButton(@NonNull Context context) {
        super(context);
    }

    public HMButton(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public HMButton(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }
} 