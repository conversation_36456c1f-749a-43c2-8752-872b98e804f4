package com.ggec.bleservice.setting.aggregatorcmd;

import com.ggec.bleservice.YoCommandApi;
import com.ggec.bleservice.setting.Command;
import com.ggec.bleservice.setting.CommandBlock;
import com.ggec.bleservice.setting.devicecmd.ClassicBtAddressCommand;
import com.ggec.bleservice.setting.devicecmd.DeviceNameGetCommand;
import com.ggec.bleservice.setting.devicecmd.EarColorCommand;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * "关于设备"页面状态聚合器
 * 负责获取"关于设备"页面需要的信息，并将其聚合成一个数据字典。
 * 采用命令块方式执行，保证时序和数据完整性。
 */
public class AboutDeviceStatusAggregator extends CommandBlock {
    private static final String TAG = "AboutDeviceStatusAggregator";

    private final YoCommandApi.AboutDeviceStatusCallback finalCallback;

    private final Map<String, Object> collectedData = new LinkedHashMap<>();
    private int step = 0;

    public AboutDeviceStatusAggregator(YoCommandApi.AboutDeviceStatusCallback callback) {
        this.finalCallback = callback;
    }

    @Override
    public Command nextCommand(Command previousCommand) {
        if (previousCommand != null) {
            // 只有当上一个命令成功完成时，才存储其结果
            if (previousCommand.getState() == Command.State.COMPLETED) {
                // 存储上一个命令的结果
                if (previousCommand instanceof DeviceNameGetCommand) {
                    collectedData.put("name", ((DeviceNameGetCommand) previousCommand).getParsedDeviceName());
                } else if (previousCommand instanceof ClassicBtAddressCommand) {
                    collectedData.put("mac", ((ClassicBtAddressCommand) previousCommand).getParsedMacAddress());
                } else if (previousCommand instanceof EarColorCommand) {
                    collectedData.put("color", String.format("%02x", ((EarColorCommand) previousCommand).getEarColor()));
                }
            } else {
                YoBTSDKLog.w(TAG, "上一个命令 " + previousCommand.getCommandId() + " 失败或未完成，跳过结果收集。");
            }
        }

        step++;
        switch (step) {
            case 1:
                //YoBTSDKLog.d(TAG, "步骤 1: 获取设备型号名称");
                return new DeviceNameGetCommand();
            case 2:
                //YoBTSDKLog.d(TAG, "步骤 2: 获取BT MAC地址");
                return new ClassicBtAddressCommand();
            case 3:
                //YoBTSDKLog.d(TAG, "步骤 3: 获取产品颜色");
                return new EarColorCommand();
            default:
                return null; // 命令块结束
        }
    }

    @Override
    public void onBlockCompleted(Map<String, Object> results) {
        YoBTSDKLog.i(TAG, "命令块执行完成");
        Map<String, String> statusMap = new LinkedHashMap<>();

        if (collectedData.containsKey("name")) {
            statusMap.put("70", (String) collectedData.get("name"));
        }
        statusMap.put("71", HSConstants.PRODUCT_MODEL);
        statusMap.put("72", HSConstants.SERIAL_NUMBER);
        if (collectedData.containsKey("mac")) {
            statusMap.put("73", (String) collectedData.get("mac"));
        }
        if (collectedData.containsKey("color")) {
            statusMap.put("74", (String) collectedData.get("color"));
        }
        statusMap.put("75", HSConstants.FEATURE_CODE);

        if (finalCallback != null) {
            finalCallback.onAboutDeviceStatusResult(Command.ResultCode.SUCCESS, statusMap);
        }
    }

    @Override
    public void onBlockFailed(int code, String message) {
        YoBTSDKLog.e(TAG, "命令块执行失败: " + message);
        if (finalCallback != null) {
            finalCallback.onAboutDeviceStatusResult(code, new LinkedHashMap<>());
        }
    }
} 