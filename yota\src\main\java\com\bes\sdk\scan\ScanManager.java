package com.bes.sdk.scan;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bes.sdk.utils.DeviceProtocol;
import com.bes.sdk.device.HmDevice;

import java.util.Collection;

/**
 * Device scan manager interface.
 */
public interface ScanManager
{
    /**
     * Check if a scan process of specified protocol is running in progress or not.
     * @param deviceProtocol
     * @return
     */
    boolean isScanInProgress(@NonNull DeviceProtocol deviceProtocol);

    /**
     * Start a device  scan process for specified protocol, with specified strategy, listener and filter.
     *
     * @param deviceProtocols
     * @param scanListener
     * @param scanFilter
     */
    void startScan(@NonNull Collection<DeviceProtocol> deviceProtocols,
                   @NonNull ScanListener scanListener, @Nullable ScanFilter scanFilter);

    /**
     * Start a device  scan process for specified protocol, with specified strategy, listener, filter and BLE broadcast parser.
     *
     * @param deviceProtocols
     * @param scanListener
     * @param scanFilter
     * @param parser
     */
    void startScan(@NonNull Collection<DeviceProtocol> deviceProtocols,
                   @NonNull ScanListener scanListener, @Nullable ScanFilter scanFilter, @Nullable BleBroadCastParser parser);

    /**
     * Stop device scan process of specified protocol.
     *
     * @param deviceProtocols
     */
    void stopScan(@NonNull Collection<DeviceProtocol> deviceProtocols);

    /**
     * Stop all device scan processes.
     */
    void stopAllScan();


    /**
     * Return all scanned devices (include master, slave), might be null
     *
     */
    Collection<HmDevice> getDeviceList();

    /**
     * Scan manager listener, defines the callback of scan events.
     */
    interface ScanListener
    {
        void onScanResult(HmDevice scannedDevice);
        /**
         * Device offline. No message received from this device in long time
         * (default 3 minutes, configurable).
         * @param device
         */
        void onDeviceOffline(HmDevice device);

        /**
         * Scan failed with message
         * @param message
         */
        void onScanFailed(String message);
    }
}
