package com.ggec.bleservice.setting.batterycmd;

import com.ggec.bleservice.setting.Command;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 左耳电池电量命令
 * 负责获取左耳电池电量
 */
public class BatteryLeftCommand extends Command {
    private static final String TAG = "BatteryLeftCommand";
    
    // 命令前缀，用于确认是左耳电量命令
    private static final String COMMAND_PREFIX = "99EC80";
    
    // 命令基础部分
    private static final String COMMAND_BASE = "99EC8000010112";
    
    // 命令校验和后缀
    private static final String COMMAND_SUFFIX = "34";

    // 测试阶段实际返回的响应前缀
    private static final String TEST_RESPONSE_PREFIX = "99EC80000101";
    
    // 正式协议的响应前缀
    private static final String RESPONSE_PREFIX = "99EC800002";
    
    // 电池电量 - 修改为String类型，支持"--"显示
    private String batteryLevel = "--";
    
    /**
     * 构造方法
     */
    public BatteryLeftCommand() {
        super();
        // 设置命令前缀
        setCommandPrefix(COMMAND_PREFIX);
    }
    
    /**
     * 获取电池电量
     * @return 电池电量字符串（百分比数字或"--"）
     */
    public String getBatteryLevel() {
        return batteryLevel;
    }
    
    @Override
    public String getCommandData() {
        // 完整命令: 99 EC 80 00 01 01 12 34
        return COMMAND_BASE + COMMAND_SUFFIX;
    }
    
    @Override
    public String parseResponse(String responseData) {
        YoBTSDKLog.d(TAG, "解析左耳电量响应数据: " + responseData);
        
        if (responseData == null) {
            batteryLevel = "--";
            notifyCompletion(ResultCode.FAILED, batteryLevel, "响应数据为空");
            return "--";
        }
        
        try {
            // 处理正式协议的响应格式：99 EC 80 00 02 01 XX 12 34
            if (responseData.length() >= 16 && responseData.startsWith(COMMAND_PREFIX) && responseData.contains("0201")) {
                // 获取电量值
                String batteryLevelHex = responseData.substring(12, 14);
                int batteryLevelInt = Integer.parseInt(batteryLevelHex, 16);
                
                // 验证电量在有效范围内（0-100%）
                if (batteryLevelInt < 0 || batteryLevelInt > 100) {
                    YoBTSDKLog.w(TAG, "左耳电量超出有效范围，值为: " + batteryLevelInt + "，设置为--");
                    batteryLevel = "--";
                } else {
                    batteryLevel = String.valueOf(batteryLevelInt);
                }
                
                // 通知命令完成
                notifyCompletion(ResultCode.SUCCESS, batteryLevel, "成功获取左耳电量");
                
                return batteryLevel;
            } 
            
            // 如果没有匹配的格式，返回--
            batteryLevel = "--";
            notifyCompletion(ResultCode.FAILED, batteryLevel, "无法解析左耳电量数据: " + responseData);
            return "--";
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "解析左耳电量失败: " + responseData, e);
            
            // 通知命令失败
            batteryLevel = "--";
            notifyCompletion(ResultCode.FAILED, batteryLevel, "解析左耳电量失败");
            
            return "--";
        }
    }
    
    @Override
    public boolean isResponseMatch(String responseData) {
        // 检查响应是否为左耳电量响应
        if (responseData == null) {
            return false;
        }
        
        YoBTSDKLog.d(TAG, "检查响应匹配: " + responseData);
        
        // 处理正式协议的响应格式：99 EC 80 00 02 01 XX 12 34
        if (responseData.length() >= 16 && 
            responseData.startsWith(COMMAND_PREFIX) && 
            responseData.contains("0201")) {
            YoBTSDKLog.d(TAG, "匹配到左耳电量响应: " + responseData);
            return true;
        }
        
        return false;
    }
    
    @Override
    public long getTimeoutMs() {
        // 电量命令使用较短超时时间，基于实际响应时间分析
        // 实际响应时间平均90ms，最长170ms，设置400ms提供足够缓冲
        return 400;
    }
} 