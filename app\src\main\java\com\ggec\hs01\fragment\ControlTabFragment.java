package com.ggec.hs01.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioGroup;
import android.widget.RadioButton;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.ggec.hs01.R;
import com.ggec.hs01.GGECHSApplication;
import com.ggec.bleservice.YoBLEApi;
import com.ggec.bleservice.YoCommandApi;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-31
 * 控制标签页Fragment
 * 用于显示每个标签页的控制选项
 */
public class ControlTabFragment extends Fragment {
    
    private static final String ARG_TAB_TYPE = "tab_type";
    
    // 标签页类型常量
    public static final int TAB_LEFT_DOUBLE = 0;
    public static final int TAB_RIGHT_DOUBLE = 1;
    public static final int TAB_LEFT_TRIPLE = 2;
    public static final int TAB_RIGHT_TRIPLE = 3;
    
    // 功能选项常量
    public static final int FUNCTION_PLAY_PAUSE = 0;
    public static final int FUNCTION_VOLUME_UP = 1;
    public static final int FUNCTION_VOLUME_DOWN = 2;
    public static final int FUNCTION_PREVIOUS_TRACK = 3;
    public static final int FUNCTION_NEXT_TRACK = 4;
    public static final int FUNCTION_VOICE_ASSISTANT = 5;
    public static final int FUNCTION_EQ_SWITCH = 6;
    public static final int FUNCTION_GAME_MODE = 7;
    public static final int FUNCTION_NONE = 8;
    
    private int tabType;
    private RadioGroup radioGroupFunctions;
    
    // API实例
    private YoBLEApi bleApi;
    private YoCommandApi commandApi;
    
    // 是否正在加载默认设置（用于避免在初始化时触发命令）
    private boolean isLoadingDefault = false;
    
    /**
     * 创建Fragment实例
     * @param tabType 标签页类型
     * @return Fragment实例
     */
    public static ControlTabFragment newInstance(int tabType) {
        ControlTabFragment fragment = new ControlTabFragment();
        Bundle args = new Bundle();
        args.putInt(ARG_TAB_TYPE, tabType);
        fragment.setArguments(args);
        return fragment;
    }
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            tabType = getArguments().getInt(ARG_TAB_TYPE);
        }
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_control_tab, container, false);
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 初始化API实例
        if (getActivity() != null) {
            GGECHSApplication app = (GGECHSApplication) getActivity().getApplication();
            bleApi = app.getBleApi();
            commandApi = app.getCommandApi();
        }
        
        // 初始化RadioGroup
        radioGroupFunctions = view.findViewById(R.id.radio_group_functions);
        
        // 设置选择监听器
        radioGroupFunctions.setOnCheckedChangeListener((group, checkedId) -> {
            // 如果正在加载默认设置，不触发命令
            if (isLoadingDefault) {
                return;
            }
            
            int selectedFunction = getSelectedFunction(checkedId);
            
            // 调用命令API设置控制功能
            setControlFunction(selectedFunction);
        });
        
        // 加载默认选择项
        loadDefaultSelection();
    }
    
    /**
     * 根据选中的RadioButton ID获取对应的功能常量
     * @param checkedId 选中的RadioButton ID
     * @return 功能常量
     */
    private int getSelectedFunction(int checkedId) {
        if (checkedId == R.id.radio_play_pause) {
            return FUNCTION_PLAY_PAUSE;
        } else if (checkedId == R.id.radio_volume_up) {
            return FUNCTION_VOLUME_UP;
        } else if (checkedId == R.id.radio_volume_down) {
            return FUNCTION_VOLUME_DOWN;
        } else if (checkedId == R.id.radio_previous_track) {
            return FUNCTION_PREVIOUS_TRACK;
        } else if (checkedId == R.id.radio_next_track) {
            return FUNCTION_NEXT_TRACK;
        } else if (checkedId == R.id.radio_voice_assistant) {
            return FUNCTION_VOICE_ASSISTANT;
        } else if (checkedId == R.id.radio_eq_switch) {
            return FUNCTION_EQ_SWITCH;
        } else if (checkedId == R.id.radio_game_mode) {
            return FUNCTION_GAME_MODE;
        } else if (checkedId == R.id.radio_none) {
            return FUNCTION_NONE;
        }
        return FUNCTION_NONE;
    }
    
    /**
     * 根据功能常量获取对应的RadioButton ID
     * @param function 功能常量
     * @return RadioButton ID
     */
    private int getRadioButtonId(int function) {
        switch (function) {
            case FUNCTION_PLAY_PAUSE:
                return R.id.radio_play_pause;
            case FUNCTION_VOLUME_UP:
                return R.id.radio_volume_up;
            case FUNCTION_VOLUME_DOWN:
                return R.id.radio_volume_down;
            case FUNCTION_PREVIOUS_TRACK:
                return R.id.radio_previous_track;
            case FUNCTION_NEXT_TRACK:
                return R.id.radio_next_track;
            case FUNCTION_VOICE_ASSISTANT:
                return R.id.radio_voice_assistant;
            case FUNCTION_EQ_SWITCH:
                return R.id.radio_eq_switch;
            case FUNCTION_GAME_MODE:
                return R.id.radio_game_mode;
            case FUNCTION_NONE:
            default:
                return R.id.radio_none;
        }
    }
    
    /**
     * 加载默认选择项
     */
    public void loadDefaultSelection() {
        // 设置标志，避免触发命令
        isLoadingDefault = true;
        
        int defaultFunction;
        
        // 根据标签页类型设置默认选项
        switch (tabType) {
            case TAB_LEFT_DOUBLE:
                defaultFunction = FUNCTION_PLAY_PAUSE; // 左耳双击默认：播放暂停
                break;
            case TAB_RIGHT_DOUBLE:
                defaultFunction = FUNCTION_VOICE_ASSISTANT; // 右耳双击默认：语音助手
                break;
            case TAB_LEFT_TRIPLE:
                defaultFunction = FUNCTION_PREVIOUS_TRACK; // 左耳三击默认：上一曲
                break;
            case TAB_RIGHT_TRIPLE:
                defaultFunction = FUNCTION_NEXT_TRACK; // 右耳三击默认：下一曲
                break;
            default:
                defaultFunction = FUNCTION_NONE;
                break;
        }
        
        // 设置默认选中项
        int radioButtonId = getRadioButtonId(defaultFunction);
        if (getView() != null) {
            RadioButton defaultRadioButton = getView().findViewById(radioButtonId);
            if (defaultRadioButton != null) {
                defaultRadioButton.setChecked(true);
            }
        }
        
        // 重置标志
        isLoadingDefault = false;
    }
    
    /**
     * 获取当前选中的功能
     * @return 当前选中的功能常量
     */
    public int getCurrentSelectedFunction() {
        if (radioGroupFunctions != null) {
            int checkedId = radioGroupFunctions.getCheckedRadioButtonId();
            return getSelectedFunction(checkedId);
        }
        return FUNCTION_NONE;
    }
    
    /**
     * 设置选中的功能（程序设置，不触发命令）
     * @param function 要选中的功能常量
     */
    public void setSelectedFunction(int function) {
        if (radioGroupFunctions != null) {
            // 设置标志，避免触发命令
            isLoadingDefault = true;
            
            int radioButtonId = getRadioButtonId(function);
            radioGroupFunctions.check(radioButtonId);
            
            // 重置标志
            isLoadingDefault = false;
        }
    }
    
    /**
     * 设置控制功能 - 调用命令API
     * @param functionType 功能类型
     */
    private void setControlFunction(int functionType) {
        // 检查API实例是否可用
        if (bleApi == null || commandApi == null) {
            return;
        }
        
        // 检查设备连接状态
        if (!bleApi.isDeviceConnected()) {
            if (getActivity() != null) {
                Toast.makeText(getActivity(), "请先连接设备", Toast.LENGTH_SHORT).show();
            }
            return;
        }
        
        // 根据标签页类型确定耳机侧和点击类型
        int earSide;
        int tapType;
        String description;
        
        switch (tabType) {
            case TAB_LEFT_DOUBLE:
                earSide = YoCommandApi.EAR_LEFT;
                tapType = YoCommandApi.TAP_TWICE;
                description = "左耳双击";
                break;
            case TAB_RIGHT_DOUBLE:
                earSide = YoCommandApi.EAR_RIGHT;
                tapType = YoCommandApi.TAP_TWICE;
                description = "右耳双击";
                break;
            case TAB_LEFT_TRIPLE:
                earSide = YoCommandApi.EAR_LEFT;
                tapType = YoCommandApi.TAP_TRIPLE;
                description = "左耳三击";
                break;
            case TAB_RIGHT_TRIPLE:
                earSide = YoCommandApi.EAR_RIGHT;
                tapType = YoCommandApi.TAP_TRIPLE;
                description = "右耳三击";
                break;
            default:
                return;
        }
        
        // 调用命令API
        commandApi.setControlFunction(earSide, tapType, functionType, (code, resultValue) -> {
            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    String functionName = getFunctionName(functionType);
                    String message = description + "设置为" + functionName + 
                            ((code == YoCommandApi.CommandResultCode.SUCCESS) ? "成功" : "失败");
                    
                    // 可以选择性显示Toast，这里暂时注释掉避免过多提示
                    // Toast.makeText(getActivity(), message, Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
    
    /**
     * 根据功能类型获取功能名称
     * @param functionType 功能类型
     * @return 功能名称
     */
    private String getFunctionName(int functionType) {
        switch (functionType) {
            case FUNCTION_PLAY_PAUSE:
                return "播放暂停";
            case FUNCTION_VOLUME_UP:
                return "音量加";
            case FUNCTION_VOLUME_DOWN:
                return "音量减";
            case FUNCTION_PREVIOUS_TRACK:
                return "上一曲";
            case FUNCTION_NEXT_TRACK:
                return "下一曲";
            case FUNCTION_VOICE_ASSISTANT:
                return "语音助手";
            case FUNCTION_EQ_SWITCH:
                return "EQ切换";
            case FUNCTION_GAME_MODE:
                return "游戏模式";
            case FUNCTION_NONE:
                return "无";
            default:
                return "未知功能";
        }
    }
} 