package com.bes.sdk.message;

import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.MessageID;

import java.util.LinkedList;

/**
 * Message to describe device info list.
 */
public class DeviceInfoMessage extends BaseMessage
{
    private LinkedList<? extends HmDevice> deviceList;

    public DeviceInfoMessage(LinkedList<? extends HmDevice> deviceList) {
        this.deviceList = deviceList;
    }

    @Override
    public MessageID getMsgID() {
        return MessageID.DEVICE_INFO;
    }

    @Override
    public LinkedList<? extends HmDevice> getMsgContent() {
        return deviceList;
    }
}
