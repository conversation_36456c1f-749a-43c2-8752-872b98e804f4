package com.ggec.hs01.activity;

import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import android.content.Intent;
import android.content.pm.PackageManager;

import android.app.Activity;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.annotation.NonNull;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ggec.hs01.R;
import com.ggec.bleservice.YoBLEApi;
import com.ggec.hs01.utils.BluetoothDeviceAdapter;
import com.ggec.hs01.GGECHSApplication;
import com.ggec.sppservice.YoSPPApi;
import com.ggec.sppservice.connect.SppConnectCallback;

import android.util.Log;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 蓝牙连接页面
 * 实现蓝牙设备扫描和连接
 */
public class BluetoothConnectActivity extends AppCompatActivity {
    private static final String TAG = "BluetoothConnectActivity";
    private static final int SCAN_TIMEOUT = 15000; // 扫描超时时间（毫秒）
    private static final int CONNECTION_STATUS_UPDATE_INTERVAL = 3000; // 连接状态更新间隔（毫秒）
    
    // 权限请求码
    private static final int REQUEST_BLUETOOTH_PERMISSIONS = 1001;
    
    // UI组件
    private TextView tvScanStatus;
    private TextView tvNoDevices;
    private ProgressBar progressScanning;
    private RecyclerView rvBluetoothDevices;
    
    // 蓝牙相关
    private BluetoothDeviceAdapter deviceAdapter;
    private boolean isConnecting = false;
    private BluetoothDevice connectedDevice = null;
    
    // YoBLEApi 实例
    private YoBLEApi bleApi;
    
    // 定时更新处理器
    private final Handler connectionStatusHandler = new Handler(Looper.getMainLooper());
//    private final Runnable connectionStatusRunnable = new Runnable() {
//        @Override
//        public void run() {
//            // 更新设备连接状态
//            if (deviceAdapter != null && deviceAdapter.getItemCount() > 0) {
//                deviceAdapter.notifyDataSetChanged();
//            }
//            // 安排下一次更新
//            connectionStatusHandler.postDelayed(this, CONNECTION_STATUS_UPDATE_INTERVAL);
//        }
//    };
    
    // 蓝牙事件监听器
    private final YoBLEApi.BleListener bleListener = new YoBLEApi.BleListener() {
        @Override
        public void onConnectionStateChanged(BluetoothDevice device, boolean connected) {
            runOnUiThread(() -> {
                if (connected) {
                    // 新的回调机制：当connected=true时，表示设备已连接且已就绪，可以直接进行通信
                    isConnecting = false;
                    connectedDevice = device;
                    updateScanStatus("已连接：" + device.getAddress());
                    deviceAdapter.setConnectedMac(device.getAddress());
                    deviceAdapter.notifyDataSetChanged();
                    Toast.makeText(BluetoothConnectActivity.this, "连接成功且设备已就绪", Toast.LENGTH_SHORT).show();
                    
                    // 直接处理连接成功
//                    handleConnectionSuccess();
                } else {
                    if (isConnecting) {
                        isConnecting = false;
                        updateScanStatus("连接失败");
                        progressScanning.setVisibility(View.GONE);
                        deviceAdapter.notifyDataSetChanged();
                        String deviceName = (device != null && device.getName() != null) ? device.getName() : "设备";
                        Toast.makeText(BluetoothConnectActivity.this, deviceName + " 连接失败，请重试", Toast.LENGTH_SHORT).show();
                        Log.d(TAG, "设备连接失败: " + ((device != null) ? device.getAddress() : "null"));
                    }
                }
            });
        }

        @Override
        public void onDeviceDisconnected(BluetoothDevice device, boolean disconnected) {
            runOnUiThread(() -> {
                if(disconnected) {
                    isConnecting = false;
                    connectedDevice = null;
                    updateScanStatus("已断开连接");
                    progressScanning.setVisibility(View.GONE);

                    String deviceName = (device != null && device.getName() != null) ? device.getName() : "设备";
                    Toast.makeText(BluetoothConnectActivity.this, deviceName + " 已断开连接", Toast.LENGTH_SHORT).show();
                    Log.d(TAG, "设备断开: " + ((device != null) ? device.getAddress() : "null"));
                    
                    // 更新列表中的连接状态
                    deviceAdapter.notifyDataSetChanged();
                }
            });
        }
        
        @Override
        public void onDeviceFound(BluetoothDevice device, int rssi) {
            runOnUiThread(() -> {
                // 打印解析的广播数据（调试用）
                if (device != null) {
                    String parsedDataString = bleApi.getParsedAdvertisingDataAsString(device);
                    if (parsedDataString != null) {
                        Log.d(TAG, "解析的广播数据: " + parsedDataString);
                    }
                }
                
                deviceAdapter.addDevice(device, rssi);
                
                if (tvNoDevices.getVisibility() == View.VISIBLE) {
                    tvNoDevices.setVisibility(View.GONE);
                }
            });
        }
        
        @Override
        public void onScanStarted() {
            runOnUiThread(() -> {
                updateScanStatus("扫描开始");
                progressScanning.setVisibility(View.VISIBLE);
            });
        }
        @Override
        public void onScanFinished() {
            runOnUiThread(() -> {
                updateScanStatus("扫描完成");
                progressScanning.setVisibility(View.GONE);
                
                if (deviceAdapter.getItemCount() == 0) {
                    tvNoDevices.setVisibility(View.VISIBLE);
                    tvNoDevices.setText("未找到蓝牙设备");
                }
            });
        }
        @Override
        public void onError(String errorMessage) {
            runOnUiThread(() -> {
                isConnecting = false;
                updateScanStatus("连接失败");
                progressScanning.setVisibility(View.GONE);
                Toast.makeText(BluetoothConnectActivity.this, "连接发生错误，请重试", Toast.LENGTH_SHORT).show();
            });
        }

        @Override
        public void systemBluetoothClose() {
            runOnUiThread(() -> {
                // 停止扫描并重置连接状态
                stopScan();
                isConnecting = false;
                connectedDevice = null;

                // 清空设备列表
                if (deviceAdapter != null) {
                    deviceAdapter.clearDevices();
                }

                // 更新UI到蓝牙关闭时的状态
                updateScanStatus("蓝牙未开启");
                if (tvNoDevices != null) {
                    tvNoDevices.setVisibility(View.VISIBLE);
                    tvNoDevices.setText("请开启蓝牙后重试");
                }
                if (progressScanning != null) {
                    progressScanning.setVisibility(View.GONE);
                }

                // 显示提示
                Toast.makeText(BluetoothConnectActivity.this, "蓝牙已关闭", Toast.LENGTH_SHORT).show();
            });
        }

        @Override
        public void systemBluetoothOpen() {
            runOnUiThread(() -> {
                Toast.makeText(BluetoothConnectActivity.this, "蓝牙已开启", Toast.LENGTH_SHORT).show();
                // 自动开始扫描
                startScan();
            });
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_bluetooth_connect);
        
        // 获取YoBLEApi实例
        bleApi = ((GGECHSApplication) getApplication()).getBleApi();
        
        // 初始化UI组件
        initViews();
        
//        // 设置内容区域适应系统UI，保留内边距防止内容被状态栏遮挡
//        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.bluetooth_connect_main), (v, insets) -> {
//            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
//            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
//            return insets;
//        });
        
        // 初始化蓝牙设备适配器
        deviceAdapter = new BluetoothDeviceAdapter(this);
        deviceAdapter.setOnDeviceClickListener(this::onDeviceSelected);
        rvBluetoothDevices.setAdapter(deviceAdapter);

        BluetoothDevice bluetoothDevice = bleApi.getConnectedDevice();
        if(bluetoothDevice != null && bleApi.isDeviceConnected()){
            updateScanStatus("已连接："+ bluetoothDevice.getAddress());
            deviceAdapter.setConnectedMac(bluetoothDevice.getAddress());
        }

        // 检查蓝牙权限
        checkBluetoothPermissions();
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 注册回调
        bleApi.registerListener(bleListener);
        
        // 如果已经获得权限，自动开始扫描
        if (bleApi.checkBluetoothPermissions(this)) {
            startScan();
        }
        
        // 开始定时更新连接状态
//        startConnectionStatusUpdates();
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        // 停止扫描
        stopScan();
        
        // 停止定时更新
//        stopConnectionStatusUpdates();
        
        // 注销回调
        bleApi.unregisterListener(bleListener);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 不在此断开蓝牙连接，只有在应用退出时才断开
    }
    
    /**
     * 初始化UI视图
     */
    private void initViews() {
        tvScanStatus = findViewById(R.id.tv_scan_status);
        tvNoDevices = findViewById(R.id.tv_no_devices);
        progressScanning = findViewById(R.id.progress_scanning);
        rvBluetoothDevices = findViewById(R.id.rv_bluetooth_devices);
        
        // 设置返回按钮点击事件
        ImageView btnBack = findViewById(R.id.btn_back);
        btnBack.setOnClickListener(v -> finish());
        
        // 设置刷新按钮点击事件
        ImageView btnRefresh = findViewById(R.id.btn_refresh);
        btnRefresh.setOnClickListener(v -> {
            stopScan();
            startScan();
        });
        
        // 设置RecyclerView
        rvBluetoothDevices.setLayoutManager(new LinearLayoutManager(this));
        
        // 启用只扫描我们自己设备的功能 (基于FeatureCode)
        bleApi.setFilterByFeatureCode(true);
        //bleApi.setCustomDeviceNameFilter(true);
        //bleApi.setFilterNoNameDevices(true);
        
        // 输出过滤器设置状态
        Log.d(TAG, "特征码过滤已启用: " + bleApi.isFilteringByFeatureCode());
    }
    
    /**
     * 更新扫描状态显示
     */
    private void updateScanStatus(String status) {
        if (tvScanStatus != null) {
            tvScanStatus.setText(status);
        }
    }

    /**
     * 更新连接状态显示
     */
    private void updateConnectionStatus(boolean connected, String deviceName) {
        if (connected) {
            connectedDevice = null; // 断开连接时清空已连接设备
            tvNoDevices.setVisibility(View.GONE);
            progressScanning.setVisibility(View.GONE);
            // 可以在这里添加连接成功的UI反馈，例如显示已连接的设备名称
        } else {
            // 断开连接时，如果当前没有连接的设备，则显示未找到设备
            if (connectedDevice == null) {
                tvNoDevices.setVisibility(View.VISIBLE);
                tvNoDevices.setText("未找到蓝牙设备");
            }
            progressScanning.setVisibility(View.GONE);
        }
    }

    /**
     * 获取设备名称
     */
    private String getDeviceName(BluetoothDevice device) {
        if (device == null) {
            return "未知设备";
        }
        return device.getName() != null && !device.getName().isEmpty() ? device.getName() : device.getAddress();
    }
    
    /**
     * 检查蓝牙权限
     */
    private void checkBluetoothPermissions() {
        if (!bleApi.checkBluetoothPermissions(this)) {
            updateScanStatus("等待授予蓝牙权限");
            progressScanning.setVisibility(View.GONE);
        }
    }
    
    /**
     * 开始蓝牙扫描
     */
    private void startScan() {
        // 如果蓝牙不可用，显示提示
        if (!bleApi.isBluetoothSupported()) {
            updateScanStatus("此设备不支持蓝牙");
            tvNoDevices.setVisibility(View.VISIBLE);
            tvNoDevices.setText("此设备不支持蓝牙");
            progressScanning.setVisibility(View.GONE);
            return;
        }
        
        // 如果蓝牙未开启，显示提示
        if (!bleApi.isBluetoothEnabled()) {
            updateScanStatus("蓝牙未开启");
            tvNoDevices.setVisibility(View.VISIBLE);
            tvNoDevices.setText("请开启蓝牙后重试");
            progressScanning.setVisibility(View.GONE);
            return;
        }
        
        // 清空之前的设备列表
        deviceAdapter.clearDevices();
        
        // 更新UI状态
        updateScanStatus("正在扫描...");
        progressScanning.setVisibility(View.VISIBLE);
        tvNoDevices.setVisibility(View.GONE);
        
        // 开始扫描
        if (!bleApi.startScan(SCAN_TIMEOUT)) {
            updateScanStatus("无法启动扫描");
            progressScanning.setVisibility(View.GONE);
        }
    }
    
    /**
     * 停止蓝牙扫描
     */
    private void stopScan() {
        bleApi.stopScan();
        progressScanning.setVisibility(View.GONE);
    }
    
//    /**
//     * 开始定期更新连接状态
//     */
//    private void startConnectionStatusUpdates() {
//        // 移除所有之前的回调，防止多次调用
//        connectionStatusHandler.removeCallbacks(connectionStatusRunnable);
//        // 添加新的定时任务
//        connectionStatusHandler.postDelayed(connectionStatusRunnable, CONNECTION_STATUS_UPDATE_INTERVAL);
//    }
//
//    /**
//     * 停止定期更新连接状态
//     */
//    private void stopConnectionStatusUpdates() {
//        connectionStatusHandler.removeCallbacks(connectionStatusRunnable);
//    }
    
    /**
     * 处理设备选择
     */
    private void onDeviceSelected(BluetoothDevice device,String classicMac) {
        if (isConnecting) {
            return;
        }
        
        // 停止扫描
        stopScan();
        
        isConnecting = true;
        updateScanStatus("正在连接...");

        bleApi.disconnect();
        // 连接ble
        bleApi.connect(device);

        // 连接bt
//        YoSPPApi sppApi = ((GGECHSApplication) getApplication()).getSppApi();
//        sppApi.scanAndConnect(null,classicMac, SCAN_TIMEOUT, true, null, new SppConnectCallback() {
//            @Override
//            public void onConnectStart(BluetoothDevice device) {
//
//            }
//
//            @Override
//            public void onPairingRequest(BluetoothDevice device) {
//
//            }
//
//            @Override
//            public void onPaired(BluetoothDevice device) {
//
//            }
//
//            @Override
//            public void onConnected(BluetoothDevice device, BluetoothSocket socket) {
//
//            }
//
//            @Override
//            public void onConnectFailed(BluetoothDevice device, String reason) {
//                isConnecting = false;
//                updateScanStatus("连接失败");
//                startScan();
//            }
//
//            @Override
//            public void onDisconnected(BluetoothDevice device) {
//
//            }
//        });
    }
    
    /**
     * 处理连接成功事件
     */
    private void handleConnectionSuccess() {
        setResult(Activity.RESULT_OK);
        finish();
    }
    
    //--- 权限请求结果处理 ---//
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        // 使用常量替代硬编码值
        if (requestCode == REQUEST_BLUETOOTH_PERMISSIONS) {
            boolean allGranted = true;
            
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }
            
            if (allGranted) {
                // 权限已授予，开始扫描
                startScan();
            } else {
                // 权限被拒绝
                updateScanStatus("蓝牙权限被拒绝");
                tvNoDevices.setVisibility(View.VISIBLE);
                tvNoDevices.setText("需要蓝牙权限才能使用此功能");
                progressScanning.setVisibility(View.GONE);
            }
        }
    }
} 