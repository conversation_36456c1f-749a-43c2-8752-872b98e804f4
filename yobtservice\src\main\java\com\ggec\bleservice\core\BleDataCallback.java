package com.ggec.bleservice.core;

/** 
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 蓝牙数据回调接口
 * 处理蓝牙数据的接收、发送和错误事件
 * 可根据需要选择性实现方法
 */
public interface BleDataCallback {

    /**
     * 当数据接收到时调用
     * @param data 接收到的数据，十六进制字符串格式
     */
    default void onDataReceived(String data) {}
    
    /**
     * 当数据成功发送时调用
     * @param data 发送的数据，十六进制字符串格式
     */
    default void onDataSent(String data) {}
    
    /**
     * 当发生错误时调用
     * @param errorMsg 错误信息
     */
    default void onError(String errorMsg) {}
    
    /**
     * 当接收到二进制数据时调用
     * @param data 二进制数据
     */
    default void onBinaryDataReceived(byte[] data) {}
    
    /**
     * 当接收到解析后的指令时调用
     * @param command 指令码
     * @param params 参数数据
     */
    default void onCommandReceived(int command, byte[] params) {}
} 