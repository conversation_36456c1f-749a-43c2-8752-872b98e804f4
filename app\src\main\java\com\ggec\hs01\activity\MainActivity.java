package com.ggec.hs01.activity;

import android.Manifest;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.ggec.hs01.R;
import com.ggec.bleservice.YoBLEApi;
import com.ggec.bleservice.YoCommandApi;
import com.ggec.hs01.GGECHSApplication;
import com.ggec.hs01.view.HMButton;
import com.ggec.hs01.view.HMSwitch;
import com.ggec.sppservice.YoSPPApi;

import android.util.Log;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 应用主界面
 * 提供蓝牙连接和各种设置功能
 */
public class MainActivity extends AppCompatActivity {
    
    private static final String TAG = "MainActivity";
    private static final int REQUEST_CONNECT_DEVICE = 1;
    private static final int REQUEST_PERMISSIONS_CODE = 101;
    
    // 蓝牙状态和电量显示
    private TextView tvBluetoothStatus;
    private TextView tvBatteryLeft;
    private TextView tvBatteryRight;
    private TextView tvBatteryCase;
    private TextView tvCaseChargingStatus;
    
    // 电池监听开关
    private HMSwitch switchBatteryMonitor;
    
    // 设置卡片
    private CardView cardMusicSettings;
    private CardView cardControlSettings;
    private CardView cardFunctionSettings;
    private CardView cardAppSettings;
    
    // 蓝牙相关
    private BluetoothDevice connectedDevice;
    
    // YoBLEApi 实例
    private YoBLEApi bleApi;
    
    // YoCommandApi 实例
    private YoCommandApi commandApi;
    
    /**
     * 主线程Handler，用于UI更新
     */
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
  
    // 颜色恢复任务管理
    private final java.util.Map<TextView, Runnable> colorResetRunnables = new java.util.HashMap<>();
    
    // 蓝牙事件监听器
    private final YoBLEApi.BleListener bleListener = new YoBLEApi.BleListener() {
        
        @Override
        public void onError(String errorMsg) {
            showMessage(errorMsg);
        }

        @Override
        public void onConnectionStateChanged(BluetoothDevice device, boolean connected) {
            runOnUiThread(() -> {
                if (connected) {
                    connectedDevice = device;
                    updateBluetoothStatus("已连接且就绪: " + (device.getName() != null ? device.getName() : "未知设备") + "（" + device.getAddress() + ")");
                    Log.i(TAG, "设备连接成功且已就绪...");
                    updateBatteryLevels("--", "--", "--");
                    
                    // 设备已经就绪，可以直接初始化命令API并设置就绪状态
                    if (commandApi == null) {
                        commandApi = YoCommandApi.getInstance();
                    }
                    
                    // 确保设备准备就绪
                    commandApi.setDeviceReady(true);
                } else {
                    connectedDevice = null;
                    updateBluetoothStatus("蓝牙未连接");
                    // 连接失败也需要重置电量显示
                    updateBatteryLevels("--", "--", "--");
                    tvCaseChargingStatus.setText("");
                }
            });
        }

        @Override
        public void onDeviceDisconnected(BluetoothDevice device, boolean disconnected) {
            runOnUiThread(() -> {
                if (disconnected) {
                    connectedDevice = null;
                    updateBluetoothStatus("蓝牙连接已断开");
                    // 连接断开时，清空电量显示
                    updateBatteryLevels("--", "--", "--");
                    tvCaseChargingStatus.setText("");
                    // 同时停止电量监听
                    commandApi.stopBatteryMonitor();
                    if (switchBatteryMonitor != null) {
                        switchBatteryMonitor.setChecked(false);
                    }
                    Log.d(TAG, "设备从主界面断开: " + ((device != null) ? device.getAddress() : "null"));
                }
            });
        }
        
        @Override
        public void onDeviceReadyStateChanged(boolean ready) {
            // 只需要处理设备不就绪的情况
            if (!ready) {
                runOnUiThread(() -> {
                    // 更新设备状态UI
                    if (connectedDevice != null) {
                        updateBluetoothStatus("已连接但未就绪: " + connectedDevice.getName() + "（" + connectedDevice.getAddress() + ")");
                    }
                    
                    // 取消监听开关
                    if (switchBatteryMonitor != null) {
                        switchBatteryMonitor.setChecked(false);
                    }
                    
                    // 更新电量显示，清除数据
                    updateBatteryLevels("--", "--", "--");
                });
            }
        }

        @Override
        public void systemBluetoothClose() {
            runOnUiThread(() -> {
                connectedDevice = null;
                updateBluetoothStatus("蓝牙已关闭");
                updateBatteryLevels("--", "--", "--");
                tvCaseChargingStatus.setText("");
                if (switchBatteryMonitor != null) {
                    switchBatteryMonitor.setChecked(false);
                }
                Toast.makeText(MainActivity.this, "蓝牙已关闭", Toast.LENGTH_SHORT).show();
            });
        }
    };

    // 命令回调，用于在命令执行完成后通知用户
    private final YoCommandApi.CommandResultCallback commandResultCallback = (code, resultValue) -> {
        Log.i(TAG, "命令执行结果: " + (code == YoCommandApi.CommandResultCode.SUCCESS ? "成功" : "失败") + ", resultValue: " + resultValue);
    };
    
    // 新的电池状态回调，用于处理主动上报
    private final YoCommandApi.BatteryStateCallback batteryStateCallback = new YoCommandApi.BatteryStateCallback() {
        @Override
        public void onBatteryStateChanged() {
            runOnUiThread(() -> {
                Log.d(TAG, "电池状态发生变化，更新UI");
                
                // 每次收到电量更新时都应用蓝色闪烁效果
                applyBlueColorEffect(tvBatteryLeft);
                applyBlueColorEffect(tvBatteryRight);
                applyBlueColorEffect(tvBatteryCase);
                
                updateBatteryLevels(
                        commandApi.getLeftBatteryLevel(),
                        commandApi.getRightBatteryLevel(),
                        commandApi.getCaseBatteryLevel()
                );
                updateCaseChargingStatus(commandApi.isCaseCharging());
            });
        }
    };
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // 在Activity创建时检查并请求权限
        checkAndRequestPermissions();
        
        // 延迟检查并输出BLE权限状态到日志（给权限请求一些时间）
        new android.os.Handler().postDelayed(() -> logBlePermissionStatus(), 1000);
        
        // 获取YoBLEApi实例
        bleApi = ((GGECHSApplication) getApplication()).getBleApi();
        
        // 设置内容区域适应系统UI，保留内边距防止内容被状态栏遮挡
//        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
//            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
//            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
//            return insets;
//        });
        
        // 初始化蓝牙连接按钮
        initBluetoothButton();
        
        // 初始化电池监听开关
        initBatteryMonitorSwitch();
        
        // 初始化获取状态按钮
        initGetStatusButton();
        
        // 初始化蓝牙状态和电量显示
        initBluetoothStatus();
        
        // 初始化设置卡片
        initSettingsCards();
        
        // 额外设置开关的尺寸
        View switchTrack = switchBatteryMonitor.findViewById(android.R.id.background);
        View switchThumb = switchBatteryMonitor.findViewById(android.R.id.toggle);
        if (switchTrack != null) {
            ViewGroup.LayoutParams params = switchTrack.getLayoutParams();
            if (params != null && params.width > 0) {
                params.width = (int)(params.width * 0.8f);
                switchTrack.setLayoutParams(params);
            }
        }
    }
    
    /**
     * 检查BLE和位置相关权限状态
     * @return 返回权限检查结果的详细信息
     */
    private String checkBlePermissionStatus() {
        StringBuilder status = new StringBuilder("BLE权限状态检查:\n");
        
        // 位置权限检查
        boolean hasFineLocation = ContextCompat.checkSelfPermission(this, 
                Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED;
        boolean hasCoarseLocation = ContextCompat.checkSelfPermission(this, 
                Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED;
        
        status.append("- 精确位置权限: ").append(hasFineLocation ? "✓已授予" : "✗未授予").append("\n");
        status.append("- 粗略位置权限: ").append(hasCoarseLocation ? "✓已授予" : "✗未授予").append("\n");
        
        // 蓝牙权限检查
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) { // Android 12+
            boolean hasBluetoothScan = ContextCompat.checkSelfPermission(this,
                    "android.permission.BLUETOOTH_SCAN") == PackageManager.PERMISSION_GRANTED;
            boolean hasBluetoothConnect = ContextCompat.checkSelfPermission(this,
                    "android.permission.BLUETOOTH_CONNECT") == PackageManager.PERMISSION_GRANTED;
            boolean hasBluetoothAdvertise = ContextCompat.checkSelfPermission(this,
                    "android.permission.BLUETOOTH_ADVERTISE") == PackageManager.PERMISSION_GRANTED;

            status.append("- 蓝牙扫描权限: ").append(hasBluetoothScan ? "✓已授予" : "✗未授予").append("\n");
            status.append("- 蓝牙连接权限: ").append(hasBluetoothConnect ? "✓已授予" : "✗未授予").append("\n");
            status.append("- 蓝牙广播权限: ").append(hasBluetoothAdvertise ? "✓已授予" : "✗未授予").append("\n");
        } else {
            boolean hasBluetooth = ContextCompat.checkSelfPermission(this,
                    Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED;
            boolean hasBluetoothAdmin = ContextCompat.checkSelfPermission(this,
                    Manifest.permission.BLUETOOTH_ADMIN) == PackageManager.PERMISSION_GRANTED;

            status.append("- 蓝牙权限: ").append(hasBluetooth ? "✓已授予" : "✗未授予").append("\n");
            status.append("- 蓝牙管理权限: ").append(hasBluetoothAdmin ? "✓已授予" : "✗未授予").append("\n");
        }
        
        // 扫描器会自动根据设备能力选择最佳实现
        status.append("- 扫描器: 系统自动选择最优实现").append("\n");
        
        return status.toString();
    }
    
    /**
     * 自动检查并输出BLE权限状态到日志
     */
    private void logBlePermissionStatus() {
        try {
            String permissionStatus = checkBlePermissionStatus();
            Log.i(TAG, "=== BLE权限状态自动检查 ===");
            Log.i(TAG, permissionStatus);
            Log.i(TAG, "================================");
            
            // 检查是否所有必要权限都已授予
            boolean allPermissionsGranted = true;
            
            // 检查位置权限
            boolean hasFineLocation = ContextCompat.checkSelfPermission(this, 
                    Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED;
            if (!hasFineLocation) {
                allPermissionsGranted = false;
            }
            
            // 检查蓝牙权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) { // Android 12+
                boolean hasBluetoothScan = ContextCompat.checkSelfPermission(this,
                        "android.permission.BLUETOOTH_SCAN") == PackageManager.PERMISSION_GRANTED;
                boolean hasBluetoothConnect = ContextCompat.checkSelfPermission(this,
                        "android.permission.BLUETOOTH_CONNECT") == PackageManager.PERMISSION_GRANTED;

                if (!hasBluetoothScan || !hasBluetoothConnect) {
                    allPermissionsGranted = false;
                }
            } else {
                boolean hasBluetooth = ContextCompat.checkSelfPermission(this, 
                        Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED;
                boolean hasBluetoothAdmin = ContextCompat.checkSelfPermission(this, 
                        Manifest.permission.BLUETOOTH_ADMIN) == PackageManager.PERMISSION_GRANTED;
                        
                if (!hasBluetooth || !hasBluetoothAdmin) {
                    allPermissionsGranted = false;
                }
            }
            
            if (allPermissionsGranted) {
                Log.i(TAG, "✓ 所有BLE相关权限已授予，扫描器可正常工作");
            } else {
                Log.w(TAG, "⚠ 部分BLE权限未授予，可能影响扫描功能");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "检查BLE权限状态时出错: " + e.getMessage());
        }
    }
    
    /**
     * 检查并请求必要的运行时权限。
     * 即使用户拒绝，App也能继续运行。
     */
    private void checkAndRequestPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            List<String> permissionsToRequest = new ArrayList<>();
            String[] requiredPermissions;

//            // 根据Android版本确定需要请求的权限
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) { // Android 13 (API 33)
//                requiredPermissions = new String[]{
//                        Manifest.permission.READ_MEDIA_IMAGES,
//                        Manifest.permission.READ_MEDIA_VIDEO,
//                        Manifest.permission.READ_MEDIA_AUDIO,
//                        Manifest.permission.READ_PHONE_STATE,
//                        // 蓝牙和位置权限 (Android 13+)
//                        "android.permission.BLUETOOTH_SCAN",
//                        "android.permission.BLUETOOTH_CONNECT",
//                        "android.permission.BLUETOOTH_ADVERTISE",
//                        Manifest.permission.ACCESS_FINE_LOCATION,
//                        Manifest.permission.ACCESS_COARSE_LOCATION
//                };
//            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) { // Android 12 (API 31)
//                requiredPermissions = new String[]{
//                        Manifest.permission.READ_EXTERNAL_STORAGE,
//                        Manifest.permission.READ_PHONE_STATE,
//                        // 蓝牙和位置权限 (Android 12)
//                        "android.permission.BLUETOOTH_SCAN",
//                        "android.permission.BLUETOOTH_CONNECT",
//                        "android.permission.BLUETOOTH_ADVERTISE",
//                        Manifest.permission.ACCESS_FINE_LOCATION,
//                        Manifest.permission.ACCESS_COARSE_LOCATION
//                };
//            } else { // Android 6 ~ 11
//                requiredPermissions = new String[]{
//                        Manifest.permission.READ_EXTERNAL_STORAGE,
//                        Manifest.permission.READ_PHONE_STATE,
//                        // 蓝牙和位置权限 (Android 6-11)
//                        Manifest.permission.BLUETOOTH,
//                        Manifest.permission.BLUETOOTH_ADMIN,
//                        Manifest.permission.ACCESS_FINE_LOCATION,
//                        Manifest.permission.ACCESS_COARSE_LOCATION
//                };
//            }

            requiredPermissions = new String[]{
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.READ_PHONE_STATE,
                    // 蓝牙和位置权限 (Android 6-11)
                    Manifest.permission.BLUETOOTH,
                    Manifest.permission.BLUETOOTH_ADMIN,
                    // 扫描
                    Manifest.permission.ACCESS_FINE_LOCATION,
                    Manifest.permission.ACCESS_COARSE_LOCATION
            };

            for (String permission : requiredPermissions) {
                if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                    permissionsToRequest.add(permission);
                }
            }

            if (!permissionsToRequest.isEmpty()) {
                ActivityCompat.requestPermissions(this,
                        permissionsToRequest.toArray(new String[0]),
                        REQUEST_PERMISSIONS_CODE);
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_PERMISSIONS_CODE) {
            boolean hasLocationPermission = false;
            boolean hasBluetoothPermission = false;
            List<String> deniedPermissions = new ArrayList<>();
            
            for (int i = 0; i < permissions.length; i++) {
                String permission = permissions[i];
                boolean granted = grantResults[i] == PackageManager.PERMISSION_GRANTED;
                
                if (granted) {
                    Log.i(TAG, "权限 " + permission + " 已被授予。");
                    
                    // 检查关键权限
                    if (permission.equals(Manifest.permission.ACCESS_FINE_LOCATION) ||
                        permission.equals(Manifest.permission.ACCESS_COARSE_LOCATION)) {
                        hasLocationPermission = true;
                    }
                    if (permission.contains("BLUETOOTH")) {
                        hasBluetoothPermission = true;
                    }
                } else {
                    Log.w(TAG, "权限 " + permission + " 已被拒绝。");
                    deniedPermissions.add(permission);
                }
            }
            
            // 给出权限状态的综合反馈
            if (!deniedPermissions.isEmpty()) {
                StringBuilder message = new StringBuilder("以下权限被拒绝：\n");
                for (String permission : deniedPermissions) {
                    String permissionName = permission.substring(permission.lastIndexOf(".") + 1);
                    message.append("• ").append(permissionName).append("\n");
                    
                    // 特殊说明重要权限的影响
                    if (permission.contains("LOCATION")) {
                        message.append("  (影响蓝牙设备扫描功能)\n");
                    } else if (permission.contains("BLUETOOTH")) {
                        message.append("  (影响蓝牙连接功能)\n");
                    }
                }
                
                // 显示详细的权限拒绝信息
                new androidx.appcompat.app.AlertDialog.Builder(this)
                        .setTitle("权限提醒")
                        .setMessage(message.toString() + "\n相关功能可能无法正常使用。")
                        .setPositiveButton("我知道了", null)
                        .setNeutralButton("查看权限状态", (dialog, which) -> {
                            String status = checkBlePermissionStatus();
                            Toast.makeText(this, "权限状态已输出到日志", Toast.LENGTH_SHORT).show();
                            Log.i(TAG, status);
                        })
                        .show();
            } else {
                Toast.makeText(this, "所有权限已授予", Toast.LENGTH_SHORT).show();
                Log.i(TAG, "所有请求的权限均已授予");
                
                // 输出权限状态到日志
                String status = checkBlePermissionStatus();
                Log.i(TAG, status);
            }
        }
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        
        // 注册蓝牙监听器
        bleApi.registerListener(bleListener);
        
        // 检查是否有已连接的设备
        connectedDevice = bleApi.getConnectedDevice();
        if (connectedDevice != null) {
            updateBluetoothStatus("已连接: " + connectedDevice.getName() + "("+ connectedDevice.getAddress() + ")");
            
            // 仅更新蓝牙状态，不自动获取电量
            if (!bleApi.isDeviceReady()) {
                Log.i(TAG, "设备已连接但未就绪");
            } else {
                getHomePageStatus();
            }
        } else {
            updateBluetoothStatus("蓝牙未连接");
            
            // 重置监听开关状态
            if (switchBatteryMonitor != null) {
                switchBatteryMonitor.setChecked(false);
            }
        }
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        
        // 注销蓝牙监听器
        bleApi.unregisterListener(bleListener);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        // 清理所有颜色恢复任务
        for (Runnable runnable : colorResetRunnables.values()) {
            mainHandler.removeCallbacks(runnable);
        }
        colorResetRunnables.clear();
        
        // 不在此断开蓝牙连接，只有在应用退出时才断开
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CONNECT_DEVICE && resultCode == RESULT_OK) {
            // 连接成功，但不立即获取电量信息，等待设备就绪
            Log.i(TAG, "蓝牙连接流程完成，等待设备就绪...");
        }
    }
    
    /**
     * 初始化蓝牙连接按钮
     */
    private void initBluetoothButton() {
        HMButton btnCustom = findViewById(R.id.btn_custom);
        btnCustom.setOnClickListener(v -> {
            // 直接进入蓝牙连接页面
            Intent intent = new Intent(MainActivity.this, BluetoothConnectActivity.class);
            startActivityForResult(intent, REQUEST_CONNECT_DEVICE);
        });
    }
    
    /**
     * 初始化电池监听开关
     */
    private void initBatteryMonitorSwitch() {
        switchBatteryMonitor = findViewById(R.id.switch_battery_monitor);
        
        // 设置开关点击监听器
        switchBatteryMonitor.setOnCheckedChangeListener(new HMSwitch.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(HMSwitch switchView, boolean isChecked) {
                // 如果是开启监听
                if (isChecked) {
                    // 必须先连接设备
                    if (connectedDevice == null || !bleApi.isDeviceReady()) {
                        switchView.setChecked(false); // 状态回滚
                        Toast.makeText(MainActivity.this, "请先连接设备", Toast.LENGTH_SHORT).show();
                        return;
                    }
                    
                    // 启动监听
                    Log.i(TAG, "启动电池状态监听...");
                    commandApi.startBatteryMonitor(batteryStateCallback);
                    Toast.makeText(MainActivity.this, "电池自动监听已开启", Toast.LENGTH_SHORT).show();
                    
                } else {
                    // 关闭监听
                    Log.i(TAG, "停止电池状态监听...");
                    commandApi.stopBatteryMonitor();
                }
            }
        });
        
        // 设置初始状态为关闭
        switchBatteryMonitor.setChecked(false);
        switchBatteryMonitor.setEnabled(true);
    }
    
    /**
     * 初始化获取状态按钮
     */
    private void initGetStatusButton() {
        HMButton btnGetStatus = findViewById(R.id.btn_get_status);
        btnGetStatus.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 主动获取首页聚合状态
                getHomePageStatus();
            }
        });
    }
    

    
    /**
     * 初始化蓝牙状态和电量显示
     */
    private void initBluetoothStatus() {
        tvBluetoothStatus = findViewById(R.id.tv_bluetooth_status);
        tvBatteryLeft = findViewById(R.id.tv_battery_left);
        tvBatteryRight = findViewById(R.id.tv_battery_right);
        tvBatteryCase = findViewById(R.id.tv_battery_case);
        tvCaseChargingStatus = findViewById(R.id.tv_case_charging_status);
        
        // 获取YoCommandApi实例
        commandApi = ((GGECHSApplication) getApplication()).getCommandApi();
        
        // 显示蓝牙未连接状态
        updateBluetoothStatus("蓝牙未连接");
        updateBatteryLevels("--", "--", "--");
        updateCaseChargingStatus(false);
    }
    
    /**
     * 更新蓝牙状态显示
     */
    private void updateBluetoothStatus(String status) {
        if (tvBluetoothStatus != null) {
            tvBluetoothStatus.setText(status);
        }
    }
    
    /**
     * 更新电池电量显示
     */
    private void updateBatteryLevels(String leftLevel, String rightLevel, String caseLevel) {
        // 已在调用处使用runOnUiThread，此处无需再次使用
        if (tvBatteryLeft != null) {
            if (!"--".equals(leftLevel)) {
                tvBatteryLeft.setText(leftLevel + "%");
            } else {
                tvBatteryLeft.setText("--%");
            }
        }
        
        if (tvBatteryRight != null) {
            if (!"--".equals(rightLevel)) {
                tvBatteryRight.setText(rightLevel + "%");
            } else {
                tvBatteryRight.setText("--%");
            }
        }
        
        if (tvBatteryCase != null) {
            if (!"--".equals(caseLevel)) {
                tvBatteryCase.setText(caseLevel + "%");
            } else {
                tvBatteryCase.setText("--%");
            }
        }
    }
    
    /**
     * 初始化设置卡片
     */
    private void initSettingsCards() {
        cardMusicSettings = findViewById(R.id.card_music_settings);
        cardControlSettings = findViewById(R.id.card_control_settings);
        cardFunctionSettings = findViewById(R.id.card_function_settings);
        cardAppSettings = findViewById(R.id.card_app_settings);
        
        // 设置点击事件
        cardMusicSettings.setOnClickListener(v -> {
            Intent intent = new Intent(MainActivity.this, MusicSettingsActivity.class);
            startActivity(intent);
        });
        
        cardControlSettings.setOnClickListener(v -> {
            Intent intent = new Intent(MainActivity.this, ControlSettingsActivity.class);
            startActivity(intent);
        });
        
        cardFunctionSettings.setOnClickListener(v -> {
            Intent intent = new Intent(MainActivity.this, FunctionSettingsActivity.class);
            startActivity(intent);
        });
        
        cardAppSettings.setOnClickListener(v -> {
            // 检查是否已连接设备
            if (connectedDevice != null) {
                Intent intent = new Intent(MainActivity.this, AppSettingsActivity.class);
                startActivity(intent);
            } else {
                // 未连接设备时显示提示信息
                showMessage("蓝牙未连接");
            }
        });
    }
    
    /**
     * 显示消息
     * @param message 消息内容
     */
    private void showMessage(final String message) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                tvBluetoothStatus.setText(message);
            }
        });
    }
    
    /**
     * 更新充电盒充电状态显示
     */
    private void updateCaseChargingStatus(boolean isCharging) {
        if (tvCaseChargingStatus != null) {
            if (isCharging) {
                tvCaseChargingStatus.setText("充电中");
            } else {
                tvCaseChargingStatus.setText("");
            }
        }
    }
    
    /**
     * 获取首页聚合状态
     * 用于手动获取电量、充电状态、固件版本等信息
     */
    private void getHomePageStatus() {
        // 确保设备已连接且命令API可用
        if (connectedDevice == null || commandApi == null) {
            showMessage("设备未连接，无法获取状态");
            return;
        }

        // 确保设备处于就绪状态
        if (!bleApi.isDeviceReady()) {
            showMessage("设备未就绪，无法获取状态");
            return;
        }

        Log.i(TAG, "手动获取首页聚合状态");
        showMessage("正在获取首页聚合状态...");

        try {
            // 使用YoCommandApi获取首页聚合状态
            commandApi.getHomePageStatus(new YoCommandApi.HomePageStatusCallback() {
                @Override
                public void onHomePageStatusResult(int code, Map<String, String> statusMap) {
                    // 增加严格判断：必须成功且数据不为空，才认为是有效聚合结果
                    if (code == YoCommandApi.CommandResultCode.SUCCESS && statusMap != null && !statusMap.isEmpty()) {
                        // 采用多行格式记录日志
                        StringBuilder logMsg = new StringBuilder("首页聚合状态获取成功:\n");
                        logMsg.append("{\n");
                        List<String> sortedKeys = new ArrayList<>(statusMap.keySet());
                        Collections.sort(sortedKeys);
                        for (String key : sortedKeys) {
                            logMsg.append("    ").append(key).append(" = ").append(statusMap.get(key)).append(";\n");
                        }
                        logMsg.append("}");
                        Log.i(TAG, logMsg.toString());

                        runOnUiThread(() -> {
                            // 更新UI显示
                            updateUIFromAggregatedStatus(statusMap);
                            
                            // 保存固件版本信息到Application
                            String firmwareVersion = statusMap.get("94");
                            if (firmwareVersion != null && !firmwareVersion.isEmpty()) {
                                ((GGECHSApplication) getApplication()).setFirmwareVersion(firmwareVersion);
                                Log.i(TAG, "固件版本信息已保存: " + firmwareVersion);
                            }
                            
                            Toast.makeText(MainActivity.this, "首页聚合状态已更新", Toast.LENGTH_SHORT).show();
                            // 恢复蓝牙状态显示
                            if (connectedDevice != null) {
                                updateBluetoothStatus("已连接: " + connectedDevice.getName() + "（" + connectedDevice.getAddress() + ")");
                            }
                        });
                    } else {
                        // 其他所有情况（包括成功但无数据、失败等）都视为获取失败
                        Log.w(TAG, "获取首页聚合状态失败或无数据返回，状态码: " + code);
                        runOnUiThread(() -> {
                            showMessage("获取首页聚合状态失败，请重试");
                             // 恢复蓝牙状态显示
                            if (connectedDevice != null) {
                                updateBluetoothStatus("已连接: " + connectedDevice.getName() + "（" + connectedDevice.getAddress() + ")");
                            }
                        });
                    }
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "获取首页聚合状态失败", e);
            showMessage("获取首页聚合状态失败，请重试");
        }
    }
    
    /**
     * 根据聚合状态数据更新UI
     * @param statusMap 聚合状态数据
     */
    private void updateUIFromAggregatedStatus(Map<String, String> statusMap) {
        // 从状态Map中提取电量信息并更新UI
        try {
            // 左耳电量 - 直接使用字符串值
            if (statusMap.containsKey("80_01")) {
                String leftLevelStr = statusMap.get("80_01");
                if (leftLevelStr != null) {
                    if ("--".equals(leftLevelStr)) {
                        tvBatteryLeft.setText("--%");
                    } else {
                        tvBatteryLeft.setText(leftLevelStr + "%");
                    }
                } else {
                    tvBatteryLeft.setText("--%");
                }
            }
            
            // 右耳电量 - 直接使用字符串值
            if (statusMap.containsKey("80_02")) {
                String rightLevelStr = statusMap.get("80_02");
                if (rightLevelStr != null) {
                    if ("--".equals(rightLevelStr)) {
                        tvBatteryRight.setText("--%");
                    } else {
                        tvBatteryRight.setText(rightLevelStr + "%");
                    }
                } else {
                    tvBatteryRight.setText("--%");
                }
            }
            
            // 盒子电量 - 直接使用字符串值
            if (statusMap.containsKey("80_03")) {
                String caseLevelStr = statusMap.get("80_03");
                if (caseLevelStr != null) {
                    if ("--".equals(caseLevelStr)) {
                        tvBatteryCase.setText("--%");
                    } else {
                        tvBatteryCase.setText(caseLevelStr + "%");
                    }
                } else {
                    tvBatteryCase.setText("--%");
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "处理聚合状态数据出错", e);
        }
    }

    /**
     * 应用品牌色闪烁效果
     * @param textView 要应用效果的TextView
     */
    private void applyBlueColorEffect(TextView textView) {
        if (textView == null) return;
        
        // 取消之前的颜色恢复任务
        Runnable previousRunnable = colorResetRunnables.get(textView);
        if (previousRunnable != null) {
            mainHandler.removeCallbacks(previousRunnable);
        }
        
        // 设置强调色（品牌蓝色）
        textView.setTextColor(ContextCompat.getColor(this, R.color.font_emphasize));
        
        // 创建恢复默认颜色的任务
        Runnable resetColorRunnable = () -> {
            textView.setTextColor(ContextCompat.getColor(this, R.color.font_primary));
            colorResetRunnables.remove(textView);
        };
        
        // 保存任务引用
        colorResetRunnables.put(textView, resetColorRunnable);
        
        // 3秒后恢复默认颜色
        mainHandler.postDelayed(resetColorRunnable, 3000);
    }
} 