package com.ggec.sppservice.scanner.impl;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothClass;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import com.ggec.sppservice.scanner.ISppScanner;
import com.ggec.sppservice.scanner.SppScanCallback;
import com.ggec.sppservice.scanner.SppScanConfig;

import java.util.HashSet;
import java.util.Set;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * SPP扫描器实现类
 * 使用Android蓝牙API实现传统蓝牙设备的扫描
 */
public class SppScannerImpl implements ISppScanner {
    private static final String TAG = "SppScannerImpl";
    
    private final Context context;
    private final BluetoothAdapter bluetoothAdapter;
    private final Handler handler = new Handler(Looper.getMainLooper());
    
    // 用于避免重复回调已发现的设备
    private final Set<String> discoveredDevices = new HashSet<>();
    
    private SppScanConfig scanConfig;
    private SppScanCallback scanCallback;
    private boolean isScanning = false;
    
    // 蓝牙设备扫描广播接收器
    private final BroadcastReceiver scanReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (BluetoothDevice.ACTION_FOUND.equals(action)) {
                // 发现设备
                BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                int rssi = intent.getShortExtra(BluetoothDevice.EXTRA_RSSI, Short.MIN_VALUE);
                
                if (device != null && isDeviceValid(device)) {
                    String address = device.getAddress();
                    if (!discoveredDevices.contains(address)) {
                        discoveredDevices.add(address);
                        
                        // 将回调转发到主线程
                        handler.post(() -> {
                            if (scanCallback != null) {
                                scanCallback.onDeviceFound(device, rssi);
                            }
                        });
                    }
                }
            } else if (BluetoothAdapter.ACTION_DISCOVERY_STARTED.equals(action)) {
                // 扫描开始
                YoBTSDKLog.d(TAG, "蓝牙扫描开始");
                handler.post(() -> {
                    if (scanCallback != null) {
                        scanCallback.onScanStart();
                    }
                });
            } else if (BluetoothAdapter.ACTION_DISCOVERY_FINISHED.equals(action)) {
                // 扫描结束
                YoBTSDKLog.d(TAG, "蓝牙扫描结束");
                handler.post(() -> {
                    if (scanCallback != null && isScanning) {
                        scanCallback.onScanFinish();
                    }
                    isScanning = false;
                });
            }
        }
    };
    
    /**
     * 构造函数
     * @param context 应用上下文
     */
    public SppScannerImpl(Context context) {
        this.context = context;
        
        // 获取蓝牙适配器
        BluetoothManager bluetoothManager = (BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE);
        bluetoothAdapter = bluetoothManager != null ? bluetoothManager.getAdapter() : BluetoothAdapter.getDefaultAdapter();
        
        if (bluetoothAdapter == null) {
            YoBTSDKLog.e(TAG, "蓝牙适配器不可用");
        }
    }
    
    @Override
    public void setScanConfig(SppScanConfig config) {
        this.scanConfig = config;
    }
    
    @Override
    public void startScan(SppScanCallback callback) {
        if (bluetoothAdapter == null) {
            YoBTSDKLog.e(TAG, "无法开始扫描，蓝牙适配器为空");
            return;
        }
        
        if (!bluetoothAdapter.isEnabled()) {
            YoBTSDKLog.e(TAG, "无法开始扫描，蓝牙未启用");
            return;
        }
        
        if (isScanning) {
            YoBTSDKLog.d(TAG, "已有扫描正在进行，停止先前的扫描");
            stopScan();
        }
        
        this.scanCallback = callback;
        
        // 注册扫描广播接收器
        IntentFilter filter = new IntentFilter();
        filter.addAction(BluetoothDevice.ACTION_FOUND);
        filter.addAction(BluetoothAdapter.ACTION_DISCOVERY_STARTED);
        filter.addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED);
        context.registerReceiver(scanReceiver, filter);
        
        // 清除已发现设备列表
        discoveredDevices.clear();
        
        // 开始扫描
        isScanning = bluetoothAdapter.startDiscovery();
        
        // 设置超时
        long timeout = scanConfig != null ? scanConfig.getScanTimeout() : SppScanConfig.createDefault().getScanTimeout();
        handler.postDelayed(this::stopScan, timeout);
    }
    
    /**
     * 检查设备是否符合过滤条件
     * @param device 蓝牙设备
     * @return 是否有效
     */
    private boolean isDeviceValid(BluetoothDevice device) {
        // 如果没有配置，使用默认过滤
        if (scanConfig == null) {
            return isClassicBluetoothDevice(device);
        }
        
        // 检查设备名称过滤
        String nameFilter = scanConfig.getDeviceNameFilter();
        if (!TextUtils.isEmpty(nameFilter)) {
            String deviceName = device.getName();
            if (deviceName == null || !deviceName.contains(nameFilter)) {
                return false;
            }
        }
        
        // 检查设备mac地址过滤
        String macFilter = scanConfig.getDeviceMacFilter();
        if (!TextUtils.isEmpty(macFilter)) {
            String deviceMac = device.getAddress();
            if (deviceMac == null || !deviceMac.contains(macFilter)) {
                return false;
            }
        }

        // 检查是否仅显示经典蓝牙设备
        if (scanConfig.isClassicBluetoothOnly()) {
            return isClassicBluetoothDevice(device);
        }
        
        return true;
    }
    
    /**
     * 判断设备是否是经典蓝牙设备（非BLE设备）
     * @param device 蓝牙设备
     * @return 是否是经典蓝牙设备
     */
    private boolean isClassicBluetoothDevice(BluetoothDevice device) {
        // 获取设备类型
        int deviceType;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            deviceType = device.getType();
            // 只接受DEVICE_TYPE_CLASSIC和DEVICE_TYPE_DUAL类型的设备
            if (deviceType == BluetoothDevice.DEVICE_TYPE_LE) {
                YoBTSDKLog.d(TAG, "过滤掉BLE设备: " + device.getName() + " - " + device.getAddress());
                return false;
            }
        }
        
        // 额外检查设备类，一些设备可能没有正确报告类型
        BluetoothClass bluetoothClass = device.getBluetoothClass();
        if (bluetoothClass != null) {
            int deviceClass = bluetoothClass.getMajorDeviceClass();
            // 检查设备主要类型是否有效（非0）
            // 如果设备类型为0，可能是BLE设备或者未报告类型的设备
            if (deviceClass == 0) {
                YoBTSDKLog.d(TAG, "过滤掉无效类型设备: " + device.getName() + " - " + device.getAddress());
                return false;
            }
        }
        
        // 检查设备是否支持SPP Profile
        // 此处可以添加额外检查，如方法UUID等
        
        return true;
    }
    
    @Override
    public void stopScan() {
        if (bluetoothAdapter != null && bluetoothAdapter.isEnabled() && bluetoothAdapter.isDiscovering()) {
            bluetoothAdapter.cancelDiscovery();
        }
        
        try {
            context.unregisterReceiver(scanReceiver);
        } catch (IllegalArgumentException e) {
            // 接收器可能尚未注册
            YoBTSDKLog.w(TAG, "接收器未注册: " + e.getMessage());
        }
        
        isScanning = false;
    }
    
    @Override
    public void close() {
        stopScan();
        discoveredDevices.clear();
        scanCallback = null;
    }
} 