package com.bes.sdk.message;


import java.io.Serializable;

/**
 * Sealing info
 */
public class SealingInfo implements Serializable {

    public static final int INVALID = -1;
    public static final int NOT_READY = 0;
    public static final int READY = 1;

    private int leftSealing;

    private int rightSealing;

    /**
     * Get left device sealing status
     * @return {@link #NOT_READY} for not ready, {@link #READY} ready or {@link #INVALID} invalid.
     */
    public int getLeftSealing() {
        return leftSealing;
    }

    public void setLeftSealing(int leftSealing) {
        this.leftSealing = leftSealing;
    }

    /**
     * Get right device sealing status
     * @return {@link #NOT_READY} for not ready, {@link #READY} ready or {@link #INVALID} invalid.
     */
    public int getRightSealing() {
        return rightSealing;
    }

    public void setRightSealing(int rightSealing) {
        this.rightSealing = rightSealing;
    }

    @Override
    public String toString() {
        return "SealingInfo{" +
                "leftSealing=" + leftSealing +
                ", rightSealing=" + rightSealing +
                '}';
    }
}
