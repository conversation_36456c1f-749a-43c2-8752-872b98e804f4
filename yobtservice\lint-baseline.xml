<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.8.0" type="baseline" client="gradle" dependencies="false" name="AGP (8.8.0)" variant="all" version="8.8.0">

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                    if (mScanConfig.getDeviceNameFilter() != null &amp;&amp; device.getName() != null) {"
        errorLine2="                                                                     ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/bleservice/scanner/impl/LegacyBleScanner.java"
            line="39"
            column="70"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                        if (!device.getName().contains(mScanConfig.getDeviceNameFilter())) {"
        errorLine2="                             ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/bleservice/scanner/impl/LegacyBleScanner.java"
            line="40"
            column="30"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="            String deviceName = device.getName();"
        errorLine2="                                ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/bleservice/scanner/ScanManager.java"
            line="43"
            column="33"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="            activity.startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT);"
        errorLine2="                                            ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/bleservice/YoBLEInitializer.java"
            line="91"
            column="45"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="        return device != null ? device.getName() : null;"
        errorLine2="                                ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/bleservice/YoCommandApi.java"
            line="113"
            column="33"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="        return String.format(COMMAND_FORMAT, position, functionType) + COMMAND_SUFFIX;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/bleservice/setting/controlcmd/ControlCommand.java"
            line="79"
            column="16"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="        String expectedPrefix = String.format(COMMAND_FORMAT, position, functionType).substring(0, 13);"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/bleservice/setting/controlcmd/ControlCommand.java"
            line="110"
            column="33"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="            sb.append(hex.toUpperCase());"
        errorLine2="                          ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/bleservice/utils/HexUtil.java"
            line="84"
            column="27"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; `SDK_INT` is never &lt; 24"
        errorLine1="        if (Build.VERSION.SDK_INT &lt; Build.VERSION_CODES.JELLY_BEAN_MR2) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/bleservice/scanner/BleScanner.java"
            line="43"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; `Build.VERSION.SDK_INT &lt; Build.VERSION_CODES.LOLLIPOP` is never true here"
        errorLine1="        } else if (Build.VERSION.SDK_INT &lt; Build.VERSION_CODES.LOLLIPOP) {"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/bleservice/scanner/BleScanner.java"
            line="46"
            column="20"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; `SDK_INT` is always >= 24"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/bleservice/gatt/GattConnectionManager.java"
            line="67"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; `SDK_INT` is always >= 18"
        errorLine1="@TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR2)"
        errorLine2="~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/bleservice/scanner/impl/LegacyBleScanner.java"
            line="16"
            column="1"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; `SDK_INT` is always >= 21"
        errorLine1="@TargetApi(Build.VERSION_CODES.LOLLIPOP)"
        errorLine2="~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/bleservice/scanner/impl/ModernBleScanner.java"
            line="23"
            column="1"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; `SDK_INT` is never &lt; 24"
        errorLine1="        if (Build.VERSION.SDK_INT &lt; Build.VERSION_CODES.JELLY_BEAN_MR2) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/bleservice/scanner/ScanManager.java"
            line="120"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; `Build.VERSION.SDK_INT &lt; Build.VERSION_CODES.LOLLIPOP` is never true here"
        errorLine1="        } else if (Build.VERSION.SDK_INT &lt; Build.VERSION_CODES.LOLLIPOP) {"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/bleservice/scanner/ScanManager.java"
            line="123"
            column="20"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `BleManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static BleManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/ggec/bleservice/core/BleManager.java"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `ScanManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static ScanManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/ggec/bleservice/scanner/ScanManager.java"
            line="26"
            column="13"/>
    </issue>

</issues>
