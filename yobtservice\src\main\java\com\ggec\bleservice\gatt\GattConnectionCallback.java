package com.ggec.bleservice.gatt;

import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGattService;

import java.util.List;
import java.util.UUID;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * GATT连接回调接口
 * 包含连接状态、服务发现和特征值操作的回调
 */
public interface GattConnectionCallback {
    
    /**
     * 连接状态变化回调
     * @param device 蓝牙设备
     * @param status 连接状态码
     * @param newState 新状态
     */
    void onConnectionStateChange(BluetoothDevice device, int status, int newState);
    
    /**
     * 服务发现完成回调
     * @param device 蓝牙设备
     * @param services 发现的服务列表
     * @param status 状态码
     */
    void onServicesDiscovered(BluetoothDevice device, List<BluetoothGattService> services, int status);
    
    /**
     * 特征值读取完成回调
     * @param device 蓝牙设备
     * @param serviceUUID 服务UUID
     * @param characteristicUUID 特征值UUID
     * @param value 读取到的数据
     * @param status 状态码
     */
    default void onCharacteristicRead(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, byte[] value, int status) {
        // 默认空实现，子类可选择性重写
    }
    
    /**
     * 特征值写入完成回调
     * @param device 蓝牙设备
     * @param serviceUUID 服务UUID
     * @param characteristicUUID 特征值UUID
     * @param status 状态码
     */
    default void onCharacteristicWrite(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, int status) {
        // 默认空实现，子类可选择性重写
    }
    
    /**
     * 特征值变化通知回调
     * @param device 蓝牙设备
     * @param serviceUUID 服务UUID
     * @param characteristicUUID 特征值UUID
     * @param value 变化的数据
     */
    default void onCharacteristicChanged(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, byte[] value) {
        // 默认空实现，子类可选择性重写
    }
    
    /**
     * MTU大小变更回调
     * @param device 蓝牙设备
     * @param mtu 新的MTU大小
     * @param status 状态码
     */
    default void onMtuChanged(BluetoothDevice device, int mtu, int status) {
        // 默认空实现，子类可选择性重写
    }

    /**
     * 通知启用/禁用状态变化回调
     * 当setCharacteristicNotification成功写入描述符后触发
     * @param device 蓝牙设备
     * @param serviceUUID 服务UUID
     * @param characteristicUUID 特征值UUID
     * @param status 状态码
     */
    default void onNotificationEnabled(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, int status) {
        // 默认空实现，子类可选择性重写
    }
} 