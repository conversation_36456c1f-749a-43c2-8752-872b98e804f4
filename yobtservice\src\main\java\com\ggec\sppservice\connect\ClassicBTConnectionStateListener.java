package com.ggec.sppservice.connect;

/**
 * 系统级经典蓝牙连接状态变更的监听器。
 * 此监听器用于在系统层面监控经典蓝牙设备的连接和断开事件。
 */
public interface ClassicBTConnectionStateListener {

    /**
     * 当一个经典蓝牙设备连接到系统时，此方法会被调用。
     *
     * @param macAddress 已连接设备的MAC地址。
     */
    void headsetIsConnected(String macAddress);

    /**
     * 当一个经典蓝牙设备从系统断开时，此方法会被调用。
     *
     * @param macAddress 已断开设备的MAC地址。
     */
    void headsetIsDisconnected(String macAddress);
} 