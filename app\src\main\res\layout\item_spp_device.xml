<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?android:attr/selectableItemBackground"
    android:padding="16dp">

    <ImageView
        android:id="@+id/iv_spp_device_icon"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@android:drawable/stat_sys_data_bluetooth"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:contentDescription="蓝牙设备" />

    <TextView
        android:id="@+id/tv_spp_device_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#333333"
        android:textSize="16sp"
        android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/btn_spp_connect"
        app:layout_constraintStart_toEndOf="@+id/iv_spp_device_icon"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="设备名称" />

    <TextView
        android:id="@+id/tv_spp_device_address"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#666666"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="@+id/tv_spp_device_name"
        app:layout_constraintStart_toStartOf="@+id/tv_spp_device_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_spp_device_name"
        tools:text="00:11:22:33:44:55" />

    <TextView
        android:id="@+id/tv_spp_device_rssi"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#999999"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="@+id/tv_spp_device_address"
        app:layout_constraintStart_toStartOf="@+id/tv_spp_device_address"
        app:layout_constraintTop_toBottomOf="@+id/tv_spp_device_address"
        tools:text="信号强度: -75 dBm" />

        <Button
            android:id="@+id/btn_spp_connect"
            style="@style/Widget.AppCompat.Button.Colored"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="72dp"
            android:text="连接"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 
</androidx.cardview.widget.CardView> 