plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.ggec.hs01'
    compileSdk 33

    defaultConfig {
        applicationId "com.ggec.hs01"
        minSdk 24
        targetSdk 33
        versionCode 1
        versionName "1.0.0.4.0802"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

//    signingConfigs{
//        release{
//            storeFile file("./keystore/release.jks")
//            keyAlias "ggec"
//            keyPassword "ggec123456"
//            storePassword "ggec123456"
//        }
//    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    // 解决.so库冲突问题 - 使用通配符匹配所有可能冲突的库文件
    packagingOptions {
        // 使用通配符匹配所有.so文件，选择第一个遇到的
        pickFirst 'lib/**/*.so'
        
        // 明确列出已知冲突的文件
        pickFirst 'lib/arm64-v8a/libimagequant.so'
        pickFirst 'lib/armeabi-v7a/libimagequant.so'
        pickFirst 'lib/arm64-v8a/liblcms2.so'
        pickFirst 'lib/armeabi-v7a/liblcms2.so'
        pickFirst 'lib/arm64-v8a/libpng.so'
        pickFirst 'lib/armeabi-v7a/libpng.so'
        pickFirst 'lib/arm64-v8a/libjpeg.so'
        pickFirst 'lib/armeabi-v7a/libjpeg.so'
        
        // 排除其他可能冲突的资源
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/ASL2.0'
    }
    
    lintOptions {
        baseline file("lint-baseline.xml")
        abortOnError false
    }
}

dependencies {

    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'libs')

    // 添加本地模块依赖
    implementation project(':yobtservice')
    implementation project(':yota')
    implementation project(':yovo')

    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.2.1'
    implementation 'androidx.activity:activity:1.2.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
}