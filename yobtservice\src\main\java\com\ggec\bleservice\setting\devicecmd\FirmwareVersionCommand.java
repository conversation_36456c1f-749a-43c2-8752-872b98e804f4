package com.ggec.bleservice.setting.devicecmd;

import com.ggec.bleservice.setting.Command;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import java.nio.charset.StandardCharsets;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 获取固件版本命令
 * 负责从耳机获取当前固件版本号信息
 */
public class FirmwareVersionCommand extends Command {
    private static final String TAG = "FirmwareVersionCommand";
    
    // 命令前缀，用于确认是获取固件版本命令
    private static final String COMMAND_PREFIX = "99EC94";
    
    // 发送命令数据
    private static final String COMMAND_DATA = "99EC94000100" + "1234";
    
    // 命令结束标识
    private static final String COMMAND_SUFFIX = "1234";
    
    // 版本号结果
    private String firmwareVersion = "";
    
    /**
     * 构造方法
     */
    public FirmwareVersionCommand() {
        super();
        // 设置命令前缀
        setCommandPrefix("99EC94");
    }
    
    @Override
    public String getCommandData() {
        return COMMAND_DATA;
    }
    
    @Override
    public String parseResponse(String responseData) {
        YoBTSDKLog.d(TAG, "解析响应数据: " + responseData);
        
        if (responseData == null || responseData.length() < 8) {
            notifyCompletion(ResultCode.FAILED, null, "响应数据格式错误");
            return "响应数据格式错误";
        }
        
        try {
            // 检查前缀是否匹配(99EC94)
            if (!responseData.startsWith(COMMAND_PREFIX)) {
                notifyCompletion(ResultCode.FAILED, null, "响应数据前缀不匹配");
                return "响应数据前缀不匹配";
            }
            
            // 提取长度信息（第4、5位，对应原始响应中的xx xx）
            int startIndex = COMMAND_PREFIX.length();
            if (responseData.length() < startIndex + 4) {
                notifyCompletion(ResultCode.FAILED, null, "响应数据长度不足");
                return "响应数据长度不足";
            }
            
            String lengthHex = responseData.substring(startIndex, startIndex + 4);
            int versionLength = Integer.parseInt(lengthHex, 16);
            
            // 提取版本号数据（从第6位开始，到结束标识前）
            int versionStartIndex = startIndex + 4;
            int versionEndIndex = responseData.length() - COMMAND_SUFFIX.length();
            
            if (versionEndIndex <= versionStartIndex || !responseData.endsWith(COMMAND_SUFFIX)) {
                notifyCompletion(ResultCode.FAILED, null, "响应数据格式错误，未找到结束标识");
                return "响应数据格式错误，未找到结束标识";
            }
            
            String versionHexData = responseData.substring(versionStartIndex, versionEndIndex);
            
            // 转换十六进制字符串为字节数组
            byte[] bytes = new byte[versionHexData.length() / 2];
            for (int i = 0; i < bytes.length; i++) {
                int index = i * 2;
                bytes[i] = (byte) Integer.parseInt(versionHexData.substring(index, index + 2), 16);
            }
            
            // 将字节数组用UTF-8解码
            firmwareVersion = new String(bytes, StandardCharsets.UTF_8);
            
            // 通知命令完成，yobackdata为版本号字符串
            notifyCompletion(ResultCode.SUCCESS, firmwareVersion, "获取固件版本成功");
            
            return firmwareVersion;
        } catch (NumberFormatException | StringIndexOutOfBoundsException e) {
            YoBTSDKLog.e(TAG, "解析版本号失败", e);
            
            // 通知命令失败，yobackdata为null
            notifyCompletion(ResultCode.FAILED, null, "版本号数据解析错误: " + e.getMessage());
            
            return "版本号数据解析错误: " + e.getMessage();
        }
    }
    
    @Override
    public boolean isResponseMatch(String responseData) {
        // 检查响应前缀是否匹配
        return responseData != null && responseData.length() >= 6 && responseData.startsWith(COMMAND_PREFIX);
    }
    
    /**
     * 获取固件版本号
     * @return 固件版本号字符串
     */
    public String getFirmwareVersion() {
        return firmwareVersion;
    }
} 