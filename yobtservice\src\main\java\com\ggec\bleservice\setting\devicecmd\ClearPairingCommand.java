package com.ggec.bleservice.setting.devicecmd;

import com.ggec.bleservice.setting.Command;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 清除配对记录命令
 * 负责清除耳机上存储的蓝牙配对历史记录
 */
public class ClearPairingCommand extends Command {
    private static final String TAG = "ClearPairingCommand";
    
    // 命令前缀，用于确认是清除配对记录命令
    private static final String COMMAND_PREFIX = "99EC930001";
    
    // 发送命令数据
    private static final String COMMAND_DATA = "99EC93000100" + "1234";
    
    // 命令校验和后缀
    private static final String COMMAND_SUFFIX = "1234";
    
    // 错误状态码
    private static final String ERROR_CODE = "FF";
    
    /**
     * 构造方法
     */
    public ClearPairingCommand() {
        super();
        // 设置命令前缀
        setCommandPrefix("99EC93");
    }
    
    @Override
    public String getCommandData() {
        return COMMAND_DATA;
    }
    
    @Override
    public String parseResponse(String responseData) {
        YoBTSDKLog.d(TAG, "解析响应数据: " + responseData);
        
        if (responseData == null || responseData.length() < 12) {
            return "响应数据格式错误";
        }
        
        // 获取状态字节
        String statusByte = responseData.substring(10, 12);
        
        // 判断是否成功
        boolean isSuccess = !ERROR_CODE.equals(statusByte);
        int resultValue = isSuccess ? 1 : 0;
        String result = isSuccess ? "清除配对记录成功" : "清除配对记录失败";
        
        // 通知命令完成
        notifyCompletion(isSuccess ? ResultCode.SUCCESS : ResultCode.FAILED, resultValue, result);
        
        return result;
    }
    
    @Override
    public boolean isResponseMatch(String responseData) {
        // 检查响应前缀是否匹配
        if (responseData != null && responseData.length() >= 10) {
            String prefix = responseData.substring(0, 10);
            return COMMAND_PREFIX.equals(prefix);
        }
        return false;
    }
} 