# 这些混淆规则将提供给SDK的使用方
# 保留SDK对外暴露的接口
-keep public class com.yovo.** {
    public *;
    protected *;
}

# 保留枚举
-keepclassmembers enum com.yovo.** {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保留Parcelable类
-keepclassmembers class com.yovo.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator CREATOR;
}

# 保留序列化类
-keepclassmembers class com.yovo.** implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    !static !transient <fields>;
    !private <fields>;
    !private <methods>;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}
