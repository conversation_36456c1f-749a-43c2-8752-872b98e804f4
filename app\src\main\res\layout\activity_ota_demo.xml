<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".otatest.OtaDemoActivity">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="OTA升级演示"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp"/>

    <!-- OTA进度显示 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="升级进度:"
        android:textSize="16sp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"/>

    <ProgressBar
        android:id="@+id/progress_bar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:max="100"
        android:progress="0"
        android:layout_marginBottom="8dp"/>

    <TextView
        android:id="@+id/tv_ota_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="0%"
        android:layout_gravity="center_horizontal"
        android:textSize="16sp"
        android:layout_marginBottom="24dp"/>

    <!-- 开始OTA按钮 -->
    <Button
        android:id="@+id/btn_start_ota"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="开始OTA升级"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="16dp"/>

    <!-- 提示信息 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="提示: 请确保设备已通过SPP正确连接，并且OTA升级文件已准备就绪。"
        android:textSize="14sp"
        android:textStyle="italic"
        android:layout_marginTop="16dp"
        android:gravity="center"/>

</LinearLayout> 