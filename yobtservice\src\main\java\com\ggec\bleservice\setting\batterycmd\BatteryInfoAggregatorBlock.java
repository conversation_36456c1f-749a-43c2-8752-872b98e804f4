package com.ggec.bleservice.setting.batterycmd;

import com.ggec.bleservice.setting.Command;
import com.ggec.bleservice.setting.CommandBlock;
import com.ggec.bleservice.setting.cmdmanager.BatterySettingsManager;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import java.util.Map;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-29
 * Description: 
 * 电池信息聚合命令块
 * 负责按顺序获取所有电池相关信息：左耳电量、右耳电量、充电盒电量、充电盒充电状态
 * 确保命令按顺序原子性执行，避免被其他命令干扰
 */
public class BatteryInfoAggregatorBlock extends CommandBlock {
    private static final String TAG = "BatteryInfoAggregatorBlock";
    
    private final BatterySettingsManager batteryManager;
    private int step = 0;
    
    // 等待最后一个命令完成的标志
    private boolean waitingForLastCommand = false;
    
    // 存储收集到的数据 - 修改为String类型支持"--"显示
    private String leftBatteryLevel = "--";
    private String rightBatteryLevel = "--";
    private String caseBatteryLevel = "--";
    private int caseChargingStatus = 0;
    
    /**
     * 构造方法
     * @param batteryManager 电池设置管理器实例
     */
    public BatteryInfoAggregatorBlock(BatterySettingsManager batteryManager) {
        this.batteryManager = batteryManager;
    }
    
    @Override
    public Command nextCommand(Command previousCommand) {
        if (previousCommand != null) {
            // 只有当上一个命令成功完成时，才存储其结果
            if (previousCommand.getState() == Command.State.COMPLETED) {
                // 存储上一个命令的结果
                if (previousCommand instanceof BatteryLeftCommand) {
                    leftBatteryLevel = ((BatteryLeftCommand) previousCommand).getBatteryLevel();
                    YoBTSDKLog.d(TAG, "收集到左耳电量: " + leftBatteryLevel);
                } else if (previousCommand instanceof BatteryRightCommand) {
                    rightBatteryLevel = ((BatteryRightCommand) previousCommand).getBatteryLevel();
                    YoBTSDKLog.d(TAG, "收集到右耳电量: " + rightBatteryLevel);
                } else if (previousCommand instanceof BatteryCaseCommand) {
                    caseBatteryLevel = ((BatteryCaseCommand) previousCommand).getBatteryLevel();
                    YoBTSDKLog.d(TAG, "收集到充电盒电量: " + caseBatteryLevel);
                } else if (previousCommand instanceof CaseChargingStatusCommand) {
                    caseChargingStatus = ((CaseChargingStatusCommand) previousCommand).getChargingStatus();
                    YoBTSDKLog.d(TAG, "收集到充电盒充电状态: " + caseChargingStatus);
                    // 最后一个命令完成，标记等待状态结束
                    if (waitingForLastCommand) {
                        YoBTSDKLog.d(TAG, "最后一个命令已完成，准备结束命令块");
                        return null; // 现在可以安全地结束命令块
                    }
                }
            } else {
                YoBTSDKLog.w(TAG, "上一个命令 " + previousCommand.getCommandId() + " 失败或未完成，跳过结果收集");
                // 如果是最后一个命令失败，也需要结束等待
                if (waitingForLastCommand && previousCommand instanceof CaseChargingStatusCommand) {
                    YoBTSDKLog.w(TAG, "最后一个命令失败，结束命令块");
                    return null;
                }
            }
        }
        
        // 如果正在等待最后一个命令完成，但上一个命令不是CaseChargingStatusCommand，继续等待
        if (waitingForLastCommand) {
            YoBTSDKLog.d(TAG, "正在等待最后一个命令完成...");
            return null;
        }
        
        step++;
        switch (step) {
            case 1:
                YoBTSDKLog.d(TAG, "步骤 1: 获取左耳电量");
                return new BatteryLeftCommand();
            case 2:
                YoBTSDKLog.d(TAG, "步骤 2: 获取右耳电量");
                return new BatteryRightCommand();
            case 3:
                YoBTSDKLog.d(TAG, "步骤 3: 获取充电盒电量");
                return new BatteryCaseCommand();
            case 4:
                YoBTSDKLog.d(TAG, "步骤 4: 获取充电盒充电状态");
                waitingForLastCommand = true; // 设置等待标志
                return new CaseChargingStatusCommand();
            default:
                return null; // 命令块结束
        }
    }
    
    @Override
    public void onBlockCompleted(Map<String, Object> results) {
        YoBTSDKLog.i(TAG, "电池信息聚合命令块执行完成");
        YoBTSDKLog.d(TAG, String.format("聚合结果 - 左耳:%s%%, 右耳:%s%%, 盒子:%s%%, 充电状态:%s", 
                leftBatteryLevel, rightBatteryLevel, caseBatteryLevel, 
                caseChargingStatus == 1 ? "充电中" : "未充电"));
        
        // 直接更新BatterySettingsManager的数据
        updateBatteryManagerData();
        
        // 触发所有相关的监听器回调
        notifyAllListeners();
        
        // 重置聚合操作标志
        batteryManager.resetBatteryAggregationFlag();
        
        // 重置状态
        resetState();
    }
    
    @Override
    public void onBlockFailed(int code, String message) {
        YoBTSDKLog.e(TAG, "电池信息聚合命令块执行失败: " + message);
        
        // 如果聚合失败，仍然尝试更新已收集到的数据
        updateBatteryManagerData();
        
        // 通知失败，但传递已收集到的数据
        notifyAllListenersWithError(code);
        
        // 重置聚合操作标志
        batteryManager.resetBatteryAggregationFlag();
        
        // 重置状态
        resetState();
    }
    
    /**
     * 重置命令块状态
     */
    private void resetState() {
        step = 0;
        waitingForLastCommand = false;
        leftBatteryLevel = "--";
        rightBatteryLevel = "--";
        caseBatteryLevel = "--";
        caseChargingStatus = 0;
    }
    
    /**
     * 更新BatterySettingsManager的内部数据
     */
    private void updateBatteryManagerData() {
        // 通过反射或者提供setter方法来更新数据
        // 这里我们需要在BatterySettingsManager中添加相应的方法
        if (batteryManager != null) {
            batteryManager.updateBatteryLevels(leftBatteryLevel, rightBatteryLevel, caseBatteryLevel);
            batteryManager.updateCaseChargingStatus(caseChargingStatus);
        }
    }
    
    /**
     * 通知所有监听器（成功状态）
     */
    private void notifyAllListeners() {
        if (batteryManager != null) {
            batteryManager.notifyBatteryLevelsUpdated(Command.ResultCode.SUCCESS);
            batteryManager.notifyCaseChargingStatusUpdated(Command.ResultCode.SUCCESS);
        }
    }
    
    /**
     * 通知所有监听器（失败状态）
     */
    private void notifyAllListenersWithError(int code) {
        if (batteryManager != null) {
            batteryManager.notifyBatteryLevelsUpdated(code);
            batteryManager.notifyCaseChargingStatusUpdated(code);
        }
    }
    
    @Override
    public long getCommandInterval() {
        // 电量聚合使用优化间隔80ms
        // 基于实际响应时间分析：平均90ms，最长170ms
        // 80ms间隔既能提升性能（比200ms快2.5倍），又确保稳定性
        // 给设备留出充足的处理时间，避免命令重叠
        return 80;
    }
    
    @Override
    public long getBlockCompletionInterval() {
        // 电量聚合完成后使用适中间隔，确保系统稳定
        return 120;
    }
} 