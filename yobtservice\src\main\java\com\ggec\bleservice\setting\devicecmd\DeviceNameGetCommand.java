package com.ggec.bleservice.setting.devicecmd;

import com.ggec.bleservice.setting.Command;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import java.nio.charset.StandardCharsets;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 获取蓝牙设备名称命令
 * 负责从耳机获取当前设置的蓝牙设备名称
 */
public class DeviceNameGetCommand extends Command {
    private static final String TAG = "DeviceNameGetCommand";
    
    // 命令前缀，用于确认是获取设备名命令
    private static final String COMMAND_PREFIX = "99EC99";
    
    // 完整的发送命令
    private static final String COMMAND = "99EC99000100";
    
    // 命令校验和后缀
    private static final String COMMAND_SUFFIX = "1234";
    
    private String parsedDeviceName;

    /**
     * 构造方法
     */
    public DeviceNameGetCommand() {
        super();
        // 设置命令前缀
        setCommandPrefix("99EC99");
    }
    
    @Override
    public String getCommandData() {
        return COMMAND + COMMAND_SUFFIX;
    }
    
    @Override
    public String parseResponse(String responseData) {
        YoBTSDKLog.d(TAG, "解析响应数据: " + responseData);
        
        if (responseData == null || responseData.length() < 10) {
            String errorMsg = "响应数据格式错误";
            notifyCompletion(ResultCode.FAILED, null, errorMsg);
            return errorMsg;
        }
        
        try {
            // 获取设备名长度（第4和第5位，也就是索引位置6-10）
            String lengthHex = responseData.substring(6, 10);
            int nameLength = Integer.parseInt(lengthHex, 16);
            
            // 计算设备名结束位置
            int endIndex = 10 + nameLength * 2;
            if (responseData.length() < endIndex) {
                throw new IllegalArgumentException("响应数据长度不足");
            }
            
            // 提取设备名称的十六进制字符串
            String deviceNameHex = responseData.substring(10, endIndex);
            
            // 将十六进制字符串转换为字节数组
            byte[] bytes = new byte[deviceNameHex.length() / 2];
            for (int i = 0; i < bytes.length; i++) {
                bytes[i] = (byte) Integer.parseInt(deviceNameHex.substring(i * 2, i * 2 + 2), 16);
            }
            
            // 使用UTF-8解码字节数组
            String deviceName = new String(bytes, StandardCharsets.UTF_8);
            this.parsedDeviceName = deviceName;
            
            String message = "获取设备名成功";
            notifyCompletion(ResultCode.SUCCESS, deviceName, message);
            return message;
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "解析设备名失败", e);
            String errorMsg = "解析设备名失败: " + e.getMessage();
            notifyCompletion(ResultCode.FAILED, null, errorMsg);
            return errorMsg;
        }
    }
    
    @Override
    public boolean isResponseMatch(String responseData) {
        // 检查响应前缀是否匹配
        if (responseData != null && responseData.length() >= 6) {
            String prefix = responseData.substring(0, 6);
            return COMMAND_PREFIX.equals(prefix);
        }
        return false;
    }

    public String getParsedDeviceName() {
        return parsedDeviceName;
    }
} 