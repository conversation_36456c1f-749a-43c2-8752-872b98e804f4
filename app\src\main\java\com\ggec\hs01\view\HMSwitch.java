package com.ggec.hs01.view;

import android.content.Context;
import android.util.AttributeSet;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 转发类，保持兼容性
 * 通过ThemeHelper间接访问，减少模块间耦合
 */
public class HMSwitch extends com.yovo.yotheme.YoSwitch {
    public HMSwitch(Context context) {
        super(context);
    }
    
    public HMSwitch(Context context, AttributeSet attrs) {
        super(context, attrs);
    }
    
    public HMSwitch(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }
    
    /**
     * 保持兼容性的内部接口
     * 代理到新的接口实现
     */
    public interface OnCheckedChangeListener {
        void onCheckedChanged(HMSwitch switchView, boolean isChecked);
    }
    
    /**
     * 重写setOnCheckedChangeListener方法，保持兼容性
     * @param listener 旧接口的监听器
     */
    public void setOnCheckedChangeListener(OnCheckedChangeListener listener) {
        // 创建适配器，将旧接口转换为新接口
        super.setOnCheckedChangeListener((switchView, isChecked) -> {
            listener.onCheckedChanged(this, isChecked);
        });
    }
} 