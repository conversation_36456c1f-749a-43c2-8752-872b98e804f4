package com.ggec.bleservice.scanner;

import android.bluetooth.le.ScanSettings;
import android.os.ParcelUuid;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-17
 * Description: 
 * 蓝牙扫描配置类
 * 使用建造者模式支持链式调用
 */
public class ScanConfig {
    // 扫描模式
    public static final int SCAN_MODE_LOW_POWER = ScanSettings.SCAN_MODE_LOW_POWER;       // 低功耗
    public static final int SCAN_MODE_BALANCED = ScanSettings.SCAN_MODE_BALANCED;         // 平衡
    public static final int SCAN_MODE_LOW_LATENCY = ScanSettings.SCAN_MODE_LOW_LATENCY;   // 低延迟
    
    // 默认超时时间10秒
    private static final long DEFAULT_TIMEOUT = 10000;

    // 设备内部特征码
    private static final String OUR_FEATURE_CODE = "86103800900000100000fffe";
    
    // 扫描超时时间（毫秒）
    private long scanTimeout = DEFAULT_TIMEOUT;
    
    // 扫描模式
    private int scanMode = SCAN_MODE_LOW_LATENCY;
    
    // 设备名称过滤
    private String deviceNameFilter;
    
    // 特征码过滤
    private String featureCodeFilter;
    
    // 服务UUID过滤
    private List<ParcelUuid> serviceUuids;
    
    /**
     * 创建默认配置
     */
    public static ScanConfig createDefault() {
        return new ScanConfig();
    }
    
    /**
     * 创建新的构建器
     */
    public static Builder newBuilder() {
        return new Builder();
    }
    
    /**
     * 设置扫描超时时间
     */
    public ScanConfig setScanTimeout(long timeoutMillis) {
        this.scanTimeout = timeoutMillis > 0 ? timeoutMillis : DEFAULT_TIMEOUT;
        return this;
    }
    
    /**
     * 设置扫描模式
     */
    public ScanConfig setScanMode(int mode) {
        this.scanMode = mode;
        return this;
    }
    
    /**
     * 设置设备名称过滤
     */
    public ScanConfig setDeviceNameFilter(String deviceName) {
        this.deviceNameFilter = deviceName;
        return this;
    }
    
    /**
     * 设置特征码过滤
     */
    public ScanConfig setFeatureCodeFilter(String featureCode) {
        this.featureCodeFilter = featureCode;
        return this;
    }

    /**
     * 设置是否基于我们内部的特征码进行过滤
     * @param enable true表示启用过滤，false表示禁用
     */
    public ScanConfig useOurFeatureCodeFilter(boolean enable) {
        this.featureCodeFilter = enable ? OUR_FEATURE_CODE : null;
        return this;
    }
    
    /**
     * 添加服务UUID过滤
     */
    public ScanConfig addServiceUuid(ParcelUuid uuid) {
        if (serviceUuids == null) {
            serviceUuids = new ArrayList<>();
        }
        if (uuid != null && !serviceUuids.contains(uuid)) {
            serviceUuids.add(uuid);
        }
        return this;
    }
    
    /**
     * 获取扫描超时时间
     */
    public long getScanTimeout() {
        return scanTimeout;
    }
    
    /**
     * 获取扫描模式
     */
    public int getScanMode() {
        return scanMode;
    }
    
    /**
     * 获取设备名称过滤
     */
    public String getDeviceNameFilter() {
        return deviceNameFilter;
    }
    
    /**
     * 获取特征码过滤
     */
    public String getFeatureCodeFilter() {
        return featureCodeFilter;
    }
    
    /**
     * 获取服务UUID过滤列表
     */
    public List<ParcelUuid> getServiceUuids() {
        return serviceUuids;
    }
    
    /**
     * 扫描配置构建器
     * 支持链式调用方法
     */
    public static class Builder {
        private final ScanConfig config = new ScanConfig();
        
        /**
         * 设置扫描超时时间
         * @param timeoutMillis 超时时间（毫秒）
         */
        public Builder timeout(long timeoutMillis) {
            config.setScanTimeout(timeoutMillis);
            return this;
        }
        
        /**
         * 设置低功耗扫描模式
         */
        public Builder lowPower() {
            config.setScanMode(SCAN_MODE_LOW_POWER);
            return this;
        }
        
        /**
         * 设置平衡扫描模式
         */
        public Builder balanced() {
            config.setScanMode(SCAN_MODE_BALANCED);
            return this;
        }
        
        /**
         * 设置低延迟扫描模式
         */
        public Builder lowLatency() {
            config.setScanMode(SCAN_MODE_LOW_LATENCY);
            return this;
        }
        
        /**
         * 设置设备名称过滤
         * @param deviceName 设备名称
         */
        public Builder nameFilter(String deviceName) {
            config.setDeviceNameFilter(deviceName);
            return this;
        }
        
        /**
         * 设置特征码过滤
         * @param featureCode 特征码
         */
        public Builder featureCodeFilter(String featureCode) {
            config.setFeatureCodeFilter(featureCode);
            return this;
        }

        /**
         * 设置是否启用内部特征码过滤
         * @param enable 是否启用
         */
        public Builder useOurFeatureCodeFilter(boolean enable) {
            config.useOurFeatureCodeFilter(enable);
            return this;
        }
        
        /**
         * 添加服务UUID过滤
         * @param uuid 服务UUID
         */
        public Builder serviceUuid(ParcelUuid uuid) {
            config.addServiceUuid(uuid);
            return this;
        }
        
        /**
         * 构建ScanConfig实例
         */
        public ScanConfig build() {
            return config;
        }
    }
} 