package com.bes.sdk.core;


import java.io.File;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/6/21 17:43
 */

public class OtaLog {
    static ILogger sLogger;
    public static boolean isWriteFile = true;
    public static boolean isWriteLog = true;
    public static String sLogPath = null;

    public static void init(String logPath, int maxFileSize) {
        sLogPath = logPath;
        File pathFile = new File(logPath);
        if (pathFile.isDirectory()) {
            File[] fileList = pathFile.listFiles();
            if (fileList != null) {
                if (fileList.length > maxFileSize) {
                    Arrays.sort(fileList, (x, y) -> Long.compare(y.lastModified(), x.lastModified()));
                    for (int i = maxFileSize; i < fileList.length; i++) {
                        fileList[i].delete();
                    }
                }
            }
        }else{
            pathFile.mkdirs();
        }
    }
    public static void logI(String text) {
        if (sLogger != null) sLogger.logI(text);
    }

    public static void logE(String text) {
        if (sLogger != null) sLogger.logE(text);
    }

    public static void setLogger(ILogger logger) {
        sLogger = logger;
    }

    public interface ILogger {
        void logI(String text);

        void logE(String text);
    }
}
