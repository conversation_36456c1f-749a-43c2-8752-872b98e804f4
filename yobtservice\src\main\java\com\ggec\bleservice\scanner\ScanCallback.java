package com.ggec.bleservice.scanner;

import android.bluetooth.BluetoothDevice;
import android.os.ParcelUuid;

import java.util.List;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-17
 * Description: 
 * 蓝牙扫描回调接口，用于通知扫描事件
 */
public interface ScanCallback {
    /**
     * 发现设备时回调
     * @param device 发现的蓝牙设备
     * @param rssi 信号强度
     * @param scanRecord 原始扫描记录
     * @param serviceUuids 从扫描记录中解析出的服务UUID列表
     */
    void onFound(BluetoothDevice device, int rssi, byte[] scanRecord, List<ParcelUuid> serviceUuids);
    
    /**
     * 扫描开始时回调
     */
    void onScanStart();
    
    /**
     * 扫描结束时回调
     */
    void onScanFinish();
} 