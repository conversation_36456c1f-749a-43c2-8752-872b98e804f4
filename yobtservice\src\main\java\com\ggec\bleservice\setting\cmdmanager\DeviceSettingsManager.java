package com.ggec.bleservice.setting.cmdmanager;

import com.ggec.bleservice.setting.devicecmd.ClassicBtAddressCommand;
import com.ggec.bleservice.setting.devicecmd.DeviceRenameCommand;
import com.ggec.bleservice.setting.devicecmd.DeviceNameGetCommand;
import com.ggec.bleservice.setting.devicecmd.EarColorCommand;
import com.ggec.bleservice.setting.devicecmd.ClearPairingCommand;
import com.ggec.bleservice.setting.devicecmd.FirmwareVersionCommand;
import com.ggec.bleservice.setting.devicecmd.GlobalStatusCommand;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 设备设置管理器
 * 负责管理所有设备设置相关的命令发送和接收，包括耳机颜色、固件版本、配对记录等
 */
public class DeviceSettingsManager extends BaseCommandManager {
    private static final String TAG = "DeviceSettingsManager";
    
    // 单例实例
    private static DeviceSettingsManager instance;
    
    /**
     * 获取单例实例
     */
    public static synchronized DeviceSettingsManager getInstance() {
        if (instance == null) {
            instance = new DeviceSettingsManager();
        }
        return instance;
    }
    
    /**
     * 私有构造方法
     */
    private DeviceSettingsManager() {
        init(); // 调用父类初始化方法
    }
    
    /**
     * 释放资源
     */
    @Override
    public void release() {
        super.release();
        instance = null;
    }
    
    /**
     * 获取耳机颜色
     * 异步获取耳机颜色，结果通过命令回调返回
     */
    public void getEarColor() {
        YoBTSDKLog.i(TAG, "获取耳机颜色");
        
        // 创建获取耳机颜色命令
        EarColorCommand command = new EarColorCommand();
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 清除配对记录
     */
    public void clearPairing() {
        YoBTSDKLog.i(TAG, "清除配对记录");
        
        // 创建清除配对记录命令
        ClearPairingCommand command = new ClearPairingCommand();
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 获取固件版本号
     */
    public void getFirmwareVersion() {
        YoBTSDKLog.i(TAG, "获取固件版本号");
        
        // 创建获取固件版本命令
        FirmwareVersionCommand command = new FirmwareVersionCommand();
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 修改耳机名称
     * @param deviceName 要设置的设备名称
     */
    public void setDeviceName(String deviceName) {
        YoBTSDKLog.i(TAG, "修改耳机名称: " + deviceName);
        
        // 创建修改耳机名称命令
        DeviceRenameCommand command = new DeviceRenameCommand(deviceName);
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 获取蓝牙设备名称
     */
    public void getDeviceName() {
        YoBTSDKLog.i(TAG, "获取蓝牙设备名称");
        
        // 创建获取蓝牙设备名称命令
        DeviceNameGetCommand command = new DeviceNameGetCommand();
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 获取经典蓝牙（SPP）MAC地址
     */
    public void getClassicBtAddress() {
        YoBTSDKLog.i(TAG, "获取经典蓝牙MAC地址");
        
        // 创建获取经典蓝牙MAC地址命令
        ClassicBtAddressCommand command = new ClassicBtAddressCommand();
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 获取设备全局状态
     * 获取所有功能的状态，包括各种开关状态和模式设置
     */
    public void getGlobalStatus() {
        YoBTSDKLog.i(TAG, "获取设备全局状态");
        
        // 创建获取设备全局状态命令
        GlobalStatusCommand command = new GlobalStatusCommand();
        
        // 执行命令
        createAndEnqueueCommand(command);
    }

    @Override
    protected String getManagerTag() {
        return TAG;
    }
    
    @Override
    protected boolean isDataRelevant(String data) {
        // 检查数据前缀是否与应用设置相关
        if (data != null && data.length() >= 6) {
            String prefix = data.substring(0, 6); // 取前6个字符 (99ECxx)
            
            // 耳机颜色 99EC91（实际返回）
            // 修改耳机名称 99EC92
            // 清除配对记录 99EC93
            // 固件版本号 99EC94
            // 获取经典蓝牙MAC地址 99EC97
            // 大状态获取 99EC98
            // 获取蓝牙设备名称 99EC99
            return "99EC91".equals(prefix) || "99EC92".equals(prefix) || 
                   "99EC93".equals(prefix) || "99EC94".equals(prefix) ||
                   "99EC97".equals(prefix) || "99EC98".equals(prefix) ||
                   "99EC99".equals(prefix);
        }
        return false;
    }
} 