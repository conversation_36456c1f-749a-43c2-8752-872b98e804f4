package com.yovo.yotheme;

import android.content.Context;

/**
 * 主题管理接口，用于获取主题化组件和设置全局主题
 */
public interface ThemeManager {
    /**
     * 获取主题化的开关组件
     * @param context 上下文
     * @return YoSwitch实例
     */
    YoSwitch getSwitch(Context context);
    
    /**
     * 获取主题化的按钮组件
     * @param context 上下文
     * @return YoButton实例
     */
    YoButton getButton(Context context);
    
    /**
     * 设置全局主题色
     * @param primaryColor 主色调
     * @param secondaryColor 次级色调
     */
    void setThemeColors(int primaryColor, int secondaryColor);
    
    /**
     * 获取默认实现的主题管理器
     * @return ThemeManager实例
     */
    static ThemeManager getDefault() {
        return DefaultThemeManager.getInstance();
    }
} 