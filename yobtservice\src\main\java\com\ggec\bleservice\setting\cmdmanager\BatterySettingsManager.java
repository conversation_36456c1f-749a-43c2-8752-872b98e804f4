package com.ggec.bleservice.setting.cmdmanager;

import android.os.Handler;
import android.os.Looper;

import com.ggec.bleservice.core.BleDataCallback;
import com.ggec.bleservice.core.BleDataService;
import com.ggec.bleservice.setting.Command;
import com.ggec.bleservice.setting.CommandQueueCenter;
import com.ggec.bleservice.setting.batterycmd.BatteryLeftCommand;
import com.ggec.bleservice.setting.batterycmd.BatteryRightCommand;
import com.ggec.bleservice.setting.batterycmd.BatteryCaseCommand;
import com.ggec.bleservice.setting.batterycmd.CaseChargingStatusCommand;
import com.ggec.bleservice.setting.batterycmd.EarInCaseStatusCommand;
import com.ggec.bleservice.setting.batterycmd.BatteryInfoAggregatorBlock;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import java.util.LinkedList;
import java.util.Queue;
import java.util.List;
import java.util.ArrayList;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 电池设置管理器
 * 负责监控和管理耳机及充电盒的电池电量、充电状态等信息
 */
public class BatterySettingsManager extends BaseCommandManager {
    private static final String TAG = "BatterySettingsManager";
    
    // 单例实例
    private static BatterySettingsManager instance;
    
    // 标志位，用于控制是否处理主动上报的数据
    private boolean isMonitoring = false;
    
    // 标志位，用于防止多个电量聚合命令块同时执行
    private volatile boolean isBatteryAggregationRunning = false;
    
    // 电池电量数据 - 修改为String类型支持"--"显示
    private String leftBatteryLevel = "--";
    private String rightBatteryLevel = "--";
    private String caseBatteryLevel = "--";
    private int caseChargingStatus = 0; // 0:不在充电，1:在充电
    private int earInCaseStatus = 0;    // 0:都不在盒子内，1:左耳在，2:右耳在，3:都在盒子内
    
    /**
     * 电池电量监听器
     */
    public interface BatteryLevelListener {
        /**
         * 电池电量更新回调
         * @param code 结果状态码
         * @param leftLevel 左耳电量字符串（百分比数字或"--"）
         * @param rightLevel 右耳电量字符串（百分比数字或"--"）
         * @param caseLevel 盒子电量字符串（百分比数字或"--"）
         */
        void onBatteryLevelsUpdated(int code, String leftLevel, String rightLevel, String caseLevel);
    }
    
    /**
     * 充电盒充电状态监听器
     */
    public interface CaseChargingStatusListener {
        /**
         * 充电盒充电状态更新回调
         * @param code 结果状态码
         * @param isCharging 是否在充电 (true:在充电，false:不在充电)
         */
        void onCaseChargingStatusUpdated(int code, boolean isCharging);
    }
    
    /**
     * 耳机在充电盒内状态监听器
     */
    public interface EarInCaseStatusListener {
        /**
         * 耳机在充电盒内状态更新回调
         * @param code 结果状态码
         * @param status 状态值（0:都不在，1:左耳在，2:右耳在，3:都在）
         * @param leftInCase 左耳是否在充电盒内
         * @param rightInCase 右耳是否在充电盒内
         */
        void onEarInCaseStatusUpdated(int code, int status, boolean leftInCase, boolean rightInCase);
    }
    
    // 电池电量监听器
    private BatteryLevelListener batteryLevelListener;
    
    // 充电盒充电状态监听器
    private CaseChargingStatusListener caseChargingStatusListener;
    
    // 耳机在充电盒内状态监听器
    private EarInCaseStatusListener earInCaseStatusListener;
    
    /**
     * 设置电池电量监听器
     * @param listener 监听器
     */
    public void setBatteryLevelListener(BatteryLevelListener listener) {
        this.batteryLevelListener = listener;
    }
    
    /**
     * 设置充电盒充电状态监听器
     * @param listener 监听器
     */
    public void setCaseChargingStatusListener(CaseChargingStatusListener listener) {
        this.caseChargingStatusListener = listener;
    }
    
    /**
     * 设置耳机在充电盒内状态监听器
     * @param listener 监听器
     */
    public void setEarInCaseStatusListener(EarInCaseStatusListener listener) {
        this.earInCaseStatusListener = listener;
    }
    
    /**
     * 开始监听主动上报的数据
     */
    public void startMonitoring() {
        YoBTSDKLog.d(TAG, "开始监听主动上报的电池数据");
        this.isMonitoring = true;
    }

    /**
     * 停止监听主动上报的数据
     */
    public void stopMonitoring() {
        YoBTSDKLog.d(TAG, "停止监听主动上报的电池数据");
        this.isMonitoring = false;
    }
    
    /**
     * 获取左耳电量
     */
    public String getLeftBatteryLevel() {
        return leftBatteryLevel;
    }
    
    /**
     * 获取右耳电量
     */
    public String getRightBatteryLevel() {
        return rightBatteryLevel;
    }
    
    /**
     * 获取盒子电量
     */
    public String getCaseBatteryLevel() {
        return caseBatteryLevel;
    }
    
    /**
     * 获取充电盒充电状态
     * @return 充电状态值 (0:不在充电，1:在充电)
     */
    public int getCaseChargingStatus() {
        return caseChargingStatus;
    }
    
    /**
     * 充电盒是否正在充电
     * @return 是否在充电
     */
    public boolean isCaseCharging() {
        return caseChargingStatus == 1;
    }
    
    /**
     * 获取耳机在充电盒内的状态
     * @return 状态值 (0:都不在盒子内，1:左耳在，2:右耳在，3:都在盒子内)
     */
    public int getEarInCaseStatus() {
        return earInCaseStatus;
    }
    
    /**
     * 左耳是否在充电盒内
     * @return 是否在充电盒内
     */
    public boolean isLeftEarInCase() {
        return earInCaseStatus == 1 || earInCaseStatus == 3;
    }
    
    /**
     * 右耳是否在充电盒内
     * @return 是否在充电盒内
     */
    public boolean isRightEarInCase() {
        return earInCaseStatus == 2 || earInCaseStatus == 3;
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized BatterySettingsManager getInstance() {
        if (instance == null) {
            instance = new BatterySettingsManager();
        }
        return instance;
    }
    
    /**
     * 私有构造方法
     */
    private BatterySettingsManager() {
        init(); // 调用父类初始化方法
    }
    
    /**
     * 释放资源
     */
    @Override
    public void release() {
        super.release();
        instance = null;
    }
    
    /**
     * 获取左耳电量
     */
    public void getLeftBattery() {
        YoBTSDKLog.i(TAG, "获取左耳电量");
        
        // 创建左耳电量命令
        BatteryLeftCommand command = new BatteryLeftCommand();
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 获取右耳电量
     */
    public void getRightBattery() {
        YoBTSDKLog.i(TAG, "获取右耳电量");
        
        // 创建右耳电量命令
        BatteryRightCommand command = new BatteryRightCommand();
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 获取充电盒电量
     */
    public void getCaseBattery() {
        YoBTSDKLog.i(TAG, "获取充电盒电量");
        
        // 创建充电盒电量命令
        BatteryCaseCommand command = new BatteryCaseCommand();
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 获取充电盒充电状态
     */
    public void requestCaseChargingStatus() {
        YoBTSDKLog.i(TAG, "获取充电盒充电状态");
        
        // 创建充电盒充电状态命令
        CaseChargingStatusCommand command = new CaseChargingStatusCommand();
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 获取耳机在充电盒内状态
     */
    public void requestEarInCaseStatus() {
        YoBTSDKLog.i(TAG, "获取耳机在充电盒内状态");
        
        // 创建耳机在充电盒内状态命令
        EarInCaseStatusCommand command = new EarInCaseStatusCommand();
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 获取所有电池电量
     * 一次性获取左耳、右耳和充电盒的电量信息
     */
    public void getAllBatteryLevels() {
        YoBTSDKLog.i(TAG, "获取所有电池电量");
        
        // 获取左耳电量
        getLeftBattery();
        
        // 获取右耳电量
        getRightBattery();
        
        // 获取充电盒电量
        getCaseBattery();
    }
    
    /**
     * 获取完整电池信息（使用聚合命令块）
     * 包括电量和充电状态，确保原子性执行不被干扰
     */
    public void getFullBatteryInfo() {
        // 检查是否已有电量聚合操作在进行
        if (isBatteryAggregationRunning) {
            YoBTSDKLog.w(TAG, "电量聚合操作已在进行中，跳过重复请求");
            return;
        }
        
        YoBTSDKLog.i(TAG, "使用聚合命令块获取完整电池信息");
        
        // 设置聚合操作标志
        isBatteryAggregationRunning = true;
        
        // 创建聚合命令块
        BatteryInfoAggregatorBlock aggregatorBlock = new BatteryInfoAggregatorBlock(this);
        
        // 将命令块加入队列执行
        CommandQueueCenter.getInstance().enqueueBlock(aggregatorBlock);
    }
    
    /**
     * 更新电池电量数据（用于聚合命令块）
     * @param left 左耳电量字符串（百分比数字或"--"）
     * @param right 右耳电量字符串（百分比数字或"--"）
     * @param caseLevel 充电盒电量字符串（百分比数字或"--"）
     */
    public void updateBatteryLevels(String left, String right, String caseLevel) {
        this.leftBatteryLevel = left;
        this.rightBatteryLevel = right;
        this.caseBatteryLevel = caseLevel;
        YoBTSDKLog.d(TAG, "聚合更新电量数据: L=" + left + " R=" + right + " C=" + caseLevel);
    }
    
    /**
     * 更新充电盒充电状态（用于聚合命令块）
     * @param status 充电状态
     */
    public void updateCaseChargingStatus(int status) {
        this.caseChargingStatus = status;
        YoBTSDKLog.d(TAG, "聚合更新充电状态: " + (status == 1 ? "充电中" : "未充电"));
    }
    
    /**
     * 通知电池电量更新
     * @param code 结果状态码
     */
    public void notifyBatteryLevelsUpdated(int code) {
        // 如果有监听器，通知电池电量更新
        if (batteryLevelListener != null) {
            batteryLevelListener.onBatteryLevelsUpdated(
                    code, leftBatteryLevel, rightBatteryLevel, caseBatteryLevel);
        }
    }
    
    /**
     * 通知电池电量更新（成功状态）
     */
    private void notifyBatteryLevelsUpdated() {
        notifyBatteryLevelsUpdated(Command.ResultCode.SUCCESS);
    }
    
    /**
     * 通知充电盒充电状态更新
     * @param code 结果状态码
     */
    public void notifyCaseChargingStatusUpdated(int code) {
        // 如果有监听器，通知充电盒充电状态更新
        if (caseChargingStatusListener != null) {
            caseChargingStatusListener.onCaseChargingStatusUpdated(
                    code, isCaseCharging());
        }
    }
    
    /**
     * 通知充电盒充电状态更新（成功状态）
     */
    private void notifyCaseChargingStatusUpdated() {
        notifyCaseChargingStatusUpdated(Command.ResultCode.SUCCESS);
    }
    
    /**
     * 重置电量聚合操作标志
     * 由BatteryInfoAggregatorBlock在完成或失败时调用
     */
    public void resetBatteryAggregationFlag() {
        YoBTSDKLog.d(TAG, "重置电量聚合操作标志");
        isBatteryAggregationRunning = false;
    }
    
    /**
     * 通知耳机在充电盒内状态更新
     * @param code 结果状态码
     */
    private void notifyEarInCaseStatusUpdated(int code) {
        // 如果有监听器，通知耳机在充电盒内状态更新
        if (earInCaseStatusListener != null) {
            earInCaseStatusListener.onEarInCaseStatusUpdated(
                    code, earInCaseStatus, isLeftEarInCase(), isRightEarInCase());
        }
    }
    
    /**
     * 通知耳机在充电盒内状态更新（成功状态）
     */
    private void notifyEarInCaseStatusUpdated() {
        notifyEarInCaseStatusUpdated(Command.ResultCode.SUCCESS);
    }
    
    @Override
    protected String getManagerTag() {
        return TAG;
    }
    
    @Override
    protected void handleUnsolicitedResponse(String data) {
        if (!isMonitoring) {
            YoBTSDKLog.d(TAG, "监听器已关闭，忽略主动上报数据: " + data);
            return;
        }
        
        // 检查是否有命令正在执行，如果有则让命令优先处理数据
        if (CommandQueueCenter.getInstance().isExecuting()) {
            YoBTSDKLog.d(TAG, "有命令正在执行，暂不处理主动上报数据: " + data);
            return;
        }

        YoBTSDKLog.d(TAG, "处理电池相关的主动上报数据: " + data);
        if (data == null || data.length() < 6) {
            return;
        }
        String prefix = data.substring(0, 6);

        try {
            switch (prefix) {
                case "99EC80": // 电池电量
                    // 检查是否为单个电量上报: 99EC80 0002 ID LEVEL ...
                    if (data.length() >= 16 && data.startsWith("99EC800002")) {
                        String id = data.substring(10, 12);
                        int newLevel = Integer.parseInt(data.substring(12, 14), 16);
                        YoBTSDKLog.d(TAG, "解析到主动上报单个电量: ID=" + id + ", Level=" + newLevel);

                        // 验证电量范围并转换为字符串
                        String batteryValue = (newLevel >= 0 && newLevel <= 100) ? String.valueOf(newLevel) : "--";
                        if (batteryValue.equals("--")) {
                            YoBTSDKLog.w(TAG, "主动上报电量超出有效范围: " + newLevel + "，设置为--");
                        }

                        switch(id) {
                            case "01": this.leftBatteryLevel = batteryValue; break;
                            case "02": this.rightBatteryLevel = batteryValue; break;
                            case "03": this.caseBatteryLevel = batteryValue; break;
                            default:
                                YoBTSDKLog.w(TAG, "未知的 99EC80 单个电量上报ID: " + id);
                                return; // 未知ID，不通知
                        }
                        notifyBatteryLevelsUpdated();

                    }
                    // 检查是否为组合电量上报: 99EC80000301{L}02{R}03{C}...
                    else if (data.length() >= 24 && data.startsWith("99EC800003")) {
                        YoBTSDKLog.d(TAG, "解析到主动上报组合电量");
                        // Left
                        if ("01".equals(data.substring(12, 14))) {
                            int leftLevel = Integer.parseInt(data.substring(14, 16), 16);
                            this.leftBatteryLevel = (leftLevel >= 0 && leftLevel <= 100) ? String.valueOf(leftLevel) : "--";
                        }
                        // Right
                        if ("02".equals(data.substring(16, 18))) {
                            int rightLevel = Integer.parseInt(data.substring(18, 20), 16);
                            this.rightBatteryLevel = (rightLevel >= 0 && rightLevel <= 100) ? String.valueOf(rightLevel) : "--";
                        }
                        // Case
                        if ("03".equals(data.substring(20, 22))) {
                            int caseLevel = Integer.parseInt(data.substring(22, 24), 16);
                            this.caseBatteryLevel = (caseLevel >= 0 && caseLevel <= 100) ? String.valueOf(caseLevel) : "--";
                        }
                        notifyBatteryLevelsUpdated();
                    } else {
                        YoBTSDKLog.w(TAG, "未知的 99EC80 数据格式: " + data);
                    }
                    break;
                case "99EC95": // 充电盒充电状态
                    int status = CaseChargingStatusCommand.parseChargingStatus(data);
                    if (status != -1) {
                        YoBTSDKLog.d(TAG, "解析到主动上报充电状态: " + (status == 1 ? "充电中" : "未充电"));
                        this.caseChargingStatus = status;
                        notifyCaseChargingStatusUpdated();
                    }
                    break;
                case "99EC96": // 耳机在充电盒内状态
                    int inCaseStatus = EarInCaseStatusCommand.parseInCaseStatus(data);
                    if (inCaseStatus != -1) {
                        YoBTSDKLog.d(TAG, "解析到主动上报耳机在位状态: " + inCaseStatus);
                        this.earInCaseStatus = inCaseStatus;
                        notifyEarInCaseStatusUpdated();
                    }
                    break;
            }
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "解析电池主动上报数据失败: " + data, e);
        }
    }

    @Override
    protected boolean isDataRelevant(String data) {
        // 检查数据前缀是否与电池设置相关
        if (data != null && data.length() >= 6) {
            String prefix = data.substring(0, 6); // 取前6个字符 (99ECxx)
            
            // 电池电量 99EC80
            // 电池充电状态 99EC95
            // 耳机在充电盒内状态 99EC96
            return "99EC80".equals(prefix) || "99EC95".equals(prefix) || "99EC96".equals(prefix);
        }
        return false;
    }
    
    @Override
    protected void onCommandCompletedInternal(Command command, int code, Object yobackdata, String result) {
        if (code != Command.ResultCode.SUCCESS || yobackdata == null) {
            // 对于失败的或没有数据的回调，可以走旧的逻辑（如果需要处理失败通知）
            super.onCommandCompletedInternal(command, code, yobackdata, result);
            return;
        }

        boolean shouldNotifyBattery = false;
        boolean shouldNotifyCharging = false;
        boolean shouldNotifyInCase = false;
        
        try {
            if (command instanceof BatteryLeftCommand) {
                this.leftBatteryLevel = (String) yobackdata;
                shouldNotifyBattery = true;
            } else if (command instanceof BatteryRightCommand) {
                this.rightBatteryLevel = (String) yobackdata;
                shouldNotifyBattery = true;
            } else if (command instanceof BatteryCaseCommand) {
                this.caseBatteryLevel = (String) yobackdata;
                shouldNotifyBattery = true;
            } else if (command instanceof CaseChargingStatusCommand) {
                int value = (Integer) yobackdata;
                this.caseChargingStatus = value;
                shouldNotifyCharging = true;
            } else if (command instanceof EarInCaseStatusCommand) {
                int value = (Integer) yobackdata;
                this.earInCaseStatus = value;
                shouldNotifyInCase = true;
            }
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "处理命令结果数据失败", e);
        }

        // 根据命令类型触发相应的通知
        if (shouldNotifyBattery) {
            YoBTSDKLog.d(TAG, "电量更新: L=" + leftBatteryLevel + " R=" + rightBatteryLevel + " C=" + caseBatteryLevel);
            notifyBatteryLevelsUpdated();
        }
        if (shouldNotifyCharging) {
            YoBTSDKLog.d(TAG, "充电状态更新: " + isCaseCharging());
            notifyCaseChargingStatusUpdated();
        }
        if (shouldNotifyInCase) {
            YoBTSDKLog.d(TAG, "在仓状态更新: " + earInCaseStatus);
            notifyEarInCaseStatusUpdated();
        }
    }

    @Override
    protected void onCommandCompletedInternal(Command command, int code, String result) {
        // 此方法保留，用于处理没有yobackdata的旧命令或失败情况
        // 但在新架构下，成功回调都应该走带yobackdata的版本
        YoBTSDKLog.d(TAG, "旧版 onCommandCompletedInternal 被调用，命令: " + command.getClass().getSimpleName() + " code: " + code);
    }
    
    /**
     * 检查批量电量请求是否完成
     */
    private boolean isBatchComplete() {
        // 这里简化处理，实际上需要根据批量请求的逻辑判断
        return true;
    }
} 