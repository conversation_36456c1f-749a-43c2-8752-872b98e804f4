# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# distributionUrl=https\://services.gradle.org/distributions/gradle-7.5-bin.zip
zipStoreBase=GRADLE_USER_HOME
zipStorePath=wrapper/dists

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8 --add-opens java.base/java.io=ALL-UNNAMED

# When configured, <PERSON>rad<PERSON> will run in a background process so that
# subsequent builds are faster.
org.gradle.daemon=true

# Force Gradle to use Java 17 to resolve build version conflicts
# NOTE: This path may need to be adjusted to your local Java 17 installation
org.gradle.java.home=C:/Program Files/Java/jdk-11

# 指定JDK路径
# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. For more details, visit
# https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects
# org.gradle.parallel=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true
# 允许使用Java 8编译高版本SDK
# android.suppressUnsupportedCompileSdk=33,35
# 忽略源/目标版本警告
android.javaCompile.suppressSourceTargetDeprecationWarning=true