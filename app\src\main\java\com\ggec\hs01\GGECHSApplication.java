package com.ggec.hs01;

import android.app.Application;
import android.util.Log;

import com.ggec.bleservice.YoBLEApi;
import com.ggec.bleservice.YoCommandApi;
import com.ggec.sppservice.YoSPPApi;
import com.ggec.yobtsdkserver.YoBTInit;
import com.ggec.yotasdk.YOTAApi;

import java.io.File;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description:     
 * 应用程序类
 * 用于集中管理所有模块的初始化逻辑
 */
public class GGECHSApplication extends Application {
    private static final String TAG = "GGECHSApplication";
    
    // 功能模块启用状态控制
    private boolean mFileLogEnabled = false;  // 是否启用文件日志
    private boolean mBluetoothEnabled = true; // 是否启用蓝牙功能
    private boolean mOtaEnabled = true;       // 是否启用OTA功能
    
    // SDK初始化器
    private YoBTInit mBtInit;
    private YOTAApi mOtaApi;
    private YoSPPApi mSppApi;
    private YoSPPApi.ClassicBTDisconnectedListener mClassicBTDisconnectedListener;
    private YoSPPApi.ClassicBTConnectedListener mClassicBTConnectedListener;
    
    // 固件版本信息存储
    private String mFirmwareVersion = "";
    
    @Override
    public void onCreate() {
        super.onCreate();
        
        // 按照依赖关系顺序初始化各模块
        if (mBluetoothEnabled) {
            initBtSdk();
        }
        
        if (mOtaEnabled) {
            initOtaService();
        }
        
        // 初始化SPP服务
        initSppService();
        
        // 创建应用私有目录
        createPrivateDirectory();
        
        Log.i(TAG, "应用初始化完成");
    }
    
    /**
     * 创建应用私有目录
     * 确保/files目录存在
     */
    private void createPrivateDirectory() {
        File filesDir = getExternalFilesDir(null);
        if (filesDir == null) {
            Log.e(TAG, "无法获取外部存储的私有目录。");
            return;
        }

        if (!filesDir.exists()) {
            Log.d(TAG, "私有目录不存在，正在创建: " + filesDir.getAbsolutePath());
            if (filesDir.mkdirs()) {
                Log.i(TAG, "私有目录创建成功。");
            } else {
                Log.e(TAG, "私有目录创建失败。");
            }
        } else {
            Log.i(TAG, "私有目录已存在: " + filesDir.getAbsolutePath());
        }
    }
    
    /**
     * 初始化日志系统
     */
    private void initLogger() {
        // 设置文件日志状态
        if (mBtInit != null && mBtInit.isInitialized()) {
            YoBLEApi.getInstance().setFileLogEnabled(mFileLogEnabled);
            Log.i(TAG, "日志系统初始化完成，文件日志状态：" + (mFileLogEnabled ? "已启用" : "已禁用"));
        } else {
            Log.e(TAG, "无法设置文件日志，因为YoBTInit尚未初始化。");
        }
    }
    
    /**
     * 初始化蓝牙服务
     */
    private void initBtSdk() {
        Log.i(TAG, "初始化蓝牙SDK");
        
        // 使用YoBTInit来初始化SDK
        mBtInit = YoBTInit.getInstance();
        
        // 初始化SDK
        mBtInit.init(this);
        
        // 初始化日志系统
        initLogger();
        
        Log.i(TAG, "蓝牙SDK初始化完成");
    }
    
    /**
     * 初始化SPP服务
     */
    private void initSppService() {
        Log.i(TAG, "初始化SPP服务");
        
        // 获取YoSPPApi实例
        mSppApi = YoSPPApi.getInstance(getApplicationContext());

        // --- 注册断开连接监听器 ---
        mClassicBTDisconnectedListener = new YoSPPApi.ClassicBTDisconnectedListener() {
            @Override
            public void headsetIsDisconnected(String macAddress) {
                Log.w(TAG, "检测到经典蓝牙设备断开，MAC地址: " + macAddress);
                // 在这里可以处理设备断开后的逻辑
            }
        };
        mSppApi.addClassicBTDisconnectedListener(mClassicBTDisconnectedListener);

        // --- 注册连接监听器 ---
        mClassicBTConnectedListener = new YoSPPApi.ClassicBTConnectedListener() {
            @Override
            public void headsetIsConnected(String macAddress) {
                Log.i(TAG, "检测到经典蓝牙设备连接，MAC地址: " + macAddress);
                // 在这里可以处理设备连接后的逻辑
            }
        };
        mSppApi.addClassicBTConnectedListener(mClassicBTConnectedListener);

        Log.i(TAG, "SPP服务初始化完成，并已注册连接/断开状态监听器。");
    }
    
    /**
     * 释放蓝牙服务资源
     */
    private void releaseBtSdk() {
        Log.i(TAG, "释放蓝牙SDK资源");
        
        // 使用YoBTInit统一释放资源
        if (mBtInit != null && mBtInit.isInitialized()) {
            mBtInit.release();
            mBtInit = null;
        }
    }
    
    /**
     * 释放SPP服务资源
     */
    private void releaseSppService() {
        Log.i(TAG, "释放SPP服务资源");
        
        if (mSppApi != null) {
            // 移除断开监听器
            if (mClassicBTDisconnectedListener != null) {
                mSppApi.removeClassicBTDisconnectedListener(mClassicBTDisconnectedListener);
                mClassicBTDisconnectedListener = null;
            }

            // 移除连接监听器
            if (mClassicBTConnectedListener != null) {
                mSppApi.removeClassicBTConnectedListener(mClassicBTConnectedListener);
                mClassicBTConnectedListener = null;
            }

            mSppApi.release();
            mSppApi = null;
        }
    }
    
    /**
     * 初始化OTA服务
     */
    private void initOtaService() {
        Log.i(TAG, "初始化OTA服务");
        
        // 使用YOTAApi接口获取实例（单例模式）
        mOtaApi = YOTAApi.getInstance(getApplicationContext());
        
        Log.i(TAG, "OTA服务初始化完成");
    }
    
    /**
     * 设置是否启用OTA功能
     * @param enabled 是否启用
     */
    public void setOtaEnabled(boolean enabled) {
        this.mOtaEnabled = enabled;
        
        // 如果禁用OTA功能，则释放资源
        if (!enabled) {
            mOtaApi = null;
        } 
        // 如果启用OTA功能且尚未初始化，则初始化
        else if (enabled && mOtaApi == null) {
            initOtaService();
        }
    }
    
    /**
     * 获取YOTAApi实例
     * 使用单例模式确保全局只有一个实例
     * 
     * @return YOTAApi实例
     */
    public YOTAApi getOtaApi() {
        try {
            // 检查OTA功能是否启用
            if (!mOtaEnabled) {
                Log.w(TAG, "OTA功能已禁用，返回null");
                return null;
            }
            
            // 如果实例为空，尝试初始化
            if (mOtaApi == null) {
                synchronized (this) {
                    if (mOtaApi == null) {
                        Log.i(TAG, "OTA API实例为空，正在重新初始化");
                        initOtaService();
                    }
                }
            }
            
            // 双重检查：如果初始化后仍为空，进行重试
            if (mOtaApi == null) {
                Log.w(TAG, "首次初始化失败，尝试重试");
                try {
                    Thread.sleep(100); // 短暂延迟后重试
                    initOtaService();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    Log.e(TAG, "重试初始化被中断: " + e.getMessage());
                }
            }
            
            // 最终检查：如果仍为空，记录详细日志
            if (mOtaApi == null) {
                Log.e(TAG, "OTA API初始化失败，所有重试均无效。请检查系统状态。");
                // 返回一个安全的代理对象，防止空指针异常
                return createSafeOtaApiProxy();
            }
            
            return mOtaApi;
            
        } catch (Exception e) {
            Log.e(TAG, "获取OTA API时发生异常: " + e.getMessage(), e);
            // 发生异常时也返回安全代理
            return createSafeOtaApiProxy();
        }
    }
    
    /**
     * 获取YoSPPApi实例
     * 使用单例模式确保全局只有一个实例
     * 
     * @return YoSPPApi实例
     */
    public YoSPPApi getSppApi() {
        if (mSppApi == null) {
            initSppService();
        }
        return mSppApi;
    }
    
    /**
     * 设置是否启用文件日志
     * @param enabled 是否启用
     */
    public void setFileLogEnabled(boolean enabled) {
        this.mFileLogEnabled = enabled;
        // 通过YoBLEApi设置文件日志状态
        YoBLEApi.getInstance().setFileLogEnabled(enabled);
    }
    
    /**
     * 设置是否启用蓝牙功能
     * @param enabled 是否启用
     */
    public void setBluetoothEnabled(boolean enabled) {
        this.mBluetoothEnabled = enabled;
        
        // 如果禁用蓝牙功能，则释放资源
        if (!enabled && mBtInit != null && mBtInit.isInitialized()) {
            releaseBtSdk();
        } 
        // 如果启用蓝牙功能且尚未初始化，则初始化
        else if (enabled && (mBtInit == null || !mBtInit.isInitialized())) {
            initBtSdk();
        }
    }
    
    /**
     * 获取YoBLEApi实例
     * 通过YoBLEInitializer获取，确保正确初始化
     */
    public YoBLEApi getBleApi() {
        if (mBtInit == null || !mBtInit.isInitialized()) {
            initBtSdk();
        }
        return mBtInit.getBleApi();
    }
    
    /**
     * 获取YoCommandApi实例
     * 通过YoBLEInitializer获取，确保正确初始化
     */
    public YoCommandApi getCommandApi() {
        if (mBtInit == null || !mBtInit.isInitialized()) {
            initBtSdk();
        }
        return mBtInit.getCommandApi();
    }
    
    /**
     * 获取固件版本信息
     * @return 固件版本信息
     */
    public String getFirmwareVersion() {
        return mFirmwareVersion;
    }

    /**
     * 设置固件版本信息
     * @param version 固件版本信息
     */
    public void setFirmwareVersion(String version) {
        this.mFirmwareVersion = version;
    }
    
    @Override
    public void onTerminate() {
        super.onTerminate();
        Log.i(TAG, "应用终止，开始释放资源");

        // 释放蓝牙SDK资源
        if (mBluetoothEnabled) {
            releaseBtSdk();
        }
        
        // 释放SPP服务资源
        releaseSppService();
        
        // 释放其他可能需要清理的资源
        mOtaApi = null;
        
        Log.i(TAG, "所有资源已释放");
    }

    /**
     * 创建安全的OTA API代理对象
     * 当真实的OTA API无法初始化时，返回这个代理对象防止空指针异常
     */
    private YOTAApi createSafeOtaApiProxy() {
        Log.w(TAG, "创建安全的OTA API代理对象");
        return new SafeOtaApiProxy();
    }
    
    /**
     * 安全的OTA API代理实现
     * 所有方法都返回安全的默认值，防止应用崩溃
     */
    private static class SafeOtaApiProxy implements YOTAApi {
        private static final String TAG = "SafeOtaApiProxy";
        
        @Override
        public boolean startUpgrade() {
            Log.w(TAG, "OTA API未初始化，无法开始升级");
            return false;
        }
        
        @Override
        public boolean startUpgrade(com.bes.sdk.device.HmDevice device) {
            Log.w(TAG, "OTA API未初始化，无法开始升级");
            return false;
        }
        
        @Override
        public boolean startUpgrade(String macAddress) {
            Log.w(TAG, "OTA API未初始化，无法开始升级");
            return false;
        }
        
        @Override
        public void setOtaFilePath(String filePath) {
            Log.w(TAG, "OTA API未初始化，无法设置文件路径");
        }
        
        @Override
        public java.util.ArrayList<com.bes.sdk.device.HmDevice> getConnectedSppDevices() {
            Log.w(TAG, "OTA API未初始化，返回空设备列表");
            return new java.util.ArrayList<>();
        }
        
        @Override
        public void setProgressListener(ProgressListener listener) {
            Log.w(TAG, "OTA API未初始化，无法设置进度监听器");
        }
        
        @Override
        public void setStatusListener(StatusListener listener) {
            Log.w(TAG, "OTA API未初始化，无法设置状态监听器");
        }
        
        @Override
        public boolean cancelUpgrade() {
            Log.w(TAG, "OTA API未初始化，无法取消升级");
            return false;
        }
        
        @Override
        public boolean isInitialized() {
            return false;
        }
        
        @Override
        public boolean isUpgrading() {
            return false;
        }
    }
}
