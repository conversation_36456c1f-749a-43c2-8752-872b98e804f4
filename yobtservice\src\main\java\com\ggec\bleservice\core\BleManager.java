package com.ggec.bleservice.core;

import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGattService;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import com.ggec.bleservice.gatt.GattConnectionCallback;
import com.ggec.bleservice.gatt.GattConnectionManager;
import com.ggec.yobtsdkserver.sdkmanager.BTPermissionManager;
import com.ggec.bleservice.scanner.ScanConfig;
import com.ggec.bleservice.scanner.ScanManager;
import com.ggec.yobtsdkserver.utils.HexUtil;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 蓝牙管理器单例类
 * 全局统一管理蓝牙功能，包括初始化、扫描、连接、状态维护等
 * 对上层提供简洁的API，封装底层实现细节
 */
public class BleManager {
    private static final String TAG = "BleManager";

    private final Map<BluetoothDevice, GattConnectionManager> gattManagerMap = new HashMap<>();

    private GattConnectionManager connectedGattManager;

    private static BleManager instance;
    private BluetoothDevice connectedDevice;
    private BleEventDispatcher eventDispatcher;
    private Context context;

    // 用于记录曾经成功连接过的设备地址
    private final Set<String> everConnectedDevices = new HashSet<>();
    
    /**
     * 获取BleManager单例
     */
    public static BleManager getInstance() {
        if (instance == null) {
            synchronized (BleManager.class) {
                if (instance == null) {
                    instance = new BleManager();
                }
            }
        }
        return instance;
    }
    
    private BleManager() {
        // 私有构造函数
    }
    
    /**
     * 初始化蓝牙服务
     * @param context 上下文
     */
    public void init(Context context) {
        this.context = context.getApplicationContext();
        
        // 初始化事件分发器
        eventDispatcher = BleEventDispatcher.getInstance();
        eventDispatcher.init();
        
        // 初始化扫描管理器
        ScanManager.getInstance().init(this.context);

        // 注册蓝牙状态广播接收器
        registerBluetoothStateReceiver();
        
        YoBTSDKLog.i(TAG, "蓝牙服务初始化完成");
    }
    
    /**
     * 检查蓝牙功能是否已初始化
     */
    private boolean checkInitialized() {
        if (context == null) {
            YoBTSDKLog.e(TAG, "蓝牙服务尚未初始化，请先调用init()方法");
            return false;
        }
        return true;
    }
    
    /**
     * 检查蓝牙功能是否已初始化
     * @return 是否已初始化
     */
    public boolean isInitialized() {
        return context != null;
    }
    
    /**
     * 释放资源
     * 仅在应用退出时调用
     */
    public void release() {
        disconnect();

        for (GattConnectionManager gattManager : gattManagerMap.values()) {
            gattManager.close();
        }
        gattManagerMap.clear();
        connectedGattManager = null;

        // 释放事件分发器资源
        if (eventDispatcher != null) {
            eventDispatcher.release();
            eventDispatcher = null;
        }

        // 注销蓝牙状态广播接收器
        unregisterBluetoothStateReceiver();
        
        // 释放扫描管理器资源
        ScanManager.getInstance().release();
        
        connectedDevice = null;
        context = null;
        everConnectedDevices.clear();
        instance = null;
        
        YoBTSDKLog.i(TAG, "蓝牙服务资源已释放");
    }
    
    /**
     * 检查蓝牙权限
     * @param activity Activity实例
     * @return 如果权限已全部授予返回true
     */
    public boolean checkBluetoothPermissions(Activity activity) {
        return BTPermissionManager.checkAndRequestPermissions(activity);
    }
    
    //------------------ 连接相关 ------------------//
    
    /**
     * 获取已连接的设备
     */
    public BluetoothDevice getConnectedDevice() {
        return connectedDevice;
    }
    
    /**
     * 获取已连接设备的名称
     * @return 设备名称，未连接时返回null
     */
    public String getConnectedDeviceName() {
        if (connectedDevice == null) {
            return null;
        }
        
        try {
            return connectedDevice.getName();
        } catch (SecurityException e) {
            YoBTSDKLog.e(TAG, "获取设备名称时出现权限错误: " + e.getMessage());
            return "未知设备";
        }
    }
    
    /**
     * 是否已连接设备
     * @return 是否已连接
     */
    public boolean isDeviceConnected() {
        return connectedDevice != null;
    }
    
    /**
     * 检查设备是否曾经成功连接过
     * @param device 要检查的蓝牙设备
     * @return 如果设备曾经成功连接过返回true
     */
    public boolean wasEverConnected(BluetoothDevice device) {
        if (device == null) return false;
        return everConnectedDevices.contains(device.getAddress());
    }
    
    /**
     * 连接设备
     * @param device 蓝牙设备
     * @return 是否成功发起连接
     */
    public boolean connect(BluetoothDevice device) {
        if (!checkInitialized() || device == null || eventDispatcher == null) return false;
        
        GattConnectionManager gattManager = gattManagerMap.get(device);
        if (gattManager == null) {
            gattManager = new GattConnectionManager(context);
            gattManagerMap.put(device, gattManager);
        } else {
            if(gattManager.isConnected()){
                connectedGattManager = gattManager;
                return true;
            } else {
                gattManager.close();
                gattManagerMap.remove(device);
                gattManager = new GattConnectionManager(context);
                gattManagerMap.put(device, gattManager);
            }
        }
        connectedGattManager = gattManager;
        gattManager.setConnectionCallback(new GattConnectionCallback() {
            @Override
            public void onConnectionStateChange(BluetoothDevice device, int status, int newState) {
                // 更新连接状态
                if (status == 0 && newState == BleConstants.STATE_CONNECTED) {
                    connectedDevice = device;
                    // 记录成功连接的设备
                    if (device != null) {
                        everConnectedDevices.add(device.getAddress());
                    }
                    // 自动发现服务
                    discoverServices();
                } else if (newState == BleConstants.STATE_DISCONNECTED) {
                    connectedDevice = null;
                }
                
                // 分发事件
                eventDispatcher.dispatchConnectionStateChanged(device, status, newState);
            }
            
            @Override
            public void onServicesDiscovered(BluetoothDevice device, List<BluetoothGattService> services, int status) {
                eventDispatcher.dispatchServicesDiscovered(device, services, status);
            }
            
            @Override
            public void onCharacteristicRead(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, byte[] value, int status) {
                eventDispatcher.dispatchCharacteristicRead(device, serviceUUID, characteristicUUID, value, status);
            }
            
            @Override
            public void onCharacteristicWrite(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, int status) {
                eventDispatcher.dispatchCharacteristicWrite(device, serviceUUID, characteristicUUID, status);
            }
            
            @Override
            public void onCharacteristicChanged(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, byte[] value) {
                eventDispatcher.dispatchCharacteristicChanged(device, serviceUUID, characteristicUUID, value);
            }
            
            @Override
            public void onMtuChanged(BluetoothDevice device, int mtu, int status) {
                //YoBTSDKLog.i(TAG, "MTU变更: " + mtu + ", 状态: " + status);
                eventDispatcher.dispatchMtuChanged(device, mtu, status);
            }

            @Override
            public void onNotificationEnabled(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, int status) {
                eventDispatcher.dispatchNotificationEnabled(device, serviceUUID, characteristicUUID, status);
            }
        });
        YoBTSDKLog.i(TAG, "正在连接设备: " + device.getAddress());
        return gattManager.connect(device);
    }
    
    /**
     * 断开当前连接
     */
    public void disconnect() {
        if (checkInitialized() && connectedDevice != null) {
            YoBTSDKLog.i(TAG, "断开设备连接: " + connectedDevice.getAddress());
            gattManagerMap.remove(connectedDevice);
            connectedGattManager.disconnect();
        }
    }
    
    /**
     * 获取服务列表
     * @return 服务列表
     */
    public List<BluetoothGattService> getServices() {
        if (checkInitialized()) {
            return connectedGattManager.getServices();
        }
        return null;
    }
    
    /**
     * 发现服务
     * @return 是否成功发起发现
     */
    public boolean discoverServices() {
        if (checkInitialized()) {
            return connectedGattManager.discoverServices();
        }
        return false;
    }
    
    //------------------ 扫描相关 ------------------//
    
    /**
     * 开始扫描
     * @return 是否成功开始扫描
     */
    public boolean startScan() {
        if (!checkInitialized()) return false;
        return ScanManager.getInstance().startScan();
    }
    
    /**
     * 开始扫描（带超时）
     * @param timeoutMs 超时时间（毫秒）
     * @return 是否成功开始扫描
     */
    public boolean startScan(int timeoutMs) {
        if (!checkInitialized()) return false;
        return ScanManager.getInstance().startScan(timeoutMs);
    }
    
    /**
     * 开始扫描（带配置）
     * @param config 扫描配置
     * @return 是否成功开始扫描
     */
    public boolean startScan(ScanConfig config) {
        if (!checkInitialized()) return false;
        ScanManager.getInstance().setScanConfig(config);
        return ScanManager.getInstance().startScan();
    }
    
    /**
     * 停止扫描
     */
    public void stopScan() {
        ScanManager.getInstance().stopScan();
    }
    
    /**
     * 是否正在扫描
     */
    public boolean isScanning() {
        return ScanManager.getInstance().isScanning();
    }
    
    //------------------ 特征值操作相关 ------------------//
    
    /**
     * 读取特征值
     * @param serviceUUID 服务UUID
     * @param characteristicUUID 特征值UUID
     * @return 是否成功发起读取
     */
    public boolean readCharacteristic(UUID serviceUUID, UUID characteristicUUID) {
        if (checkInitialized()) {
            return connectedGattManager.readCharacteristic(serviceUUID, characteristicUUID);
        }
        return false;
    }
    
    /**
     * 写入特征值
     * @param serviceUUID 服务UUID
     * @param characteristicUUID 特征值UUID
     * @param value 字节数据
     * @return 是否成功发起写入
     */
    public boolean writeCharacteristic(UUID serviceUUID, UUID characteristicUUID, byte[] value) {
        if (checkInitialized()) {
            return connectedGattManager.writeCharacteristic(serviceUUID, characteristicUUID, value);
        }
        return false;
    }
    
    /**
     * 写入特征值（十六进制字符串）
     * @param serviceUUID 服务UUID
     * @param characteristicUUID 特征值UUID
     * @param hexString 十六进制字符串
     * @return 是否成功发起写入
     */
    public boolean writeCharacteristic(UUID serviceUUID, UUID characteristicUUID, String hexString) {
        if (checkInitialized()) {
            try {
                byte[] data = HexUtil.hexStringToBytes(hexString);
                return writeCharacteristic(serviceUUID, characteristicUUID, data);
            } catch (Exception e) {
                YoBTSDKLog.e(TAG, "写入特征值失败，无效的十六进制字符串: " + hexString, e);
                return false;
            }
        }
        return false;
    }
    
    /**
     * 设置特征值通知
     * @param serviceUUID 服务UUID
     * @param characteristicUUID 特征值UUID
     * @param enable 是否启用通知
     * @return 是否成功设置
     */
    public boolean setCharacteristicNotification(UUID serviceUUID, UUID characteristicUUID, boolean enable) {
        if (checkInitialized()) {
            return connectedGattManager.setCharacteristicNotification(serviceUUID, characteristicUUID, enable);
        }
        return false;
    }
    
    /**
     * 获取当前是否已连接到设备
     * @return 是否已连接
     */
    public boolean isConnected() {
        return connectedDevice != null && connectedGattManager != null && connectedGattManager.isConnected();
    }

    /**
     * 获取当前MTU大小
     * @return 当前MTU大小
     */
    public int getCurrentMtu() {
        if (checkInitialized()) {
            return connectedGattManager.getCurrentMtu();
        }
        return 23; // 返回默认MTU大小
    }
    
    /**
     * 请求更新MTU大小
     * @param mtu 请求的MTU大小
     * @return 是否成功发起请求
     */
    public boolean requestMtu(int mtu) {
        if (checkInitialized()) {
            return connectedGattManager.requestMtu(mtu);
        }
        return false;
    }

    //------------------ 蓝牙状态广播接收器 ------------------//

    private final BroadcastReceiver bluetoothStateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            final String action = intent.getAction();
            if (BluetoothAdapter.ACTION_STATE_CHANGED.equals(action)) {
                final int state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR);
                if (state == BluetoothAdapter.STATE_OFF) {
                    YoBTSDKLog.d(TAG, "检测到系统蓝牙已关闭");
                    if (eventDispatcher != null) {
                        eventDispatcher.dispatchSystemBluetoothClose();
                    }
                } else if (state == BluetoothAdapter.STATE_ON) {
                    YoBTSDKLog.d(TAG, "检测到系统蓝牙已开启");
                    if (eventDispatcher != null) {
                        eventDispatcher.dispatchSystemBluetoothOpen();
                    }
                }
            }
        }
    };

    private void registerBluetoothStateReceiver() {
        if (context != null) {
            IntentFilter filter = new IntentFilter(BluetoothAdapter.ACTION_STATE_CHANGED);
            context.registerReceiver(bluetoothStateReceiver, filter);
        }
    }

    private void unregisterBluetoothStateReceiver() {
        if (context != null) {
            try {
                context.unregisterReceiver(bluetoothStateReceiver);
            } catch (IllegalArgumentException e) {
                //YoBTSDKLog.e(TAG, "注销蓝牙状态接收器失败: " + e.getMessage());
            }
        }
    }
} 