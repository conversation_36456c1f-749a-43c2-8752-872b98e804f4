package com.ggec.yotasdk;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothManager;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.util.Log;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.BesOtaService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.ota.OTADfuInfo;
import com.bes.sdk.ota.OTATask;
import com.bes.sdk.ota.RemoteOTAConfig;
import com.bes.sdk.utils.DeviceProtocol;
import com.bes.sdk.utils.OTAStatus;
import com.bes.bessdk.utils.FileUtils;

import static com.bes.bessdk.service.BesOTAConstants.OTA_CMD_GET_HW_INFO;

import java.io.File;
import java.util.ArrayList;
import java.util.Set;

/**
 * YOTA管理器
 * 实现OTA升级功能的核心类
 */
class YOTAManager implements YOTAApi, BesServiceListener, OTATask.StatusListener {

    private static final String TAG = "YOTAManager";
    private static volatile YOTAManager instance;
    
    private Context context;
    private HmDevice currentDevice;
    private String upgradeFilePath;
    private OTATask otaTask;
    private BesServiceConfig serviceConfig;
    private volatile boolean isUpgrading = false;
    private boolean isInitialized = false;
    
    // 默认OTA升级文件路径
    private static final String DEFAULT_OTA_FILE_NAME = "test.bin";
    private String defaultOtaFilePath;
    
    // 监听器
    private YOTAApi.ProgressListener progressListener;
    private YOTAApi.StatusListener statusListener;
    
    /**
     * 私有构造函数，防止外部创建实例
     */
    private YOTAManager() {
        // Private constructor for singleton pattern
    }
    
    /**
     * 获取YOTAManager单例，并自动初始化
     * 
     * @param context 应用上下文
     * @return YOTAManager单例
     */
    public static YOTAManager getInstance(Context context) {
        if (instance == null) {
            synchronized (YOTAManager.class) {
                if (instance == null) {
                    try {
                        instance = new YOTAManager();
                        instance.initialize(context);
                        Log.i(TAG, "YOTAManager实例创建和初始化成功");
                    } catch (Exception e) {
                        Log.e(TAG, "YOTAManager初始化异常: " + e.getMessage(), e);
                        // 即使初始化失败，也要确保返回实例，避免空指针
                        if (instance == null) {
                            instance = new YOTAManager();
                            // 设置一个基本的上下文，避免后续空指针
                            if (context != null) {
                                instance.context = context.getApplicationContext();
                            }
                        }
                    }
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化默认OTA升级文件路径
     */
    private void initDefaultOtaFilePath() {
        File filesDir = context.getExternalFilesDir(null);
        if (filesDir != null) {
            defaultOtaFilePath = new File(filesDir, DEFAULT_OTA_FILE_NAME).getAbsolutePath();
        } else {
            Log.e(TAG, "外部文件目录不可用，回退到旧版路径构建方法。");
            String packageName = context.getPackageName();
            defaultOtaFilePath = "/storage/emulated/0/Android/data/" + packageName + "/files/" + DEFAULT_OTA_FILE_NAME;
        }
        Log.i(TAG, "默认OTA文件路径: " + defaultOtaFilePath);
    }
    
    /**
     * 内部初始化方法
     * @param context 应用上下文
     */
    private void initialize(Context context) {
        if (isInitialized) {
            Log.w(TAG, "SDK has already been initialized.");
            return;
        }
        
        try {
            // 基本上下文设置 - 这是最重要的，必须成功
            if (context != null) {
                this.context = context.getApplicationContext();
                Log.i(TAG, "上下文设置成功");
            } else {
                Log.e(TAG, "传入的上下文为空，初始化可能不完整");
            }
            
            // 初始化FileUtils（SDK内部依赖）- 即使失败也继续
            try {
                if (this.context != null) {
                    FileUtils.init(this.context);
                    Log.i(TAG, "FileUtils initialized successfully.");
                } else {
                    Log.w(TAG, "上下文为空，跳过FileUtils初始化");
                }
            } catch (Exception e) {
                Log.e(TAG, "FileUtils initialization failed: " + e.getMessage(), e);
                // 继续执行，不抛出异常
            }
            
            // 初始化默认OTA文件路径 - 即使失败也继续
            try {
                initDefaultOtaFilePath();
                Log.i(TAG, "默认OTA文件路径初始化成功");
            } catch (Exception e) {
                Log.e(TAG, "默认OTA文件路径初始化失败: " + e.getMessage(), e);
            }
            
            // 标记为已初始化
            isInitialized = true;
            Log.i(TAG, "YOTA SDK initialized successfully.");
            
        } catch (Exception e) {
            Log.e(TAG, "YOTA SDK初始化过程中发生异常: " + e.getMessage(), e);
            // 即使发生异常，也要设置基本状态，避免后续调用出现问题
            isInitialized = true; // 标记为已初始化，避免重复初始化
            if (this.context == null && context != null) {
                this.context = context.getApplicationContext();
            }
        }
    }
    
    @Override
    public void setOtaFilePath(String filePath) {
        if (filePath != null && !filePath.isEmpty()) {
            this.upgradeFilePath = filePath;
        }
    }
    
    @Override
    public ArrayList<HmDevice> getConnectedSppDevices() {
        ArrayList<HmDevice> resultDevices = new ArrayList<>();
        
        try {
            Log.i(TAG, "获取已连接的SPP设备");
            BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
            if (bluetoothAdapter == null) {
                Log.e(TAG, "蓝牙适配器未启用");
                return resultDevices;
            }
            
            // 获取BluetoothManager
            BluetoothManager bluetoothManager = (BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE);
            if (bluetoothManager == null) {
                Log.e(TAG, "无法获取BluetoothManager");
                return resultDevices;
            }
            
            // 获取已配对设备
            Set<BluetoothDevice> pairedDevices = bluetoothAdapter.getBondedDevices();
            if (pairedDevices.isEmpty()) {
                Log.i(TAG, "没有配对设备");
                return resultDevices;
            }
            
            Log.i(TAG, "找到" + pairedDevices.size() + "个配对设备，检查连接状态");
            
            // 遍历配对设备，检查是否真正连接
            for (BluetoothDevice device : pairedDevices) {
                try {
                    if (device.getType() == BluetoothDevice.DEVICE_TYPE_CLASSIC || 
                        device.getType() == BluetoothDevice.DEVICE_TYPE_DUAL) {
                        
                        // 检查设备连接状态
                        int connectionState = bluetoothManager.getConnectionState(device, BluetoothProfile.GATT);
                        boolean isConnected = (connectionState == BluetoothProfile.STATE_CONNECTED);
                        
                        // 额外检查SPP连接状态
                        boolean isSppConnected = checkSppConnectionState(device);
                        
                        if (isConnected || isSppConnected) {
                            // 只添加已连接的设备
                            HmDevice hmDevice = new HmDevice();
                            hmDevice.setDeviceMAC(device.getAddress());
                            hmDevice.setDeviceName(device.getName() != null ? device.getName() : "未知设备");
                            hmDevice.setPreferredProtocol(DeviceProtocol.PROTOCOL_SPP);
                            hmDevice.setRssi(100); // 默认信号强度
                            
                            Log.i(TAG, "添加已连接设备 " + hmDevice.getDeviceName() + "(" + hmDevice.getDeviceMAC() + ")");
                            resultDevices.add(hmDevice);
                        } else {
                            Log.i(TAG, "跳过未连接设备 " + device.getName() + "(" + device.getAddress() + ")");
                        }
                    }
                } catch (Exception e) {
                    Log.e(TAG, "检查设备连接状态异常: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "获取已连接设备异常: " + e.getMessage());
        }
        
        // 输出最终结果
        Log.i(TAG, "已找到的连接中SPP设备数量: " + resultDevices.size());
        logConnectedDevices(resultDevices);
        
        return resultDevices;
    }
    
    /**
     * 检查SPP连接状态
     * 因为SPP是经典蓝牙协议，需要特殊处理
     * 
     * @param device 蓝牙设备
     * @return 是否连接
     */
    private boolean checkSppConnectionState(BluetoothDevice device) {
        try {
            // 使用反射获取连接状态
            // 注意：这是一个实现细节，可能在不同Android版本中有所不同
            Class<?> bluetoothDeviceClass = BluetoothDevice.class;
            java.lang.reflect.Method isConnectedMethod = bluetoothDeviceClass.getDeclaredMethod("isConnected");
            isConnectedMethod.setAccessible(true);
            boolean isConnected = (boolean) isConnectedMethod.invoke(device);
            
            Log.i(TAG, "SPP设备 " + device.getName() + " 连接状态: " + isConnected);
            return isConnected;
        } catch (Exception e) {
            Log.e(TAG, "检查SPP连接状态失败: " + e.getMessage());
            // 反射失败时，尝试其他方法确认连接
            return false;
        }
    }
    
    /**
     * 记录已连接设备的信息
     */
    private void logConnectedDevices(ArrayList<HmDevice> devices) {
        if (devices.isEmpty()) {
            Log.i(TAG, "没有已连接的设备");
            return;
        }
        
        for (int i = 0; i < devices.size(); i++) {
            HmDevice device = devices.get(i);
            Log.i(TAG, "设备" + (i+1) + ": " + device.getDeviceName() + " (" + device.getDeviceMAC() + ")");
        }
    }
    
    @Override
    public boolean startUpgrade() {
        if (!isInitialized) {
            Log.e(TAG, "SDK is not initialized. Please call init() first.");
            return false;
        }
        Log.d(TAG, "尝试通过无参数接口启动OTA升级");
        // 直接调用现有逻辑，传入null，使其自动查找设备和使用默认文件路径
        return startUpgrade((HmDevice) null);
    }
    
    @Override
    public boolean startUpgrade(String macAddress) {
        if (!isInitialized) {
            Log.e(TAG, "SDK is not initialized. Please call init() first.");
            return false;
        }

        if (macAddress == null || macAddress.isEmpty()) {
            Log.i(TAG, "MAC地址为空，将自动选择设备进行升级");
            return startUpgrade((HmDevice) null);
        }

        Log.i(TAG, "尝试根据MAC地址查找设备: " + macAddress);
        ArrayList<HmDevice> connectedDevices = getConnectedSppDevices();
        HmDevice targetDevice = null;
        for (HmDevice device : connectedDevices) {
            if (macAddress.equalsIgnoreCase(device.getDeviceMAC())) {
                targetDevice = device;
                Log.i(TAG, "找到匹配的设备: " + device.getDeviceName() + " (" + device.getDeviceMAC() + ")");
                break;
            }
        }

        if (targetDevice == null) {
            Log.w(TAG, "未找到MAC地址为 " + macAddress + " 的已连接设备，将回退到默认设备选择逻辑");
        }
        
        return startUpgrade(targetDevice);
    }

    @Override
    public synchronized boolean startUpgrade(HmDevice device) {
        try {
            if (!isInitialized) {
                Log.e(TAG, "SDK is not initialized. Please call init() first.");
                return false;
            }
            
            if (isUpgrading) {
                Log.w(TAG, "OTA升级已在进行中，请勿重复操作");
                return false;
            }
            
            // 检查上下文是否有效
            if (context == null) {
                Log.e(TAG, "应用上下文为空，无法开始OTA升级");
                return false;
            }
            
            // 获取已连接的SPP设备
            ArrayList<HmDevice> connectedDevices;
            try {
                connectedDevices = getConnectedSppDevices();
            } catch (Exception e) {
                Log.e(TAG, "获取已连接设备时发生异常: " + e.getMessage(), e);
                return false;
            }
            
            if (connectedDevices.isEmpty()) {
                Log.e(TAG, "没有已连接的SPP设备，无法开始OTA升级");
                return false;
            }
            
            // 使用第一个已连接的设备或指定设备
            if (device != null) {
                // 检查指定的设备是否在已连接设备列表中
                boolean deviceFound = false;
                for (HmDevice connectedDevice : connectedDevices) {
                    if (connectedDevice.getDeviceMAC().equalsIgnoreCase(device.getDeviceMAC())) {
                        currentDevice = device;
                        deviceFound = true;
                        break;
                    }
                }
                
                if (!deviceFound) {
                    Log.w(TAG, "指定的设备未连接，将回退到使用第一个已连接的设备");
                    currentDevice = connectedDevices.get(0);
                }
            } else {
                currentDevice = connectedDevices.get(0);
            }
            
            Log.i(TAG, "使用设备进行OTA升级: " + currentDevice.getDeviceName() + " (" + currentDevice.getDeviceMAC() + ")");
            
            // 使用设置的文件路径或默认路径
            if (upgradeFilePath == null || upgradeFilePath.isEmpty()) {
                upgradeFilePath = defaultOtaFilePath;
            }
            Log.i(TAG, "使用OTA文件路径: " + upgradeFilePath);
            
            // 检查文件是否存在
            try {
                File otaFile = new File(upgradeFilePath);
                if (!otaFile.exists()) {
                    Log.e(TAG, "OTA文件不存在: " + upgradeFilePath);
                    // 为了测试，即使文件不存在也尝试继续
                    Log.w(TAG, "警告: OTA文件不存在，但仍尝试继续升级流程");
                }
            } catch (Exception e) {
                Log.e(TAG, "检查OTA文件时发生异常: " + e.getMessage(), e);
            }
            
            // 初始化ServiceConfig
            try {
                serviceConfig = new BesServiceConfig();
                serviceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
                serviceConfig.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID);
                serviceConfig.setUseTotaV2(false);
                serviceConfig.setTotaConnect(false);
                serviceConfig.setUSER_FLAG(1);
                serviceConfig.setCurUser(1);
                serviceConfig.setDevice(currentDevice);
            } catch (Exception e) {
                Log.e(TAG, "配置OTA服务参数时发生异常: " + e.getMessage(), e);
                return false;
            }
            
            Log.i(TAG, "创建OTA服务");
            // 创建OTA服务
            try {
                BesOtaService besOtaService = new BesOtaService(serviceConfig, this, context);
                otaTask = besOtaService;
                isUpgrading = true;
                Log.i(TAG, "OTA升级服务创建成功");
                return true;
            } catch (Exception e) {
                Log.e(TAG, "创建OTA服务失败: " + e.getMessage(), e);
                // 重置状态
                isUpgrading = false;
                otaTask = null;
                return false;
            }
            
        } catch (Exception e) {
            Log.e(TAG, "启动OTA升级过程中发生未预期的异常: " + e.getMessage(), e);
            // 确保状态重置
            isUpgrading = false;
            otaTask = null;
            return false;
        }
    }
    
    @Override
    public boolean isInitialized() {
        return isInitialized;
    }
    
    @Override
    public boolean isUpgrading() {
        return isUpgrading;
    }
    
    @Override
    public void setProgressListener(YOTAApi.ProgressListener listener) {
        this.progressListener = listener;
        Log.i(TAG, "设置进度监听器");
    }
    
    @Override
    public void setStatusListener(YOTAApi.StatusListener listener) {
        this.statusListener = listener;
        Log.i(TAG, "设置状态监听器");
    }
    
    @Override
    public boolean cancelUpgrade() {
        if (otaTask != null && isUpgrading) {
            Log.i(TAG, "取消OTA升级");
            otaTask.stopDataTransfer();
            otaTask = null; // 立即将otaTask置为null
            isUpgrading = false;
            return true;
        }
        Log.i(TAG, "无法取消OTA升级，没有正在进行的升级任务");
        return false;
    }
    
    /**
     * 通知状态变更
     * @param status OTA状态
     */
    private void notifyStatusChanged(YOTAApi.Status status) {
        if (statusListener != null) {
            statusListener.onStatusChanged(status);
        }
    }
    
    // BesServiceListener接口实现
    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {
        Log.i(TAG, "TOTA连接状态变更: " + (state ? "已连接" : "已断开"));
        notifyStatusChanged(state ? YOTAApi.Status.STARTED : YOTAApi.Status.CANCELED);
    }
    
    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {
        Log.e(TAG, "收到错误消息，错误码: " + msg);
        if (statusListener != null) {
            statusListener.onStatusChanged(YOTAApi.Status.FAILED);
            statusListener.onError(msg, "OTA错误 " + msg);
        }
        isUpgrading = false;
    }
    
    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        Log.i(TAG, "状态变更消息: " + msg + ", " + msgStr);
        switch (msg) {
            case OTA_CMD_GET_HW_INFO:
                Log.i(TAG, "收到硬件信息，准备开始OTA升级");
                if (otaTask != null && upgradeFilePath != null) {
                    // 设置OTA配置
                    RemoteOTAConfig config = new RemoteOTAConfig();
                    config.setLocalPath(upgradeFilePath);
                    otaTask.setOtaConfig(config);
                    
                    // 开始数据传输
                    OTADfuInfo otaDfuInfo = new OTADfuInfo("001", 0); // breakpoint为0则从0开始升级，为1则从断点处升级
                    Log.i(TAG, "开始OTA数据传输");
                    otaTask.startDataTransfer(otaDfuInfo, this);
                    
                    notifyStatusChanged(YOTAApi.Status.STARTED);
                } else {
                    Log.e(TAG, "OTA任务或文件路径为空，无法开始数据传输");
                }
                break;
            default:
                Log.i(TAG, "收到其他状态变更消息: " + msg);
                break;
        }
    }
    
    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {
        Log.i(TAG, "收到成功消息: " + msg);
        if (statusListener != null) {
            statusListener.onStatusChanged(YOTAApi.Status.SUCCEED);
            statusListener.onSuccess();
        }
        isUpgrading = false;
    }
    
    // OTATask.StatusListener接口实现
    @Override
    public void onOTAStatusChanged(OTAStatus newStatus, HmDevice hmDevice) {
        Log.i(TAG, "OTA状态变更: " + newStatus.getName());
        notifyStatusChanged(YOTAApi.Status.fromOTAStatus(newStatus));
        
        if (newStatus == OTAStatus.STATUS_SUCCEED || 
            newStatus == OTAStatus.STATUS_FAILED || 
            newStatus == OTAStatus.STATUS_CANCELED) {
            Log.i(TAG, "OTA升级结束，状态: " + newStatus.getName());
            isUpgrading = false;
        }
    }
    
    @Override
    public void onOTAProgressChanged(float progress, HmDevice hmDevice) {
        Log.i(TAG, "OTA进度更新: " + progress + "%");
        if (progressListener != null) {
            progressListener.onProgressChanged(progress);
        }
    }
    
    /**
     * 防止通过反序列化创建实例
     */
    private Object readResolve() {
        return instance;
    }
}
