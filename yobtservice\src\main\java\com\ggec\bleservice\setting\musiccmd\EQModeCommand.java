package com.ggec.bleservice.setting.musiccmd;

import com.ggec.bleservice.setting.Command;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 设置EQ均衡器模式命令
 * 负责切换耳机的EQ音效模式，包括音乐、电影、新闻和老年人模式
 */
public class EQModeCommand extends Command {
    private static final String TAG = "EQModeCommand";
    
    // 命令前缀，用于确认是EQ模式命令
    private static final String COMMAND_PREFIX = "99EC830001";
    
    // 命令基础部分
    private static final String COMMAND_BASE = "99EC830001";
    
    // 命令校验和后缀
    private static final String COMMAND_SUFFIX = "1234";
    
    // EQ模式常量
    public static final int MUSIC_MODE = 0;     // 音乐模式
    public static final int MOVIE_MODE = 1;     // 电影模式
    public static final int NEWS_MODE = 2;      // 新闻模式
    public static final int ELDERLY_MODE = 3;   // 老年人模式
    
    // 当前EQ模式
    private final int mode;
    
    /**
     * 构造方法
     * @param mode EQ模式，可选值见常量定义
     */
    public EQModeCommand(int mode) {
        super();
        // 验证模式合法性
        if (mode != MUSIC_MODE && mode != MOVIE_MODE && mode != NEWS_MODE && mode != ELDERLY_MODE) {
            throw new IllegalArgumentException("无效的EQ模式: " + mode);
        }
        this.mode = mode;
        
        // 设置命令前缀
        setCommandPrefix("99EC83");
    }
    
    @Override
    public String getCommandData() {
        // 构造完整命令，格式: COMMAND_BASE + mode(十六进制) + COMMAND_SUFFIX
        String modeHex = String.format("%02X", mode);
        return COMMAND_BASE + modeHex + COMMAND_SUFFIX;
    }
    
    @Override
    public String parseResponse(String responseData) {
        YoBTSDKLog.d(TAG, "解析响应数据: " + responseData);
        
        if (responseData == null || responseData.length() < 12) {
            return "响应数据格式错误";
        }
        
        // 获取命令状态字节
        String statusByteHex = responseData.substring(10, 12);
        
        try {
            // 将十六进制状态转换为整数
            int statusMode = Integer.parseInt(statusByteHex, 16);
            
            // 判断设置是否成功
            boolean isSuccess = statusMode == mode;
            
            String modeName = getModeNameByCode(mode);
            String result = isSuccess ? 
                    modeName + "设置成功" : 
                    modeName + "设置失败";
            
            // 计算 resultValue，格式为 status(两位) + mode(两位)
            // 例如：0102 表示成功切换到新闻模式(模式代码2)
            int resultValue;
            if (isSuccess) {
                // 将状态(1)和模式合并为一个数字，例如：成功(01) + 新闻模式(02) = 102
                resultValue = (1 * 100) + statusMode;
            } else {
                resultValue = 0; // 失败状态
            }
            
            // 通知命令完成
            notifyCompletion(isSuccess ? ResultCode.SUCCESS : ResultCode.FAILED, resultValue, result);
            
            return result;
        } catch (NumberFormatException e) {
            YoBTSDKLog.e(TAG, "解析EQ模式状态失败: " + statusByteHex, e);
            
            // 通知命令失败
            notifyCompletion(ResultCode.FAILED, 0, "解析EQ模式状态失败");
            
            return "解析EQ模式状态失败";
        }
    }
    
    @Override
    public boolean isResponseMatch(String responseData) {
        // 检查响应前缀是否匹配
        if (responseData != null && responseData.length() >= 10) {
            String prefix = responseData.substring(0, 10);
            return COMMAND_PREFIX.equals(prefix);
        }
        return false;
    }
    
    /**
     * 根据模式代码获取模式名称
     * @param modeCode 模式代码
     * @return 模式名称
     */
    private String getModeNameByCode(int modeCode) {
        switch (modeCode) {
            case MUSIC_MODE:
                return "音乐模式";
            case MOVIE_MODE:
                return "电影模式";
            case NEWS_MODE:
                return "新闻模式";
            case ELDERLY_MODE:
                return "老年人模式";
            default:
                return "未知模式";
        }
    }
} 