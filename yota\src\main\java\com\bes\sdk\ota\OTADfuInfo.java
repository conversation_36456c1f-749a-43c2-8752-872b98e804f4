package com.bes.sdk.ota;

public class OTADfuInfo
{
    // OTA DFU version.
    private String mVer;

    // OTA DFU breakpoint.
    private int mBp;

    public OTADfuInfo(String version, int breakpoint)
    {
        mVer = version;
        mBp = breakpoint;
    }

    /**
     * Get the DFU version, can be null if there is no DFU available.
     * @return
     */
    public String getVersion() { return mVer; };

    /**
     * Get DFU breakpoint, the data length that already transferred to device.
     * @return
     */
    public int getBreakpoint() { return mBp; }

    @Override
    public String toString() {
        return "OTADfuInfo{" +
                "\nmVer='" + mVer + '\'' +
                "\nmBp=" + mBp +
                '}';
    }
}
