<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/control_settings_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/comp_background_gray"
    android:fitsSystemWindows="false"
    tools:context=".activity.ControlSettingsActivity">

    <!-- 顶部导航栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/nav_bar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/comp_background_gray"
        android:elevation="0dp"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="4dp"
            android:padding="12dp"
            android:src="@drawable/ic_public_back"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="控制设置"
            android:textColor="@color/font_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 内容区域 - 使用ScrollView确保所有内容可滚动 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@+id/nav_bar"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <!-- 控制面板标签页 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- 标签栏 -->
                    <com.google.android.material.tabs.TabLayout
                        android:id="@+id/tab_layout_controls"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:background="@color/background_primary"
                        app:tabTextColor="@color/font_secondary"
                        app:tabSelectedTextColor="@color/brand"
                        app:tabIndicatorColor="@color/brand"
                        app:tabIndicatorHeight="2dp"
                        app:tabMode="fixed"
                        app:tabGravity="fill"
                        app:tabTextAppearance="@style/TabTextAppearance"/>

                    <!-- ViewPager for tab content -->
                    <androidx.viewpager2.widget.ViewPager2
                        android:id="@+id/view_pager_controls"
                        android:layout_width="match_parent"
                        android:layout_height="480dp"/>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 恢复默认控制按钮 - 独立样式 -->
            <com.ggec.hs01.view.HMButton
                android:id="@+id/btn_reset_controls"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_marginTop="6dp"
                android:layout_marginBottom="8dp"
                android:text="恢复默认控制"
                android:textSize="15sp"/>

            <com.ggec.hs01.view.HMButton
                android:id="@+id/btn_goto_test"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="12dp"
                android:text="跳转测试页面"
                android:textSize="15sp"/>

        </LinearLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout> 