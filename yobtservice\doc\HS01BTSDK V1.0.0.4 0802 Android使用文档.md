# HS01 耳机 Android SDK API文档

| 项目 | 内容         |
| --- |------------|
| 版本号 | V1.0.0.4   |
| 修改日期 | 2025/08/02 |
| 编写人员 | 杨庭锋        |

---

## 更新日志（Changelog）

- V1.0.0.4 (2025/08/02)
  - **新增功能
    - SDK版本从33降级到30
  - **优化功能:
    - 游戏模式、EQ切换需要主动上报app处理

- V1.0.0.4 (2025/08/01)
  - **新增功能
    - 新增EQ模式切换、游戏模式开关状态主动监听
  - **重要优化: 电量显示统一优化**
    - 将所有无效电量值显示从 `0%` 统一改为 `--`，提供更清晰的"无数据"状态表示
    - 修改电量相关接口返回类型从 `int` 改为 `String`，更好地支持非数字显示
    - 优化电量范围验证机制，超出0-100%范围的电量值统一显示为 `--`
    - 涉及场景：设备未连接、电量解析失败、数据超出有效范围、蓝牙断开等情况
    - 更新了所有电量相关的命令类、管理器和API接口，确保整个电量模块的一致性
  - **优化功能: 控制设置恢复机制优化**
    - 优化恢复默认控制设置功能，使用统一的 `resetControlSettings()` 命令
    - 简化命令发送逻辑，从4个独立的控制设置命令改为1个统一的恢复命令
    - 提高恢复默认设置的效率和可靠性
    - 优化发送队列实现，优化多命令并发等待处理

- V1.0.0.4 (2025/07/30)
  - **优化: 智能扫描器自动选择机制**
    - 新增设备BLE扩展功能自动检测机制，系统会根据设备能力智能选择最优扫描器
    - 如果设备支持BLE扩展功能，自动使用增强型扫描器(EnhancedBleScanner)，享受更强大的扫描功能
    - 如果设备不支持或检测失败，自动回退到标准扫描器(ModernBleScanner)，确保基础功能可用
    - 新增多维度检测算法：Android版本、硬件支持、位置服务、扩展广告功能、权限状态等
    - 优化扫描器创建的健壮性，提供详细的检测日志和回退机制
  - **优化功能: 完善权限管理机制**
    - 在应用启动时自动检查和申请BLE相关权限（位置、蓝牙扫描、蓝牙连接、蓝牙广播）
    - 新增权限状态详细日志输出，帮助开发者了解权限授予情况
    - 优化权限申请流程，支持Android 6.0+ 到 Android 13+ 的全版本兼容
    - 增强型扫描器现在能充分利用位置权限提供的高级功能
  - **修复: 修复扫描服务自动初始化问题**
    - 修复了当系统蓝牙关闭时初始化SDK，蓝牙开启后扫描服务无法自动重新初始化的问题
    - 优化了`ScanManager.createScanner()`方法的逻辑，移除了导致扫描器无法重新创建的返回问题
    - 增强了蓝牙状态变化的容错处理，提升了SDK在各种蓝牙状态切换场景下的稳定性
  - **优化功能: 优化命令回调机制**
    - 重构设备命令的回调处理机制，统一使用 `notifyCompletion()` 方法处理命令完成回调
    - 优化命令结果状态码处理，提供更详细的错误状态反馈和结果值返回
  - **重要优化: 优化命令队列超时机制**
    - 修复了CommandQueueCenter中超时定时器被不匹配数据干扰的问题
    - 解决了设备主动上报数据（如电量信息）导致命令执行超时的错误判断
    - 提升了命令执行的稳定性，显著减少偶发性命令超时失败
    - 将默认命令超时时间从550ms增加到800ms，为蓝牙通信延迟提供更多缓冲

- V1.0.0.3 (2025/07/25)
  - **重构日志系统: 引入文件日志功能**
    - 新增 `setFileLogEnabled(boolean enabled)` 和 `isFileLogEnabled()` 接口，用于控制文件日志的生成。
    - 移除了旧的 `setLogEnabled` 和 `isLogEnabled` 接口。
    - 移除了 `initWithDebug` 调试模式，简化了初始化流程。
  - **优化功能: 重构设备连接状态回调机制**
    - 优化 `onConnectionStateChanged` 回调，现在只有在设备完全就绪后才会以 `connected=true` 回调
    - 应用层可以直接在 `onConnectionStateChanged(device, true)` 回调中开始发送命令，无需再等待 `onDeviceReadyStateChanged`
    - 简化了应用层连接就绪的判断逻辑，提升了用户体验
  - **新增功能: 新增经典蓝牙断开事件监听**
    - 新增 `OnHeadsetDisconnectedListener` 接口，用于监听系统级别的经典蓝牙设备断开事件。
    - 新增 `addHeadsetDisconnectedListener` 和 `removeHeadsetDisconnectedListener` 方法，用于管理监听器。
    - 应用层现在可以接收到任何经典蓝牙设备断开的通知，并获取其MAC地址。
  - **新增功能: 系统蓝牙关闭事件回调**
    - 新增 `systemBluetoothClose()` 回调接口，用于监听系统蓝牙状态。
    - 当用户关闭系统蓝牙时，会触发此回调，方便App进行UI更新和状态重置。
    - 相应地更新了相关的内部事件分发机制。
  - **新增功能: 控制功能新增 无**
    - 新增功能常量8表示无
  - **优化功能: 优化就绪状态判断**
    - 优化对设备就绪状态的判断

- V1.0.0.3 (2025/07/19)
  - **Demo APP 新增功能：经典蓝牙连接状态显示**
    - 在蓝牙设备列表中新增显示经典蓝牙连接状态
    - 通过 `YoSPPApi.isSppDeviceConnected(String macAddress)` 接口检测广播中解析出的MAC地址是否已连接
    - 优化了对BLE广播解析的MAC地址与经典蓝牙连接状态的联动处理
    - 实现了设备列表的定时自动刷新，实时更新经典蓝牙连接状态
    - 主要实现逻辑是：
      在扫描到设备时，解析广播数据获取经典蓝牙MAC地址。
      使用YoSPPApi的isSppDeviceConnected方法检查该MAC地址对应的设备是否已连接。
      显示检查结果（"已连接"或"未连接"）。
      通过定时器定期刷新列表，保持连接状态的更新。
  - **优化Demo APP GGECHSApplication类**
    - 新增 `getSppApi()` 方法，提供统一的YoSPPApi实例获取入口
    - 添加SPP服务的自动初始化和释放资源逻辑，优化内存管理
    - 优化 `initSppService()` 和 `releaseSppService()` 方法，增强SDK稳定性

- V1.0.0.3 (2025/07/18)
  - **接口优化: 统一连接失败与断开连接回调**
    - `onConnectFailed(BluetoothDevice device, boolean failed)`，返回一个布尔值
    - `onDeviceDisconnected(BluetoothDevice device, boolean disconnected)`，返回布尔值，与连接失败回调保持一致。

- V1.0.0.3 (2025/07/17)
  - **新增功能：Demo APP中实现featurecode广播功能**
    - 新增示例代码展示如何解析设备广播中的featurecode信息
    - 优化广播数据解析逻辑，支持在扫描阶段直接获取设备特征码
  - **重要更新：新增对SPP的支持**
    - 新增 `isSppDeviceConnected(String macAddress)` 接口，用于检查指定MAC地址的经典蓝牙设备是否已连接。 
    - 修复OTA对设备的MAC地址匹配的问题
    - 新增 `getParsedAdvertisingDataAsString(BluetoothDevice device)` 接口，以字符串形式返回解析后的广播数据，简化了应用层调用并降低了模块耦合度。
  - **优化连接回调逻辑**
    - `onConnectionStateChanged` 现在仅在连接成功时触发 (`connected` 始终为 `true`)。
    - 设备断开时将只触发 `onDeviceDisconnected` 回调，避免了之前同时触发两个回调的问题。
  - **更新命令回调接口**
    - `CommandResultCallback` 接口更新：
      - `onCommandResult` 回调现在返回 `(int code, int resultValue)` 两个参数。
      - `resultValue` 是命令执行结果具体状态。
    - **开关类命令** (`setVolumeAdaptive`, `setWearDetection` 等):
      - `resultValue` 返回 `0` (关闭) 或 `1` (开启)。
    - **模式类命令** (`setEQMode`, `setControlFunction`):
      - `resultValue` 采用 `状态 + 模式` 的格式 (例如，`102` 表示成功切换到模式2)。
  - **优化首页聚合获取**
    - 优化获取速度，减少时延


- V1.0.0.2 (2025/07/15)
  - **重要更新：扫描与数据解析API重构**
    - 新增 `setFilterByFeatureCode(boolean enable)` 接口，用于开启基于内部特征码的设备过滤，这是识别我们设备的推荐方式。
    - `setCustomDeviceNameFilter(boolean enable)` 接口被保留，用于按设备名称 ("HS"或"vidda") 过滤，可与特征码过滤同时使用。
    - 新增 `getParsedAdvertisingData(BluetoothDevice device)` 接口，用于在扫描到设备后获取其解析后的广播数据（MAC地址、颜色、特征码）。
    - 废弃并移除了旧的 `parseAdvertisingData(byte[] serviceData)` 和 `getCustomServiceUuid()` 等相关方法。
    - 内部实现重构，将数据解析逻辑完全封装在 `scanner` 包中，`YoBLEApi` 只提供简洁接口。
  - **优化聚合命令稳定性**
    - 重构了 `getHomePageStatus` 和 `getAboutDeviceStatus` 等聚合命令的内部实现。
    - 聚合命令现在会智能等待多个相关数据包返回，并在超时后将所有数据整合，通过一次回调返回，减少了回调次数并保证了数据完整性。
    - 修复了聚合命令在执行后，可能会被设备主动上报的其它数据（如独立电量上报）错误触发的问题，确保聚合命令的回调只包含其请求的聚合数据。
  - **优化寻找耳机功能**
    - `findAlertLeft` 和 `findAlertRight` API 现在支持通过布尔参数来启动或停止响铃，从而实现了取消正在进行中的响铃的功能。
  - **优化连接状态回调**
    - 在 `BleListener` 中新增 `onDeviceDisconnected(BluetoothDevice device, int status)` 回调，专门用于处理设备断开连接的事件，使连接和断开的逻辑更清晰。


- V1.0.0.1 (2025/07/14)
  - 调整SDK包的开发架构（使用jdk-1.8 , gradle 7.5）

- V1.0.0.1 (2025/07/11)
  - **重要更新：寻找提示音API完全改造**
    - 移除旧的开关模式API，`setFindAlert`、`setFindAlertLeft`和`setFindAlertRight`
    - 新增触发响铃API：`triggerFindAlert`、`triggerFindAlertLeft`和`triggerFindAlertRight`
    - 响铃命令协议更新为直接触发模式，命令执行完成后耳机会立即响铃
    - 优化命令状态队列处理机制，强化单一队列管理命令
    - 修复因为Android 12 及以下版本修改MTU失败的问题
    - 优化电量主动上报机制的处理，让主动上报不会影响命令队列的执行
  - **新增BLE广播数据解析接口**
    - 新增 `getCustomServiceUuid()` 方法，用于获取过滤设备的服务UUID
    - 新增 `parseAdvertisingData()` 方法，用于解析BLE广播数据
    - 新增 `ParsedAdvertisingData` 类，用于存储解析后的特征码、颜色和MAC地址
    - 支持从广播数据中提取设备特征码、颜色和经典蓝牙MAC地址
    - 该功能处于测试阶段，在DemoAPP中，用户可以主动调用

- V******* (2025/07/10)
  - 优化状态码机制
  - 增加状态码详细说明文档
  - 明确数据通道中 `yobackdata` 和 `result` 的职责划分
  - 优化首页状态获取功能
  - 增加聚合状态获取结果的键值说明

- V******* (2025/07/09)
  - **重大架构重构**
  - 引入 `YoBTInit` 作为统一SDK初始化入口，整合BLE和SPP初始化流程。
  - `YoBTInit` 现在负责初始化 `YoBLEApi`, `YoCommandApi`, 和 `YoSPPApi`。
  - 移除 `YoBLEInitializer`，相关功能已全部迁移至 `YoBTInit`。
  - 重命名 `BleSDKManager` 为 `YoBTSDKManager`，并将其职责精简为版本管理。
  - 更新了部分接口名称，与iOS统一接口，增强可读性和一致性
  - 将获取设备型号名称功能从 `YoBLEApi` 迁移至 `YoCommandApi`，并改为异步命令方式。
  - 新增左耳和右耳单独寻找提示音控制API
  - 更新 `setFindAlert` API描述，明确其控制全耳机寻找提示音
  - 增加 `setFindAlertLeft` 和 `setFindAlertRight` API用于控制单耳提示音
  - 移除音量提醒的接口
  - 增加首页状态聚合回调
  - 增加设备状态聚合回调
  - 新增主动上报数据处理机制，用于接收耳机主动上报的电量和充电状态。
  - 新增 `BatteryReportCallback` 回调接口。
  - 新增 `setBatteryReportCallback` 和 `handleReportedData` API 用于处理主动上报。

- V1.0.4.0705 (2025/07/05)
  - 新增OTA升级回调接口(OtaResultCallback)
  - 新增带回调参数的OTA升级方法
  - 新增OTA状态查询方法(getOtaStatus)
  - 更新了部分接口名称，增强可读性和一致性

---

## 一、SDK 统一初始化 (YoBTInit)

`YoBTInit` 是 SDK 的统一入口，负责初始化所有蓝牙模块、权限管理和全局配置。

### 1.1 获取单例
```java
YoBTInit.getInstance()
```
- **描述**：获取 `YoBTInit` 的全局唯一实例。

### 1.2 SDK 初始化与释放
```java
void init(Application application)
```
- **描述**：初始化 SDK，应在 `Application.onCreate` 中调用。

```java
void release()
```
- **描述**：释放 SDK 所占用的所有资源，建议在应用退出时调用。

```java
boolean isInitialized()
```
- **描述**：检查 SDK 是否已经初始化。

### 1.3 权限与蓝牙状态
```java
boolean hasBluetoothPermissions(Context context)
```
- **描述**：检查应用是否已获得所有必需的蓝牙权限。现已支持完整的BLE权限检查，包括位置权限、蓝牙扫描、连接和广播权限。

```java
boolean checkAndRequestPermissions(Activity activity)
```
- **描述**：检查并请求缺失的蓝牙权限。**V1.0.0.3优化**: 现在会自动根据Android版本智能请求合适的权限组合，包括：
  - **Android 6-11**: 位置权限 + 传统蓝牙权限
  - **Android 12+**: 位置权限 + 新蓝牙权限（BLUETOOTH_SCAN, BLUETOOTH_CONNECT, BLUETOOTH_ADVERTISE）
  - **Android 13+**: 增加媒体权限支持
  
```java
boolean checkBluetoothEnabled(Activity activity)
```
- **描述**：检查系统蓝牙是否开启，若未开启则会发起开启请求。

> **权限管理优化 (V1.0.0.3)**: SDK现在会在应用启动时自动检查权限状态并输出详细的权限日志，帮助开发者了解权限授予情况。增强型扫描器需要位置权限才能发挥最佳性能，如果缺少位置权限，系统会自动使用标准扫描器确保基础功能可用。

### 1.4 回调与结果处理
```java
void setBluetoothEnableCallback(BluetoothEnableCallback callback)
```
- **描述**：设置蓝牙开启请求的结果回调。

```java
void setPermissionResultCallback(PermissionResultCallback callback)
```
- **描述**：设置权限请求的结果回调。

```java
boolean handleActivityResult(int requestCode, int resultCode, Intent data)
```
- **描述**：在 Activity 的 `onActivityResult` 中调用，用于处理 `checkBluetoothEnabled` 的结果。

```java
boolean handlePermissionResult(int requestCode, String[] permissions, int[] grantResults)
```
- **描述**：在 Activity 的 `onRequestPermissionsResult` 中调用，用于处理权限请求的结果。

### 1.5 API 实例获取
```java
YoBLEApi getBleApi()
```
- **描述**：获取 `YoBLEApi` 的实例，用于蓝牙核心操作（BLE）。

```java
YoCommandApi getCommandApi()
```
- **描述**：获取 `YoCommandApi` 的实例，用于向耳机发送指令（BLE）。

```java
YOTAApi getYOTAApi()
```
- **描述**：获取 `YOTAApi` 的实例，用于执行OTA升级操作。

```java
YoSPPApi getSppApi()
```
- **描述**：获取 `YoSPPApi` 的实例，用于经典蓝牙（SPP）通信。

---

## 二、蓝牙核心 API (YoBLEApi)

`YoBLEApi` 封装了低功耗蓝牙（BLE）设备的扫描、连接、数据通信等核心功能。

### 2.1 事件监听器 (BleListener)
通过 `registerListener` 注册监听器以接收各类蓝牙事件。

- `onDeviceFound(BluetoothDevice device, int rssi)`: 发现设备时回调。
- `onScanStarted()`: 扫描开始时回调。
- `onScanFinished()`: 扫描结束时回调。
- `onConnectionStateChanged(BluetoothDevice device, boolean connected)`: 只有在设备完全就绪后才会以`connected=true`回调。
- `onConnectFailed(BluetoothDevice device, boolean failed)`: 连接设备失败时回调。
- `onDeviceDisconnected(BluetoothDevice device, boolean disconnected)`: 已连接的设备断开时回调。
- `onDeviceReadyStateChanged(boolean ready)`: 设备就绪状态变更时回调。
- `onError(String errorMessage)`: 发生错误时回调。
- `systemBluetoothClose()`: 当系统蓝牙关闭时回调。
- `systemBluetoothOpen()`: 当系统蓝牙开启时回调。

### 2.2 核心管理
```java
YoBLEApi.getInstance()
```
- **描述**：获取 `YoBLEApi` 的全局唯一实例。

```java
void init(Context context)
```
- **描述**：初始化蓝牙服务。此方法由 `YoBTInit` 自动调用。

```java
void release()
```
- **描述**：释放蓝牙服务资源。此方法由 `YoBTInit` 自动调用。

```java
void registerListener(BleListener listener)
```
- **描述**：注册一个蓝牙事件监听器。

```java
void unregisterListener(BleListener listener)
```
- **描述**：注销一个已注册的蓝牙事件监听器。

### 2.3 设备扫描
```java
boolean startScan()
```
- **描述**：开始扫描蓝牙设备。

```java
boolean startScan(int timeoutMs)
```
- **描述**：开始扫描，并在指定时间（毫秒）后自动停止。

```java
boolean startScanWithConfig(ScanConfig config)
```
- **描述**：使用自定义配置进行扫描。

```java
void stopScan()
```
- **描述**：手动停止设备扫描。

```java
boolean isScanning()
```
- **描述**：检查当前是否正在扫描。

### 2.4 扫描过滤
```java
boolean setFilterNoNameDevices(boolean enable)
```
- **描述**：设置是否过滤掉没有设备名称的蓝牙设备。

```java
boolean isFilterNoNameDevicesEnabled()
```
- **描述**：检查是否已启用"无名称设备"过滤。

```java
boolean setCustomDeviceNameFilter(boolean enable)
```
- **描述**：设置是否启用自定义设备名称过滤。启用后，将只扫描名称以 "HS" 或 "vidda" 开头的设备（不区分大小写），同时也会过滤掉没有名称的设备。

```java
boolean isCustomDeviceNameFilterEnabled()
```
- **描述**：检查是否已启用自定义设备名称过滤。

```java
boolean setFilterByFeatureCode(boolean enable)
```
- **描述**：设置是否通过特征码过滤设备。启用后，SDK将只发现符合预定义特征码的设备，这是识别我们自己设备的核心方法。

```java
boolean isFilteringByFeatureCode()
```
- **描述**：检查是否已启用特征码过滤。

### 2.5 设备连接

```java
void disconnect()
```
- **描述**：断开当前已连接的设备。

```java
boolean isDeviceConnected()
```
- **描述**：检查是否已连接到设备。

```java
boolean isDeviceReady()
```
- **描述**：检查设备是否已就绪。设备就绪指设备已连接、服务已发现且通知已启用，只有设备就绪后才能发送命令。

```java
void registerDeviceReadyCallback(DeviceReadyCallback callback)
```
- **描述**：注册设备就绪状态监听器，此方法为便捷方法，可直接监听设备就绪状态而不需要注册完整的BleListener。

```java
interface DeviceReadyCallback
```
- **描述**：设备就绪状态回调接口。
- `void onDeviceReady(boolean ready)`: 设备就绪状态变化时调用。

### 2.6 BLE广播数据解析
此功能用于从设备的蓝牙广播中提取自定义信息，如经典蓝牙MAC地址、设备颜色等。

```java
String getParsedAdvertisingDataAsString(BluetoothDevice device)
```
- **描述**： **(推荐)** 获取指定蓝牙设备的解析后广播数据，并以字符串形式返回。此方法最适合用于日志记录或简单的信息展示，因为它避免了对 `ParsedAdvertisingData` 类的直接依赖。
- **参数**:
    - `device`: `onDeviceFound` 回调中返回的 `BluetoothDevice` 对象。
- **返回值**：一个包含所有解析信息的字符串，格式为 `"ParsedAdvertisingData{macAddress='...', color='...', featureCode='...'}"`。如果该设备没有可解析的数据则返回 `null`。

```java
ParsedAdvertisingData getParsedAdvertisingData(BluetoothDevice device)
```
- **描述**：获取指定蓝牙设备的解析后广播数据，返回一个结构化对象。当您需要单独访问某个特定数据（如只获取MAC地址）时，此方法比 `getParsedAdvertisingDataAsString` 更为适用。
- **参数**:
    - `device`: `onDeviceFound` 回调中返回的 `BluetoothDevice` 对象。
- **返回值**：一个 `ParsedAdvertisingData` 对象，如果该设备没有可解析的数据则返回 `null`。

```java
public class ParsedAdvertisingData
```
- **描述**：解析后的BLE广播数据容器类，通过 `getParsedAdvertisingData` 方法返回。包含以下方法：
  - `String getMacAddress()`: 获取经典蓝牙MAC地址 (格式: XX:XX:XX:XX:XX:XX)。
  - `String getColor()`: 获取设备颜色代码 (十六进制字符串)。
  - `String getFeatureCode()`: 获取设备特征码。
  - `UUID getServiceUuid()`: 获取设备广播的服务UUID。

### 2.8 状态与信息
```java
boolean isBluetoothSupported()
```
- **描述**：检查设备是否支持蓝牙。

```java
boolean isBluetoothEnabled()
```
- **描述**：检查系统蓝牙是否已开启。

```java
String getSDKVersion()
```
- **描述**：获取 SDK 版本名称 (例如 "*******")。

```java
int getSDKVersionCode()
```
- **描述**：获取 SDK 版本号 (例如 10000)。

```java
boolean isBleManagerInitialized()
```
- **描述**：检查蓝牙核心管理器是否已初始化。

### 2.9 日志管理
```java
void setFileLogEnabled(boolean enabled)
```
- **描述**：设置是否启用文件日志记录。启用后，SDK的运行日志将被写入应用的私有目录中，便于调试和问题排查。

```java
boolean isFileLogEnabled()
```
- **描述**：检查文件日志记录是否已启用。

### 2.10 OTA 相关接口

```java
void IsitCanOTA(IsitCanOtaCallback callback)
```
- **描述**：查询设备是否可以进行OTA升级。

---

## 三、耳机命令 API (YoCommandApi)

`YoCommandApi` 提供了向耳机发送具体功能指令的接口（基于BLE）。

### 3.1 核心管理
```java
YoCommandApi.getInstance()
```
- **描述**：获取 `YoCommandApi` 的全局唯一实例。

```java
void release()
```
- **描述**：释放命令 API 资源。此方法由 `YoBTInit` 自动调用。

### 3.2 命令状态码

所有命令执行后都会返回一个状态码，表示命令执行的结果。状态码定义在 `YoCommandApi.CommandResultCode` 类中。
- **SUCCESS (0)**：命令执行成功。表示命令已被设备正确接收、处理并返回了有效的结果。
- **TIMEOUT (1)**：命令执行超时。表示命令已发送，但在预定的时间内未收到设备的响应。这可能是由于设备未收到命令、设备处理命令时间过长，或者响应数据在传输过程中丢失等原因导致。
- **RESERVED (2)**：命令执行过快。
- **FAILED (3)**：命令执行失败。表示命令执行过程中发生了错误，如参数错误、设备内部错误、设备不支持该命令等。具体的错误原因可能会在命令的结果数据中提供。

在处理命令回调时，应首先检查状态码，只有在状态码为 `SUCCESS` 时才能保证返回的数据是有效的。

### 3.3 回调接口
- `CommandResultCallback`: 通用命令结果回调。
  - `onCommandResult(int code, int resultValue)`:
    - `code`: 命令执行结果状态码（参考 `CommandResultCode`）。
    - `resultValue`: 一个复合状态值。对于开关类命令，`0` 表示关闭，`1` 表示开启。对于模式类命令，它通常由 `状态(1位) + 模式(多位)` 组成，例如 `102` 表示成功切换到模式2。
- `BatteryInfoCallback`: 电池电量信息回调。
  - `onBatteryInfoUpdated(int code, int leftLevel, int rightLevel, int caseLevel)`: 电量信息更新回调。
    - `code`: 命令执行结果状态码
    - `leftLevel`: 左耳电量百分比 (0-100)
    - `rightLevel`: 右耳电量百分比 (0-100)  
    - `caseLevel`: 充电盒电量百分比 (0-100)
- `CaseChargingStatusCallback`: 充电盒充电状态回调。
  - `onCaseChargingStatusUpdated(int code, boolean isCharging)`: 充电状态更新回调。
    - `code`: 命令执行结果状态码
    - `isCharging`: 是否正在充电
- `EarInCaseStatusCallback`: 耳机在充电盒内状态回调。
  - `onEarInCaseStatusUpdated(int code, int status, boolean leftInCase, boolean rightInCase)`: 耳机在盒内状态更新回调。
    - `code`: 命令执行结果状态码
    - `status`: 状态值 (参考状态常量)
    - `leftInCase`: 左耳是否在盒内
    - `rightInCase`: 右耳是否在盒内
- `BatteryStateCallback`: 电池状态变化回调，用于 `startBatteryMonitor`。
    - `onBatteryStateChanged()`: 当任何电池信息（电量、充电状态）发生变化时调用。调用此回调后，可以通过缓存获取方法获取最新状态。
- `EarColorCallback`: 耳机颜色信息回调。
- `FirmwareVersionCallback`: 固件版本信息回调。
- `ClassicBtAddressCallback`: 经典蓝牙MAC地址回调。
- `DeviceModelNameCallback`: 设备型号名称回调。
- `GlobalStatusCallback`: 全局设备状态回调，返回设备的全局状态信息。
- `HomePageStatusCallback`: 首页聚合状态回调，返回一个包含所有设备状态的有序数据字典。
- `AboutDeviceStatusCallback`: 关于设备页面的聚合状态回调，返回与设备信息相关的聚合数据。

### 3.4 音乐与音效设置
```java
void setVolumeAdaptive(boolean enabled, CommandResultCallback callback)
```
- **描述**：设置音量自适应功能开关。

```java
void setSoundQualityMode(boolean isHighQuality, CommandResultCallback callback)
```
- **描述**：设置音质模式。`true` 为高音质模式，`false` 为高续航模式。

```java
void setEQMode(int mode, CommandResultCallback callback)
```
- **描述**：设置 EQ 模式。
- **模式常量**:
    - `YoCommandApi.EQ_MUSIC_MODE` (0): 音乐模式
    - `YoCommandApi.EQ_MOVIE_MODE` (1): 电影模式
    - `YoCommandApi.EQ_NEWS_MODE` (2): 新闻模式
    - `YoCommandApi.EQ_ELDERLY_MODE` (3): 老人模式
- **`resultValue`**: `100 + mode` (成功时), `0` (失败时)。例如，`102` 表示成功切换到新闻模式。

### 3.5 功能开关设置
```java
void setWearDetection(boolean enabled, CommandResultCallback callback)
```
- **描述**：设置佩戴检测功能开关。

```java
void setVoiceWakeup(boolean enabled, CommandResultCallback callback)
```
- **描述**：设置语音唤醒功能开关。

```java
void setGameMode(boolean enabled, CommandResultCallback callback)
```
- **描述**：设置游戏模式开关。

```java
void setVolumeRemind(boolean enabled, CommandResultCallback callback)
```
- **描述**：设置音量提醒功能开关，开启后在音量过高时提醒用户。


```java
void setFallAlert(boolean enabled, CommandResultCallback callback)
```
- **描述**：设置掉落提醒功能开关。

```java
void findAlertLeft(boolean start, CommandResultCallback callback)
```
- **描述**：控制左耳寻找提示音。
- **参数**:
    - `start`: `true` - 开始响铃, `false` - 停止响铃。

```java
void findAlertRight(boolean start, CommandResultCallback callback)
```
- **描述**：控制右耳寻找提示音。
- **参数**:
    - `start`: `true` - 开始响铃, `false` - 停止响铃。

```java
void setEarLocation(boolean enabled, CommandResultCallback callback)
```
- **描述**：设置寻找耳机功能开关。

### 3.6 触控设置
```java
void setControlFunction(int earSide, int tapType, int function, CommandResultCallback callback)
```
- **描述**：自定义耳机的触控功能。
- **earSide**: `YoCommandApi.EAR_LEFT` (1), `YoCommandApi.EAR_RIGHT` (2)
- **tapType**: `YoCommandApi.TAP_TWICE` (1), `YoCommandApi.TAP_TRIPLE` (2)
- **function**: 
    - 0: 播放/暂停
    - 1: 音量+
    - 2: 音量-
    - 3: 上一曲
    - 4: 下一曲
    - 5: 语音助手
    - 6: EQ切换
    - 7: 游戏模式
    - 8: 无
- **`resultValue`**: `100 + function` (成功时), `0` (失败时)。例如，`103` 表示成功设置为“上一曲”。

```java
void resetControlSettings(CommandResultCallback callback)
```
- **描述**：将所有触控设置恢复为默认值。
- **`resultValue`**: `1` (成功), `0` (失败)。

### 3.7 电量信息

```java
void getBatteryInfo(BatteryInfoCallback callback)
```
- **描述**：异步获取所有电池电量信息（左耳、右耳、充电盒）。

```java
void getFullBatteryInfo(BatteryInfoCallback batteryCallback, CaseChargingStatusCallback chargingCallback)
```
- **描述**：异步获取完整的电池信息，包括电量和充电状态。

```java
String getLeftBatteryLevel()
```
- **描述**：获取左耳电池电量（从缓存中获取）。
- **返回值**：电量字符串（百分比数字或"--"），如果数据无效或未获取过电量则返回 "--"。

```java
String getRightBatteryLevel()
```
- **描述**：获取右耳电池电量（从缓存中获取）。
- **返回值**：电量字符串（百分比数字或"--"），如果数据无效或未获取过电量则返回 "--"。

```java
String getCaseBatteryLevel()
```
- **描述**：获取充电盒电池电量（从缓存中获取）。
- **返回值**：电量字符串（百分比数字或"--"），如果数据无效或未获取过电量则返回 "--"。

### 3.8 设备信息与应用设置
```java
void getEarColor(EarColorCallback callback)
```
- **描述**：获取耳机颜色。

```java
void getFirmwareVersion(FirmwareVersionCallback callback)
```
- **描述**：获取耳机固件版本。

```java
void getDeviceModelName(DeviceModelNameCallback callback)
```
- **描述**：获取设备型号名称。

```java
void clearPairing(CommandResultCallback callback)
```
- **描述**：清除耳机的配对记录。
- **`resultValue`**: `1` (成功), `0` (失败)。

```java
void setDeviceRename(String name, CommandResultCallback callback)
```
- **描述**：修改设备经典蓝牙名称。
- **`resultValue`**: `1` (成功), `0` (失败)。

```java
void getDeviceMacAddress(ClassicBtAddressCallback callback)
```
- **描述**：获取经典蓝牙（SPP）的MAC地址。

### 3.9 耳机在充电盒内状态
```java
void getEarInCaseStatus(EarInCaseStatusCallback callback)
```
- **描述**：获取耳机在充电盒内状态。
- **状态常量**:
    - `YoCommandApi.EARS_BOTH_OUT` (0): 两只耳机都不在盒内
    - `YoCommandApi.EAR_LEFT_IN` (1): 左耳在盒内，右耳不在
    - `YoCommandApi.EAR_RIGHT_IN` (2): 右耳在盒内，左耳不在
    - `YoCommandApi.EARS_BOTH_IN` (3): 两只耳机都在盒内

### 3.10 充电状态
```java
void getCaseChargingStatus(CaseChargingStatusCallback callback)
```
- **描述**：获取充电盒充电状态。

```java
boolean isCaseCharging()
```
- **描述**：检查充电盒是否正在充电。

### 3.11 聚合状态
```java
void getHomePageStatus(HomePageStatusCallback callback)
```
- **描述**：异步获取首页聚合状态。该接口会一次性获取电量、充电状态、固件版本以及所有功能开关状态，并将它们聚合成一个有序的数据字典返回。这可以有效简化首页数据的获取流程。
- **返回数据字典键值说明**:
    - `80_1`: 左耳电量 (0-100的数字字符串，或"--"表示无效数据)
    - `80_2`: 右耳电量 (0-100的数字字符串，或"--"表示无效数据)
    - `80_3`: 盒子电量 (0-100的数字字符串，或"--"表示无效数据)
    - `81`: 音质模式 (`00`: 高续航, `01`: 高音质)
    - `82`: 音量自适应 (`00`: 关闭, `01`: 开启)
    - `83`: EQ模式 (`00`: 音乐, `01`: 电影, `02`: 新闻, `03`: 老年人)
    - `84`: 音量提醒/听力保护 (`00`: 关闭, `01`: 开启)
    - `85_0`: 左耳双击功能代码
    - （播放+暂停/音量加/音量减/上一曲/下一曲/语音助手/EQ切换/游戏模式/无）
    - `85_1`: 右耳双击功能代码
    - `85_2`: 左耳三击功能代码
    - `85_3`: 右耳三击功能代码
    - `87`: 佩戴检测 (`00`: 关闭, `01`: 开启)
    - `88`: 语音唤醒(VAD) (`00`: 关闭, `01`: 开启)
    - `89`: 游戏模式 (`00`: 关闭, `01`: 开启)
    - `8A`: 掉落提醒 (`00`: 关闭, `01`: 开启)
    - `94`: 固件版本号 (字符串, e.g., "1.0.0.5")

```java
void getAboutDeviceStatus(AboutDeviceStatusCallback callback)
```
- **描述**：异步获取关于设备页面的聚合状态。此接口会一次性获取设备型号、固件版本、经典蓝牙MAC地址等设备信息，便于在"关于设备"页面展示。
- **返回数据字典键值说明**:
    - `70`: 设备名称
    - `71`: 产品型号
    - `72`: 序列号
    - `73`: MAC地址
    - `74`: 产品颜色
    - `75`: FeatureCode

### 3.12 电池状态自动监听

SDK提供了便捷的电池状态自动监听机制，可以在状态变化时主动通知应用。

```java
void startBatteryMonitor(BatteryStateCallback callback)
```
- **描述**：开始电池状态监听。启动成功后，SDK会持续在后台监听设备主动上报的电池状态。当任何状态（电量、充电状态等）发生变化时，会通过 `onBatteryStateChanged()` 回调通知应用。应用收到回调后，应调用 `getLeftBatteryLevel()`, `isCaseCharging()` 等方法来获取最新的状态值。

```java
void stopBatteryMonitor()
```
- **描述**：停止电池状态监听。调用后，SDK将不再处理设备主动上报的电池数据，以节省资源。

## 四、OTA升级接口 (YOTAApi)

> **注意**: 使用OTA升级功能需导入依赖包 **HS01SDKota-V1.1.0705.aar**

`YOTAApi` 提供了耳机固件OTA升级功能的简化接口。

### 4.1 核心功能

```java
static YOTAApi getInstance(Context context)
```
- **描述**: 获取YOTAApi单例实例，SDK内部自动初始化。

```java
boolean startUpgrade()
```
- **描述**: 使用默认配置启动升级，自动查找已连接设备和默认固件文件

```java
boolean startUpgrade(String macAddress)
```
- **描述**: 对指定MAC地址的设备进行升级。这是推荐的升级方式。SDK会优先尝试连接并升级指定MAC地址的设备。如果找不到该设备，将自动回退到默认行为（升级第一个找到的已连接设备）。
- **参数**: `macAddress` - 设备的经典蓝牙MAC地址 (格式: XX:XX:XX:XX:XX:XX)。

```java
void setProgressListener(ProgressListener listener)
```
- **描述**: 设置升级进度监听器
- **接口定义**:
  ```java
  interface ProgressListener {
      void onProgressChanged(float progress); // progress: 0-100
  }
  ```

```java
void setStatusListener(StatusListener listener)
```
- **描述**: 设置升级状态监听器
- **接口定义**:
  ```java
  interface StatusListener {
      void onStatusChanged(Status status);
      void onError(int errorCode, String message);
      void onSuccess();
  }
  ```

```java
boolean cancelUpgrade()
```
- **描述**: 取消当前升级

```java
boolean isInitialized()
```
- **描述**: 检查SDK是否已初始化

```java
boolean isUpgrading()
```
- **描述**: 检查是否正在升级

### 4.2 状态枚举

YOTAApi中定义了以下OTA状态枚举：

| 状态 | 名称 | 描述 |
|------|------|------|
| `UNKNOWN` | 未知状态 | 初始状态或无法识别的状态 |
| `STARTED` | 开始升级 | 升级流程已启动 |
| `UPDATING` | 升级中 | 正在传输固件数据 |
| `VERIFYING` | 验证中 | 固件传输完成，正在验证 |
| `SUCCEED` | 升级成功 | 升级流程成功完成 |
| `FAILED` | 升级失败 | 升级过程中发生错误 |
| `CANCELED` | 升级取消 | 升级已被用户或系统取消 |

---

## 五、SPP 通信 API (YoSPPApi)

`YoSPPApi` 提供了经典蓝牙（SPP）的扫描、连接和通信功能。

### 5.1 核心管理

```java
YoSPPApi.getInstance(Context context)
```
- **描述**：获取 `YoSPPApi` 的全局唯一实例。**注意**：此API通常不由开发者直接调用，应通过 `YoBTInit.getInstance().getSppApi()` 获取。

```java
void release()
```
- **描述**：释放SPP模块占用的所有资源。此方法由 `YoBTInit` 自动调用。

### 5.2 设备扫描与连接

```java
void scanSppDevices(String deviceNameFilter, long timeoutMillis, SppScanCallback callback)
```
- **描述**：扫描SPP设备。

```java
void stopScan()
```
- **描述**：停止扫描。

### 5.3 连接状态检查

```java
boolean isConnected()
```
- **描述**：检查当前SPP是否已连接。

```java
String getConnectedDeviceMac()
```
- **描述**：获取当前通过SPP连接的设备MAC地址。

```java
boolean isSppDeviceConnected(String macAddress)
```
- **描述**：检查指定MAC地址的设备是否已通过经典蓝牙（A2DP Profile）连接。这可以用于判断某个设备是否正与手机保持系统级别的音频连接。
- **参数**: `macAddress` - 要检查的设备MAC地址 (格式: XX:XX:XX:XX:XX:XX),不区分大小写。
- **返回值**：如果设备已连接则返回 `true`，否则返回 `false`。

### 5.4 事件监听

```java
void addClassicBTConnectionStateListener(ClassicBTConnectionStateListener listener) 
```
- **描述**: 添加一个监听器，用于接收系统级经典蓝牙设备断开的事件通知。此监听器可以捕获任何经典蓝牙设备的断开，不限于通过本SDK连接的设备。
- **参数**: `listener` - 要添加的监听器实例。

```java
void removeClassicBTConnectionStateListener(ClassicBTConnectionStateListener listener) 
```
- **描述**: 移除一个之前添加的设备断开事件监听器。
- **参数**: `listener` - 要移除的监听器实例。

```java
public interface ClassicBTDisconnectedListener
```
- **描述**: 经典蓝牙设备断开事件的回调接口。
- `void headsetIsDisconnected(String macAddress)`: 当一个经典蓝牙设备从系统断开时调用。
    - **参数**: `macAddress` - 已断开设备的MAC地址。
