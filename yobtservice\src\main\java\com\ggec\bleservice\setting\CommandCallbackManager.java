package com.ggec.bleservice.setting;

import android.os.Handler;
import android.os.Looper;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-29
 * Description: 
 * 命令回调管理器
 * 负责管理命令特定回调的生命周期，支持命令类型过滤和一次性回调机制
 */
public class CommandCallbackManager {
    private static final String TAG = "CommandCallbackManager";
    
    // 单例实例
    private static CommandCallbackManager instance;
    
    // 主线程Handler，用于确保回调在主线程执行
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    // 命令特定回调映射表 - 命令ID -> 回调列表
    private final Map<String, List<CallbackRegistration>> commandCallbacks = new ConcurrentHashMap<>();
    
    // 命令类型回调映射表 - 命令类名 -> 回调列表
    private final Map<String, List<CallbackRegistration>> typeCallbacks = new ConcurrentHashMap<>();
    
    // 全局回调列表（保持向后兼容）
    private final List<CallbackRegistration> globalCallbacks = new ArrayList<>();
    
    // 回调清理间隔（毫秒）
    private static final long CLEANUP_INTERVAL = 30 * 1000; // 30秒
    private static final long CALLBACK_EXPIRE_TIME = 60 * 1000; // 60秒过期
    private static final long STALE_CALLBACK_TIME = 10 * 1000; // 10秒认为可能已失效
    
    /**
     * 增强的回调注册信息，支持弱引用
     */
    public static class CallbackRegistration {
        private final WeakReference<Command.CommandCallback> callbackRef;
        private final boolean isOneTime;
        private final String commandType;
        private final long registrationTime;
        private volatile boolean isExecuted = false;
        private volatile boolean isInvalid = false;
        
        public CallbackRegistration(Command.CommandCallback callback, boolean isOneTime, String commandType) {
            this.callbackRef = new WeakReference<>(callback);
            this.isOneTime = isOneTime;
            this.commandType = commandType;
            this.registrationTime = System.currentTimeMillis();
        }
        
        public Command.CommandCallback getCallback() {
            Command.CommandCallback callback = callbackRef.get();
            if (callback == null) {
                isInvalid = true;
            }
            return callback;
        }
        
        public boolean isOneTime() { return isOneTime; }
        public String getCommandType() { return commandType; }
        public long getRegistrationTime() { return registrationTime; }
        public boolean isExecuted() { return isExecuted; }
        public boolean isInvalid() { return isInvalid || callbackRef.get() == null; }
        public void markExecuted() { this.isExecuted = true; }
        public void markInvalid() { this.isInvalid = true; }
        
        /**
         * 检查回调是否匹配指定的命令
         */
        public boolean matches(Command command) {
            if (isInvalid()) return false;
            if (commandType == null) return true; // 无类型限制
            return commandType.equals(command.getClass().getSimpleName());
        }
        
        /**
         * 检查回调是否已过期
         */
        public boolean isExpired() {
            long age = System.currentTimeMillis() - registrationTime;
            return age > CALLBACK_EXPIRE_TIME || isInvalid();
        }
        
        /**
         * 检查回调是否可能已失效（需要谨慎处理）
         */
        public boolean isStale() {
            long age = System.currentTimeMillis() - registrationTime;
            return age > STALE_CALLBACK_TIME || isInvalid();
        }
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized CommandCallbackManager getInstance() {
        if (instance == null) {
            instance = new CommandCallbackManager();
        }
        return instance;
    }
    
    /**
     * 私有构造方法
     */
    private CommandCallbackManager() {
        // 启动更频繁的清理任务
        startAggressiveCleanupTask();
    }
    
    /**
     * 为特定命令ID注册回调
     * @param commandId 命令ID
     * @param callback 回调接口
     * @param isOneTime 是否为一次性回调
     * @param commandType 命令类型过滤器（可为null表示不过滤）
     */
    public void registerCommandCallback(String commandId, Command.CommandCallback callback, 
                                      boolean isOneTime, String commandType) {
        if (commandId == null || callback == null) {
            YoBTSDKLog.w(TAG, "注册命令回调失败：参数为空");
            return;
        }
        
        CallbackRegistration registration = new CallbackRegistration(callback, isOneTime, commandType);
        
        synchronized (commandCallbacks) {
            List<CallbackRegistration> callbacks = commandCallbacks.computeIfAbsent(commandId, k -> new ArrayList<>());
            callbacks.add(registration);
        }
        
        YoBTSDKLog.d(TAG, String.format("为命令[%s]注册%s回调，类型过滤：%s", 
                     commandId, isOneTime ? "一次性" : "持久", commandType != null ? commandType : "无"));
    }
    
    /**
     * 为命令类型注册回调
     * @param commandType 命令类型
     * @param callback 回调接口
     * @param isOneTime 是否为一次性回调
     */
    public void registerTypeCallback(String commandType, Command.CommandCallback callback, boolean isOneTime) {
        if (commandType == null || callback == null) {
            YoBTSDKLog.w(TAG, "注册类型回调失败：参数为空");
            return;
        }
        
        CallbackRegistration registration = new CallbackRegistration(callback, isOneTime, commandType);
        
        synchronized (typeCallbacks) {
            List<CallbackRegistration> callbacks = typeCallbacks.computeIfAbsent(commandType, k -> new ArrayList<>());
            callbacks.add(registration);
        }
        
        YoBTSDKLog.d(TAG, String.format("为命令类型[%s]注册%s回调", commandType, isOneTime ? "一次性" : "持久"));
    }
    
    /**
     * 注册全局回调（保持向后兼容）
     * @param callback 回调接口
     * @param isOneTime 是否为一次性回调
     */
    public void registerGlobalCallback(Command.CommandCallback callback, boolean isOneTime,String commandType) {
        if (callback == null) {
            YoBTSDKLog.w(TAG, "注册全局回调失败：回调为空");
            return;
        }
        
        CallbackRegistration registration = new CallbackRegistration(callback, isOneTime, commandType);
        
        synchronized (globalCallbacks) {
            globalCallbacks.add(registration);
        }
        
        YoBTSDKLog.d(TAG, String.format("注册%s全局回调", isOneTime ? "一次性" : "持久"));
    }
    
    /**
     * 分发命令完成回调
     * @param command 完成的命令
     * @param code 结果代码
     * @param result 结果信息
     */
    public void dispatchCommandCompleted(Command command, int code, String result) {
        if (command == null) {
            return;
        }
        
        String commandId = command.getCommandId();
        String commandType = command.getClass().getSimpleName();
        
        YoBTSDKLog.d(TAG, String.format("分发命令完成回调 - ID:[%s], 类型:[%s], 结果码:[%s]",
                     commandId, commandType, result));
        
        // 1. 分发命令特定回调
        dispatchCommandSpecificCallbacks(commandId, command, code, result);
        
        // 2. 分发类型特定回调
        dispatchTypeSpecificCallbacks(commandType, command, code, result);
        
        // 3. 分发全局回调
        dispatchGlobalCallbacks(command, code, result);
        
        // 4. 清理已执行的一次性回调
        cleanupExecutedCallbacks(commandId);
    }
    
    /**
     * 分发命令完成回调（带数据）
     */
    public void dispatchCommandCompleted(Command command, int code, Object yobackdata, String result) {
        if (command == null) {
            return;
        }
        
        String commandId = command.getCommandId();
        String commandType = command.getClass().getSimpleName();
        
        // 同样的分发逻辑，但调用带数据的回调方法
        dispatchCommandSpecificCallbacks(commandId, command, code, yobackdata, result);
        dispatchTypeSpecificCallbacks(commandType, command, code, yobackdata, result);
        dispatchGlobalCallbacks(command, code, yobackdata, result);
        cleanupExecutedCallbacks(commandId);
    }
    
    /**
     * 分发命令完成回调（带结果值）
     */
    public void dispatchCommandCompleted(Command command, int code, int resultValue, String result) {
        if (command == null) {
            return;
        }
        
        String commandId = command.getCommandId();
        String commandType = command.getClass().getSimpleName();
        
        // 同样的分发逻辑，但调用带结果值的回调方法
        dispatchCommandSpecificCallbacks(commandId, command, code, resultValue, result);
        dispatchTypeSpecificCallbacks(commandType, command, code, resultValue, result);
        dispatchGlobalCallbacks(command, code, resultValue, result);
        cleanupExecutedCallbacks(commandId);
    }
    
    /**
     * 分发命令特定回调
     */
    private void dispatchCommandSpecificCallbacks(String commandId, Command command, int code, String result) {
        List<CallbackRegistration> callbacks = commandCallbacks.get(commandId);
        if (callbacks != null) {
            synchronized (callbacks) {
                for (CallbackRegistration registration : callbacks) {
                    if (registration.matches(command) && !registration.isExecuted()) {
                        executeCallback(registration, command, code, result);
                    }
                }
            }
        }
    }
    
    private void dispatchCommandSpecificCallbacks(String commandId, Command command, int code, Object yobackdata, String result) {
        List<CallbackRegistration> callbacks = commandCallbacks.get(commandId);
        if (callbacks != null) {
            synchronized (callbacks) {
                for (CallbackRegistration registration : callbacks) {
                    if (registration.matches(command) && !registration.isExecuted()) {
                        executeCallback(registration, command, code, yobackdata, result);
                    }
                }
            }
        }
    }
    
    private void dispatchCommandSpecificCallbacks(String commandId, Command command, int code, int resultValue, String result) {
        List<CallbackRegistration> callbacks = commandCallbacks.get(commandId);
        if (callbacks != null) {
            synchronized (callbacks) {
                for (CallbackRegistration registration : callbacks) {
                    if (registration.matches(command) && !registration.isExecuted()) {
                        executeCallback(registration, command, code, resultValue, result);
                    }
                }
            }
        }
    }
    
    /**
     * 分发类型特定回调
     */
    private void dispatchTypeSpecificCallbacks(String commandType, Command command, int code, String result) {
        List<CallbackRegistration> callbacks = typeCallbacks.get(commandType);
        if (callbacks != null) {
            synchronized (callbacks) {
                for (CallbackRegistration registration : callbacks) {
                    if (!registration.isExecuted()) {
                        executeCallback(registration, command, code, result);
                    }
                }
            }
        }
    }
    
    private void dispatchTypeSpecificCallbacks(String commandType, Command command, int code, Object yobackdata, String result) {
        List<CallbackRegistration> callbacks = typeCallbacks.get(commandType);
        if (callbacks != null) {
            synchronized (callbacks) {
                for (CallbackRegistration registration : callbacks) {
                    if (!registration.isExecuted()) {
                        executeCallback(registration, command, code, yobackdata, result);
                    }
                }
            }
        }
    }
    
    private void dispatchTypeSpecificCallbacks(String commandType, Command command, int code, int resultValue, String result) {
        List<CallbackRegistration> callbacks = typeCallbacks.get(commandType);
        if (callbacks != null) {
            synchronized (callbacks) {
                for (CallbackRegistration registration : callbacks) {
                    if (!registration.isExecuted()) {
                        executeCallback(registration, command, code, resultValue, result);
                    }
                }
            }
        }
    }
    
    /**
     * 分发全局回调
     */
    private void dispatchGlobalCallbacks(Command command, int code, String result) {
        synchronized (globalCallbacks) {
            for (CallbackRegistration registration : globalCallbacks) {
                if (registration.matches(command) && !registration.isExecuted()) {
                    executeCallback(registration, command, code, result);
                }
            }
        }
    }
    
    private void dispatchGlobalCallbacks(Command command, int code, Object yobackdata, String result) {
        synchronized (globalCallbacks) {
            for (CallbackRegistration registration : globalCallbacks) {
                if (registration.matches(command) && !registration.isExecuted()) {
                    executeCallback(registration, command, code, yobackdata, result);
                }
            }
        }
    }
    
    private void dispatchGlobalCallbacks(Command command, int code, int resultValue, String result) {
        synchronized (globalCallbacks) {
            for (CallbackRegistration registration : globalCallbacks) {
                if (registration.matches(command) && !registration.isExecuted()) {
                    executeCallback(registration, command, code, resultValue, result);
                }
            }
        }
    }
    
    /**
     * 安全执行回调
     */
    private void executeCallback(CallbackRegistration registration, Command command, int code, String result) {
        try {
            mainHandler.post(() -> {
                try {
                    registration.getCallback().onCommandCompleted(command, code, result);
                    if (registration.isOneTime()) {
                        registration.markExecuted();
                    }
                } catch (Exception e) {
                    YoBTSDKLog.e(TAG, "执行回调异常: " + e.getMessage(), e);
                }
            });
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "提交回调执行任务异常: " + e.getMessage(), e);
        }
    }
    
    private void executeCallback(CallbackRegistration registration, Command command, int code, Object yobackdata, String result) {
        try {
            mainHandler.post(() -> {
                try {
                    registration.getCallback().onCommandCompleted(command, code, yobackdata, result);
                    if (registration.isOneTime()) {
                        registration.markExecuted();
                    }
                } catch (Exception e) {
                    YoBTSDKLog.e(TAG, "执行回调异常: " + e.getMessage(), e);
                }
            });
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "提交回调执行任务异常: " + e.getMessage(), e);
        }
    }
    
    private void executeCallback(CallbackRegistration registration, Command command, int code, int resultValue, String result) {
        try {
            mainHandler.post(() -> {
                try {
                    registration.getCallback().onCommandCompleted(command, code, resultValue, result);
                    if (registration.isOneTime()) {
                        registration.markExecuted();
                    }
                } catch (Exception e) {
                    YoBTSDKLog.e(TAG, "执行回调异常: " + e.getMessage(), e);
                }
            });
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "提交回调执行任务异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 清理指定命令的已执行回调和无效回调
     */
    private void cleanupExecutedCallbacks(String commandId) {
        int cleanupCount = 0;
        
        try {
            // 清理命令特定回调
            List<CallbackRegistration> commandSpecificCallbacks = commandCallbacks.get(commandId);
            if (commandSpecificCallbacks != null) {
                synchronized (commandSpecificCallbacks) {
                    Iterator<CallbackRegistration> iterator = commandSpecificCallbacks.iterator();
                    while (iterator.hasNext()) {
                        CallbackRegistration registration = iterator.next();
                        
                        // 清理已执行的一次性回调、无效回调或过期回调
                        if ((registration.isOneTime() && registration.isExecuted()) || 
                            registration.isInvalid() || 
                            registration.isExpired()) {
                            iterator.remove();
                            cleanupCount++;
                            YoBTSDKLog.d(TAG, "清理回调: " + commandId + " (类型: " + 
                                       (registration.isOneTime() && registration.isExecuted() ? "已执行" : 
                                        registration.isInvalid() ? "无效" : "过期") + ")");
                        }
                    }
                    
                    // 如果列表为空，移除整个映射项
                    if (commandSpecificCallbacks.isEmpty()) {
                        commandCallbacks.remove(commandId);
                        YoBTSDKLog.d(TAG, "移除空的命令回调映射: " + commandId);
                    }
                }
            }
            
            // 清理全局回调中的无效回调
            synchronized (globalCallbacks) {
                Iterator<CallbackRegistration> iterator = globalCallbacks.iterator();
                while (iterator.hasNext()) {
                    CallbackRegistration registration = iterator.next();
                    
                    // 清理已执行的一次性回调、无效回调或过期回调
                    if ((registration.isOneTime() && registration.isExecuted()) || 
                        registration.isInvalid() || 
                        registration.isExpired()) {
                        iterator.remove();
                        cleanupCount++;
                        YoBTSDKLog.d(TAG, "清理全局回调 (类型: " + 
                                   (registration.isOneTime() && registration.isExecuted() ? "已执行" : 
                                    registration.isInvalid() ? "无效" : "过期") + ")");
                    }
                }
            }
            
            if (cleanupCount > 0) {
                YoBTSDKLog.d(TAG, "命令 " + commandId + " 清理完成，清理了 " + cleanupCount + " 个回调");
            }
            
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "清理回调时发生异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 移除特定命令的所有回调
     * @param commandId 命令ID
     */
    public void removeCommandCallbacks(String commandId) {
        if (commandId != null) {
            commandCallbacks.remove(commandId);
            YoBTSDKLog.d(TAG, "移除命令回调: " + commandId);
        }
    }
    
    /**
     * 移除特定类型的所有回调
     * @param commandType 命令类型
     */
    public void removeTypeCallbacks(String commandType) {
        if (commandType != null) {
            typeCallbacks.remove(commandType);
            YoBTSDKLog.d(TAG, "移除类型回调: " + commandType);
        }
    }
    
    /**
     * 移除特定的全局回调
     * @param callback 要移除的回调
     */
    public void removeGlobalCallback(Command.CommandCallback callback) {
        if (callback != null) {
            synchronized (globalCallbacks) {
                Iterator<CallbackRegistration> iterator = globalCallbacks.iterator();
                while (iterator.hasNext()) {
                    if (iterator.next().getCallback() == callback) {
                        iterator.remove();
                        YoBTSDKLog.d(TAG, "移除全局回调");
                        break;
                    }
                }
            }
        }
    }
    
    /**
     * 清理所有全局回调
     * 用于防止回调污染，特别是在Activity切换时
     */
    public void clearAllGlobalCallbacks() {
        synchronized (globalCallbacks) {
            int count = globalCallbacks.size();
            globalCallbacks.clear();
            YoBTSDKLog.i(TAG, "已清理所有全局回调，共 " + count + " 个");
        }
    }

    /**
     * 启动积极的清理任务
     */
    private void startAggressiveCleanupTask() {
        // 每5秒清理一次过期的回调 - 比原来的5分钟更频繁
        mainHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                performAggressiveCleanup();
                mainHandler.postDelayed(this, 5 * 1000); // 5秒
            }
        }, 5 * 1000);
    }
    
    /**
     * 执行积极的清理任务
     * 比原来的清理更频繁和彻底
     */
    private void performAggressiveCleanup() {
        long currentTime = System.currentTimeMillis();
        int cleanupCount = 0;
        
        try {
            // 清理命令特定回调中的无效回调
            synchronized (commandCallbacks) {
                Iterator<Map.Entry<String, List<CallbackRegistration>>> entryIterator = commandCallbacks.entrySet().iterator();
                while (entryIterator.hasNext()) {
                    Map.Entry<String, List<CallbackRegistration>> entry = entryIterator.next();
                    List<CallbackRegistration> callbacks = entry.getValue();
                    
                    synchronized (callbacks) {
                        Iterator<CallbackRegistration> callbackIterator = callbacks.iterator();
                        while (callbackIterator.hasNext()) {
                            CallbackRegistration registration = callbackIterator.next();
                            
                            // 清理过期、无效或已执行的一次性回调
                            if (registration.isExpired() || 
                                registration.isInvalid() || 
                                (registration.isOneTime() && registration.isExecuted())) {
                                callbackIterator.remove();
                                cleanupCount++;
                                YoBTSDKLog.d(TAG, "清理无效的命令回调，命令ID: " + entry.getKey());
                            }
                        }
                        
                        // 如果列表为空，移除整个映射项
                        if (callbacks.isEmpty()) {
                            entryIterator.remove();
                        }
                    }
                }
            }
            
            // 清理类型特定回调中的无效回调
            synchronized (typeCallbacks) {
                Iterator<Map.Entry<String, List<CallbackRegistration>>> entryIterator = typeCallbacks.entrySet().iterator();
                while (entryIterator.hasNext()) {
                    Map.Entry<String, List<CallbackRegistration>> entry = entryIterator.next();
                    List<CallbackRegistration> callbacks = entry.getValue();
                    
                    synchronized (callbacks) {
                        Iterator<CallbackRegistration> callbackIterator = callbacks.iterator();
                        while (callbackIterator.hasNext()) {
                            CallbackRegistration registration = callbackIterator.next();
                            
                            // 清理过期、无效或已执行的一次性回调
                            if (registration.isExpired() || 
                                registration.isInvalid() || 
                                (registration.isOneTime() && registration.isExecuted())) {
                                callbackIterator.remove();
                                cleanupCount++;
                                YoBTSDKLog.d(TAG, "清理无效的类型回调，类型: " + entry.getKey());
                            }
                        }
                        
                        // 如果列表为空，移除整个映射项
                        if (callbacks.isEmpty()) {
                            entryIterator.remove();
                        }
                    }
                }
            }
            
            // 清理全局回调中的无效回调
            synchronized (globalCallbacks) {
                Iterator<CallbackRegistration> iterator = globalCallbacks.iterator();
                while (iterator.hasNext()) {
                    CallbackRegistration registration = iterator.next();
                    
                    // 清理过期、无效或已执行的一次性回调
                    if (registration.isExpired() || 
                        registration.isInvalid() || 
                        (registration.isOneTime() && registration.isExecuted())) {
                        iterator.remove();
                        cleanupCount++;
                        YoBTSDKLog.d(TAG, "清理无效的全局回调");
                    }
                }
            }
            
            if (cleanupCount > 0) {
                YoBTSDKLog.i(TAG, "积极清理完成，清理了 " + cleanupCount + " 个无效回调");
            }
            
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "执行积极清理时发生异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 释放所有资源
     */
    public void release() {
        commandCallbacks.clear();
        typeCallbacks.clear();
        globalCallbacks.clear();
        mainHandler.removeCallbacksAndMessages(null);
        YoBTSDKLog.i(TAG, "CommandCallbackManager 资源已释放");
    }
} 