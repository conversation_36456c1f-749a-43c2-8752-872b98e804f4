package com.ggec.sppservice.check;

import android.bluetooth.BluetoothA2dp;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothClass;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothManager;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description:
 * 经典蓝牙设备MAC地址检查器
 * 用于检查当前连接的经典蓝牙设备并获取其MAC地址
 * 仅在调用方法时初始化，不使用时不会持续监听
 */
public class YoSPPMacDeviceChecker implements BluetoothProfile.ServiceListener {
    private static final String TAG = "YoSPPMacDeviceChecker";
    private static final int PROFILE_WAIT_TIMEOUT = 2; // 秒
    private static final int MAX_RETRY_COUNT = 0; // 不再重试
    private static final int RETRY_DELAY_MS = 1000;

    private final Context context;
    private final BluetoothManager bluetoothManager;
    private BluetoothA2dp a2dpProfile;
    private CountDownLatch profileLatch = new CountDownLatch(1);
    private final Handler handler = new Handler(Looper.getMainLooper());
    
    // 初始化状态标记
    private final AtomicBoolean initializing = new AtomicBoolean(false);
    private final AtomicBoolean initialized = new AtomicBoolean(false);
    private int retryCount = 0;

    private static volatile YoSPPMacDeviceChecker INSTANCE;

    /**
     * 获取YoSPPMacDeviceChecker实例
     * @param context 应用上下文
     * @return YoSPPMacDeviceChecker实例
     */
    public static YoSPPMacDeviceChecker getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (YoSPPMacDeviceChecker.class) {
                if (INSTANCE == null) {
                    INSTANCE = new YoSPPMacDeviceChecker(context.getApplicationContext());
                }
            }
        }
        return INSTANCE;
    }

    public void init(){
        ensureProfileReady();
    }

    /**
     * 私有构造方法，不再自动初始化
     * @param context 应用上下文
     */
    private YoSPPMacDeviceChecker(Context context) {
        this.context = context.getApplicationContext();
        this.bluetoothManager = (BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE);
        // 不再自动初始化，改为按需初始化
    }
    
    /**
     * 初始化Profile代理
     * 只在需要时才调用此方法
     */
    private void initializeProfileProxy() {
        if (initializing.getAndSet(true)) {
            return; // 已经在初始化过程中
        }
        
        retryCount = 0;
        BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
        if (adapter == null) {
            YoBTSDKLog.e(TAG, "设备不支持蓝牙");
            initializing.set(false);
            return;
        }
        
        // 重置锁，以便重新连接
        if (profileLatch.getCount() == 0) {
            profileLatch = new CountDownLatch(1);
        }
        
        try {
            boolean success = adapter.getProfileProxy(context, this, BluetoothProfile.A2DP);
            if (!success) {
                YoBTSDKLog.e(TAG, "获取A2DP Profile代理失败，不进行重试");
                initializing.set(false);
                // scheduleRetry();
            }
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "获取A2DP Profile代理异常", e);
            initializing.set(false);
            // scheduleRetry();
        }
    }
    
    /**
     * 安排重试
     */
    private void scheduleRetry() {
        if (retryCount < MAX_RETRY_COUNT) {
            retryCount++;
            handler.postDelayed(this::initializeProfileProxy, RETRY_DELAY_MS);
        } else {
            YoBTSDKLog.e(TAG, "获取A2DP Profile代理失败，已达到最大重试次数");
            initializing.set(false);
        }
    }

    @Override
    public void onServiceConnected(int profile, BluetoothProfile proxy) {
        if (profile == BluetoothProfile.A2DP) {
            this.a2dpProfile = (BluetoothA2dp) proxy;
            profileLatch.countDown(); // 释放锁，表示服务已连接
            initializing.set(false);
            initialized.set(true);
            retryCount = 0;
        }
    }

    @Override
    public void onServiceDisconnected(int profile) {
        if (profile == BluetoothProfile.A2DP) {
            this.a2dpProfile = null;
            // 重置锁，以便下次连接
            profileLatch = new CountDownLatch(1);
            initialized.set(false);
            // 不自动重新连接
        }
    }

    /**
     * 等待A2DP Profile就绪
     * @return true表示Profile已就绪，false表示超时
     */
    private boolean waitForA2dpProfile() {
        if (a2dpProfile != null) {
            return true; // 已经就绪
        }
        
        // 如果还没初始化或初始化过程中，尝试再次初始化
        if (!initialized.get() && !initializing.get()) {
            initializeProfileProxy();
        }
        
        try {
            // 等待Profile就绪，最多等待PROFILE_WAIT_TIMEOUT秒
            boolean profileReady = profileLatch.await(PROFILE_WAIT_TIMEOUT, TimeUnit.SECONDS);
            if (!profileReady) {
                YoBTSDKLog.w(TAG, "等待A2DP Profile就绪超时");
                // 尝试重新初始化
                if (!initializing.get()) {
                    // initializeProfileProxy(); // 不再重新初始化
                }
            }
            return profileReady && a2dpProfile != null;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    /**
     * 确保A2DP Profile就绪
     * 这个方法会尝试重试获取Profile
     * @return true表示Profile已就绪，false表示仍然未就绪
     */
    private boolean ensureProfileReady() {
        if (a2dpProfile != null) {
            return true; // 已经就绪
        }
        
        // 尝试等待Profile就绪
        return waitForA2dpProfile();
    }

    /**
     * 检查是否连接了经典蓝牙设备并返回结果
     * @return DeviceConnectionResult 包含连接状态和MAC地址的结果对象
     */
    public DeviceConnectionResult checkConnectedDevice() {
        if (!ensureProfileReady()) {
            return new DeviceConnectionResult(false, null, "A2DP服务未就绪");
        }

        try {
            List<BluetoothDevice> connectedDevices = a2dpProfile.getConnectedDevices();
            if (connectedDevices != null && !connectedDevices.isEmpty()) {
                // 通常只连接一个A2DP设备，取第一个
                BluetoothDevice device = connectedDevices.get(0);
                String macAddress = standardizeMacAddress(device.getAddress());
                return new DeviceConnectionResult(true, macAddress, "已连接");
            }
        } catch (SecurityException e) {
            YoBTSDKLog.e(TAG, "检查设备连接需要蓝牙权限", e);
            return new DeviceConnectionResult(false, null, "缺少蓝牙权限");
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "检查设备连接异常", e);
            return new DeviceConnectionResult(false, null, "检查设备连接异常: " + e.getMessage());
        }
        
        return new DeviceConnectionResult(false, null, "未连接任何A2DP设备");
    }

    /**
     * 检查指定MAC地址的设备是否已通过经典蓝牙连接。
     * 此方法会标准化输入的MAC地址，不区分大小写和分隔符。
     * @param macAddress 要检查的设备MAC地址 (e.g., "AA:BB:CC:11:22:33", "aa-bb-cc-11-22-33")
     * @return 如果设备已连接则返回true，否则返回false
     */
    public boolean isDeviceConnected(String macAddress) {
        if (macAddress == null || macAddress.isEmpty()) {
            YoBTSDKLog.e(TAG, "无效的MAC地址: (null or empty)");
            return false;
        }

        // 标准化输入的MAC地址，以处理不同格式
        String standardizedMac = standardizeMacAddress(macAddress);
        if (!BluetoothAdapter.checkBluetoothAddress(standardizedMac)) {
            YoBTSDKLog.e(TAG, "无效的MAC地址: " + macAddress + " (标准化后: " + standardizedMac + ")");
            return false;
        }

        // 确保A2DP Profile就绪
        if (!ensureProfileReady()) {
            YoBTSDKLog.w(TAG, "A2DP Profile未就绪，无法检查设备连接");
            return false;
        }
        
        try {
            List<BluetoothDevice> connectedDevices = a2dpProfile.getConnectedDevices();
            if (connectedDevices != null) {
                for (BluetoothDevice device : connectedDevices) {
                    // device.getAddress() 返回的是标准大写格式 "XX:XX:XX:XX:XX:XX"
                    // standardizedMac 也是这个格式，所以直接比较
                    if (standardizedMac.equals(device.getAddress())) {
                        return true;
                    }
                }
            }
        } catch (SecurityException e) {
            YoBTSDKLog.e(TAG, "检查设备连接需要蓝牙权限", e);
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "检查设备连接异常", e);
        }

        return false;
    }
    
    /**
     * 标准化MAC地址格式为XX:XX:XX:XX:XX:XX
     * @param macAddress 输入的MAC地址
     * @return 标准化的MAC地址
     */
    private String standardizeMacAddress(String macAddress) {
        if (macAddress == null || macAddress.isEmpty()) {
            return "";
        }
        
        // 1. 转为大写
        // 2. 移除所有空格
        macAddress = macAddress.toUpperCase().replace(" ", "");
        
        // 3. 移除所有分隔符（冒号、横线等）
        macAddress = macAddress.replace(":", "").replace("-", "").replace(".", "");
        
        // 4. 检查MAC地址长度是否为12个字符（6个字节）
        if (macAddress.length() != 12) {
            YoBTSDKLog.w(TAG, "MAC地址格式错误: " + macAddress);
            return macAddress; // 返回原始值，避免处理错误的MAC地址
        }
        
        // 5. 重新格式化为标准格式 XX:XX:XX:XX:XX:XX
        StringBuilder formattedMac = new StringBuilder();
        for (int i = 0; i < macAddress.length(); i++) {
            if (i > 0 && i % 2 == 0) {
                formattedMac.append(':');
            }
            formattedMac.append(macAddress.charAt(i));
        }
        
        return formattedMac.toString();
    }
    
    /**
     * 设备连接结果类
     * 包含是否连接了经典蓝牙设备及其MAC地址
     */
    public static class DeviceConnectionResult {
        private final boolean connected;
        private final String macAddress;
        private final String message;

        public DeviceConnectionResult(boolean connected, String macAddress) {
            this(connected, macAddress, "");
        }

        public DeviceConnectionResult(boolean connected, String macAddress, String message) {
            this.connected = connected;
            this.macAddress = macAddress;
            this.message = message;
        }
        
        /**
         * 是否连接了经典蓝牙设备
         * @return true表示已连接，false表示未连接
         */
        public boolean isConnected() {
            return connected;
        }
        
        /**
         * 获取已连接设备的MAC地址
         * @return 已连接设备的MAC地址，如果未连接则为null
         */
        public String getMacAddress() {
            return macAddress;
        }

        /**
         * 获取结果相关的消息
         * @return 描述信息
         */
        public String getMessage() {
            return message;
        }
    }
    
    /**
     * 释放资源
     */
    public void release() {
        // 移除所有回调
        handler.removeCallbacksAndMessages(null);
        
        // 关闭Profile代理
        if (a2dpProfile != null) {
            try {
                BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
                if (adapter != null) {
                    adapter.closeProfileProxy(BluetoothProfile.A2DP, a2dpProfile);
                }
            } catch (Exception e) {
                // 不再记录日志
            }
            this.a2dpProfile = null;
        }
        
        initialized.set(false);
        initializing.set(false);

        synchronized (YoSPPMacDeviceChecker.class) {
            INSTANCE = null;
        }
    }
} 