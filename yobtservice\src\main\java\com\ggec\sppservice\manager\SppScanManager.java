package com.ggec.sppservice.manager;

import android.content.Context;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import com.ggec.sppservice.scanner.ISppScanner;
import com.ggec.sppservice.scanner.SppScanCallback;
import com.ggec.sppservice.scanner.SppScanConfig;
import com.ggec.sppservice.scanner.impl.SppScannerImpl;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * SPP扫描管理类
 * 单例模式实现，用于管理SPP设备扫描
 */
public class SppScanManager {
    private static final String TAG = "SppScanManager";
    
    private static volatile SppScanManager INSTANCE;
    
    private final ISppScanner scanner;
    private SppScanConfig scanConfig;
    
    /**
     * 获取单例实例
     * @param context 应用上下文
     * @return SppScanManager实例
     */
    public static SppScanManager getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (SppScanManager.class) {
                if (INSTANCE == null) {
                    INSTANCE = new SppScanManager(context.getApplicationContext());
                }
            }
        }
        return INSTANCE;
    }
    
    /**
     * 私有构造方法
     * @param context 应用上下文
     */
    private SppScanManager(Context context) {
        scanner = new SppScannerImpl(context);
        scanConfig = SppScanConfig.createDefault();
        YoBTSDKLog.d(TAG, "SPP扫描管理器已初始化");
    }
    
    /**
     * 设置扫描配置
     * @param config SPP扫描配置
     */
    public void setScanConfig(SppScanConfig config) {
        if (config != null) {
            this.scanConfig = config;
            scanner.setScanConfig(config);
            YoBTSDKLog.d(TAG, "扫描配置已更新");
        }
    }
    
    /**
     * 开始扫描SPP设备
     * @param callback 扫描回调
     */
    public void startScan(final SppScanCallback callback) {
        if (callback == null) {
            YoBTSDKLog.e(TAG, "无法开始扫描，回调不能为空");
            return;
        }
        
        YoBTSDKLog.d(TAG, "开始SPP扫描...");
        scanner.startScan(callback);
    }
    
    /**
     * 停止扫描
     */
    public void stopScan() {
        YoBTSDKLog.d(TAG, "停止SPP扫描");
        scanner.stopScan();
    }
    
    /**
     * 释放资源
     */
    public void release() {
        YoBTSDKLog.d(TAG, "释放资源");
        scanner.close();
        synchronized (SppScanManager.class) {
            INSTANCE = null;
        }
    }
} 