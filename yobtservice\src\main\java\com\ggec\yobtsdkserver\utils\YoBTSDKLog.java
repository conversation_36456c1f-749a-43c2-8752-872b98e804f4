package com.ggec.yobtsdkserver.utils;

import android.content.Context;
import android.os.Environment;
import android.util.Log;

import com.ggec.yobtservice.BuildConfig;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 日志工具类
 * 统一处理日志输出，便于调试和问题排查
 */
public class YoBTSDKLog {
    private static final String DEFAULT_TAG = "BLE_SERVICE";
    // 控制台日志默认始终开启，无需开关
    private static boolean isFileLogEnabled = false; // 控制是否写入文件的开关
    private static final int MAX_LOG_LINES = 10000; // 单个日志文件最大行数
    private static int currentLineCount = 0; // 当前日志文件行数
    private static String currentLogHourMinute; // 当前日志文件名中的小时和分钟部分
    
    private static String logDirPath; // 日志文件夹路径
    private static String logFilePath; // 当天日志文件路径
    private static String packageName; // 应用包名
    private static Context appContext; // 应用上下文
    
    // 定义需要过滤的关键词列表
    private static final List<String> FILTER_KEYWORDS = Arrays.asList(
    );
    
    /**
     * 初始化日志系统，仅设置上下文
     * @param context 应用上下文
     */
    public static void init(Context context) {
        if (appContext == null) {
            packageName = context.getPackageName();
            appContext = context.getApplicationContext();
        }
        FILTER_KEYWORDS.clear();
        if(!BuildConfig.DEBUG) {
            FILTER_KEYWORDS.add("特征值");
            FILTER_KEYWORDS.add("服务");
            FILTER_KEYWORDS.add("数据");
            FILTER_KEYWORDS.add("正在发送命令");
            FILTER_KEYWORDS.add("响应");
            FILTER_KEYWORDS.add("期望");
            FILTER_KEYWORDS.add("uuid");
            FILTER_KEYWORDS.add("UUID");
            FILTER_KEYWORDS.add("蓝牙");
        }
    }

    /**
     * 初始化日志系统
     * @param context 应用上下文
     * @param enableFileLog 是否启用文件日志
     */
    public static void init(Context context, boolean enableFileLog) {
        init(context);
        setFileLogEnabled(enableFileLog);
    }
    
    /**
     * 初始化日志文件夹
     * @param context 应用上下文
     * @return 是否成功创建日志目录
     */
    private static boolean initLogDirectory(Context context) {
        if (!isFileLogEnabled) {
            return false;
        }
        
        try {
            // 直接在应用包名目录下创建日志文件夹
            File externalDir = context.getExternalFilesDir(null);
            if (externalDir != null) {
                // 获取包名目录 (Android/data/com.example.app/)
                File packageDir = externalDir.getParentFile();
                if (packageDir != null && packageDir.exists()) {
                    File logDir = new File(packageDir, "YoBTSDK_LOG");
                    if (!logDir.exists()) {
                        boolean created = logDir.mkdirs();
                        if (!created) {
                            Log.e(DEFAULT_TAG, "无法创建日志目录: " + logDir.getAbsolutePath());
                            return false;
                        }
                    }
                    logDirPath = logDir.getAbsolutePath();
                    return true;
                }
            }
            
            // 外部存储不可用或无法创建，使用内部存储
            File filesDir = context.getFilesDir();
            if (filesDir != null) {
                // 获取包名目录 (data/data/com.example.app/)
                File packageDir = filesDir.getParentFile();
                if (packageDir != null && packageDir.exists()) {
                    File logDir = new File(packageDir, "YoBTSDK_LOG");
                    if (!logDir.exists()) {
                        boolean created = logDir.mkdirs();
                        if (!created) {
                            Log.e(DEFAULT_TAG, "无法创建日志目录: " + logDir.getAbsolutePath());
                            return false;
                        }
                    }
                    logDirPath = logDir.getAbsolutePath();
                    return true;
                }
            }
            
            // 如果上述方法都失败，则回退到使用文件目录
            File fallbackDir = new File(context.getFilesDir(), "YoBTSDK_LOG");
            if (!fallbackDir.exists()) {
                boolean created = fallbackDir.mkdirs();
                if (!created) {
                    Log.e(DEFAULT_TAG, "无法创建日志目录: " + fallbackDir.getAbsolutePath());
                    return false;
                }
            }
            logDirPath = fallbackDir.getAbsolutePath();
            Log.w(DEFAULT_TAG, "无法在应用包名目录下创建日志文件夹，使用备用目录：" + logDirPath);
            return true;
        } catch (Exception e) {
            Log.e(DEFAULT_TAG, "创建日志目录失败", e);
            return false;
        }
    }
    
    /**
     * 检查并根据需要创建新的日志文件（按小时或行数）
     */
    private static void checkAndRollLogFile() {
        if (!isFileLogEnabled || logDirPath == null) {
            return;
        }
        
        Date now = new Date();
        SimpleDateFormat hourFormat = new SimpleDateFormat("yyyy-MM-dd_HH", Locale.getDefault());
        String hourPart = hourFormat.format(now);
        
        boolean needsNewFile = false;
        
        if (!hourPart.equals(getHourPartFromCurrentLog())) {
            // 新的小时开始了
            needsNewFile = true;
            currentLogHourMinute = hourPart;
        } else if (currentLineCount >= MAX_LOG_LINES) {
            // 当前文件已满
            needsNewFile = true;
            
            // "小时加十分钟" 策略：在当前文件名上增加10分钟标记
            if (currentLogHourMinute.length() == "yyyy-MM-dd_HH".length()) {
                currentLogHourMinute += "_10";
            } else {
                try {
                    int lastPartIndex = currentLogHourMinute.lastIndexOf('_');
                    int minutes = Integer.parseInt(currentLogHourMinute.substring(lastPartIndex + 1));
                    minutes = Math.min(minutes + 10, 50); // 10, 20, 30, 40, 50
                    currentLogHourMinute = currentLogHourMinute.substring(0, lastPartIndex + 1) + minutes;
                } catch (NumberFormatException e) {
                    // 如果解析失败，回退到小时格式
                    currentLogHourMinute = hourPart;
                }
            }
        }
        
        if (needsNewFile) {
            logFilePath = logDirPath + File.separator + packageName + "_" + currentLogHourMinute + ".log";
            File logFile = new File(logFilePath);
            if (!logFile.exists()) {
                createNewFileWithHeader();
            } else {
                currentLineCount = 0; // 如果文件已存在，重置行数，继续写入
            }
        }
    }
    
    private static String getHourPartFromCurrentLog() {
        if (currentLogHourMinute == null || currentLogHourMinute.length() < "yyyy-MM-dd_HH".length()) {
            return "";
        }
        return currentLogHourMinute.substring(0, "yyyy-MM-dd_HH".length());
    }
    
    /**
     * 创建日志文件并写入文件头
     */
    private static void createNewFileWithHeader() {
        if (logFilePath == null) return;
        
        File logFile = new File(logFilePath);
        try {
            if (logFile.createNewFile()) {
                currentLineCount = 0; // 新文件，行数清零
                writeHeaderToFile();
            }
        } catch (IOException e) {
            Log.e(DEFAULT_TAG, "创建日志文件失败: " + logFilePath, e);
        }
    }
    
    private static void writeHeaderToFile() {
        // 直接写入，避免调用外层writeToFile导致循环
        try (FileOutputStream fos = new FileOutputStream(logFilePath, true);
             PrintWriter writer = new PrintWriter(fos)) {
            
            String timeStamp = new SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault()).format(new Date());
            
            String[] headers = {
                    "====== YoBTSDK 日志会话开始 ======",
                    "包名: " + packageName,
                    "时间: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(new Date()),
                    "===================================="
            };
            
            for (String header : headers) {
                writer.println(timeStamp + " " + header);
                currentLineCount++;
            }
            writer.flush();
            
        } catch (IOException e) {
            Log.e(DEFAULT_TAG, "写入日志头失败", e);
        }
    }
    
    /**
     * 将日志写入文件
     * @param message 日志信息
     */
    private static synchronized void writeToFile(String message) {
        if (!isFileLogEnabled) {
            return;
        }
        
        // 检查是否需要切换日志文件
        checkAndRollLogFile();
        
        if (logFilePath == null) {
            return;
        }
        
        try (FileOutputStream fos = new FileOutputStream(logFilePath, true);
             PrintWriter writer = new PrintWriter(fos)) {
            
            SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault());
            String timeStamp = timeFormat.format(new Date());
            
            writer.println(timeStamp + " " + message);
            writer.flush();
            currentLineCount++;
        } catch (IOException e) {
            Log.e(DEFAULT_TAG, "写入日志文件失败", e);
        }
    }
    
    /**
     * 设置文件日志开关
     * @param enabled 是否启用文件日志
     */
    public static void setFileLogEnabled(boolean enabled) {
        if (enabled && appContext == null) {
            Log.e(DEFAULT_TAG, "YoBTSDKLog尚未初始化，无法开启文件日志。请先调用YoBTInit.getInstance().init()");
            return;
        }

        isFileLogEnabled = enabled;
        
        if (isFileLogEnabled) {
            // 如果开启文件日志，则初始化日志目录和文件
            if (initLogDirectory(appContext)) {
                checkAndRollLogFile(); // 初始化时检查并创建第一个日志文件
                i("YoBTSDKLog", "日志系统初始化完成，日志文件：" + logFilePath);
            } else {
                isFileLogEnabled = false; // 如果初始化失败，关闭文件日志功能
                Log.e(DEFAULT_TAG, "日志系统初始化失败，文件日志功能已禁用");
            }
        }
    }
    
    /**
     * 获取文件日志开关状态
     * @return 文件日志是否启用
     */
    public static boolean isFileLogEnabled() {
        return isFileLogEnabled;
    }
    
    /**
     * 检查消息是否包含过滤关键词
     * @param message 日志信息
     * @return 是否应该过滤此日志
     */
    private static boolean shouldFilter(String message) {
        if (message == null) {
            return false;
        }
        
        for (String keyword : FILTER_KEYWORDS) {
            if (message.contains(keyword)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 输出调试日志
     * @param tag 标签
     * @param message 日志信息
     */
    public static void d(String tag, String message) {
        if (!shouldFilter(message)) {
            Log.d(tag, message);
            
            if (isFileLogEnabled) {
                writeToFile("D/" + tag + ": " + message);
            }
        }
    }
    
    /**
     * 使用默认标签输出调试日志
     * @param message 日志信息
     */
    public static void d(String message) {
        d(DEFAULT_TAG, message);
    }
    
    /**
     * 输出信息日志
     * @param tag 标签
     * @param message 日志信息
     */
    public static void i(String tag, String message) {
        if (!shouldFilter(message)) {
            Log.i(tag, message);
            
            if (isFileLogEnabled) {
                writeToFile("I/" + tag + ": " + message);
            }
        }
    }
    
    /**
     * 使用默认标签输出信息日志
     * @param message 日志信息
     */
    public static void i(String message) {
        i(DEFAULT_TAG, message);
    }
    
    /**
     * 输出警告日志
     * @param tag 标签
     * @param message 日志信息
     */
    public static void w(String tag, String message) {
        if (!shouldFilter(message)) {
            Log.w(tag, message);
            
            if (isFileLogEnabled) {
                writeToFile("W/" + tag + ": " + message);
            }
        }
    }
    
    /**
     * 使用默认标签输出警告日志
     * @param message 日志信息
     */
    public static void w(String message) {
        w(DEFAULT_TAG, message);
    }
    
    /**
     * 输出错误日志
     * @param tag 标签
     * @param message 日志信息
     */
    public static void e(String tag, String message) {
        if (!shouldFilter(message)) {
            Log.e(tag, message);
            
            if (isFileLogEnabled) {
                writeToFile("E/" + tag + ": " + message);
            }
        }
    }
    
    /**
     * 使用默认标签输出错误日志
     * @param message 日志信息
     */
    public static void e(String message) {
        e(DEFAULT_TAG, message);
    }
    
    /**
     * 输出错误日志（带异常）
     * @param tag 标签
     * @param message 日志信息
     * @param throwable 异常
     */
    public static void e(String tag, String message, Throwable throwable) {
        if (!shouldFilter(message)) {
            Log.e(tag, message, throwable);
            
            if (isFileLogEnabled) {
                writeToFile("E/" + tag + ": " + message);
                writeToFile("异常: " + throwable.toString());
                
                // 写入堆栈跟踪
                StackTraceElement[] stackTrace = throwable.getStackTrace();
                for (StackTraceElement element : stackTrace) {
                    writeToFile("    at " + element.toString());
                }
            }
        }
    }
    
    /**
     * 使用默认标签输出错误日志（带异常）
     * @param message 日志信息
     * @param throwable 异常
     */
    public static void e(String message, Throwable throwable) {
        e(DEFAULT_TAG, message, throwable);
    }
    
    /**
     * 输出错误日志（带异常），并写入文件
     * @param tag 标签
     * @param message 日志信息
     * @param throwable 异常
     */
    private static void writeToFile(String message, Throwable throwable) {
        writeToFile(message);
        writeToFile("异常: " + throwable.toString());
        
        // 写入堆栈跟踪
        StackTraceElement[] stackTrace = throwable.getStackTrace();
        for (StackTraceElement element : stackTrace) {
            writeToFile("    at " + element.toString());
        }
    }
} 