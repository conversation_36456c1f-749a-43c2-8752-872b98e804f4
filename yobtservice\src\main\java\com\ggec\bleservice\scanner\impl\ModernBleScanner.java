package com.ggec.bleservice.scanner.impl;

import static android.bluetooth.le.ScanSettings.SCAN_MODE_BALANCED;
import static android.bluetooth.le.ScanSettings.SCAN_MODE_LOW_LATENCY;

import android.annotation.TargetApi;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.le.BluetoothLeScanner;
import android.bluetooth.le.ScanFilter;
import android.bluetooth.le.ScanResult;
import android.bluetooth.le.ScanSettings;
import android.content.Context;
import android.os.Build;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import com.ggec.bleservice.scanner.ScanCallback;
import com.ggec.bleservice.scanner.ScanConfig;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * Android 5.0+ (API 21+)的BLE扫描器实现
 */
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
public class ModernBleScanner extends BaseScanner {
    private static final String TAG = "ModernBleScanner";
    
    private BluetoothLeScanner mLeScanner;
    private android.bluetooth.le.ScanCallback mLeScanCallback;
    private ScanConfig mScanConfig = ScanConfig.createDefault();
    
    public ModernBleScanner(Context context) {
        super(context);
        
        // 初始化回调
        initScanCallback();
    }

    private final Set<String> mAddresses = new HashSet<>();

    /**
     * 初始化蓝牙LE扫描回调
     */
    private void initScanCallback() {
        mLeScanCallback = new android.bluetooth.le.ScanCallback() {

            @Override
            public void onScanResult(int callbackType, ScanResult result) {
                super.onScanResult(callbackType, result);
                
                if (mScanCallback != null && result != null && result.getDevice() != null) {
                    BluetoothDevice device = result.getDevice();
                    String address = device.getAddress();
                    if (mAddresses.contains(address)) {
                        return;
                    }

                    mAddresses.add(address);

                    int rssi = result.getRssi();
                    android.bluetooth.le.ScanRecord scanRecord = result.getScanRecord();
                    byte[] scanRecordBytes = scanRecord != null ? scanRecord.getBytes() : new byte[0];
                    List<android.os.ParcelUuid> serviceUuids = scanRecord != null ? scanRecord.getServiceUuids() : null;

                    // 通知发现设备
                    mScanCallback.onFound(device, rssi, scanRecordBytes, serviceUuids);
                }
            }

            @Override
            public void onBatchScanResults(List<ScanResult> results) {
                super.onBatchScanResults(results);
                
                if (mScanCallback != null) {
                    for (ScanResult result : results) {
                        if (result != null && result.getDevice() != null) {
                            BluetoothDevice device = result.getDevice();
                            String address = device.getAddress();
                            if (mAddresses.contains(address)) {
                                return;
                            }

                            mAddresses.add(address);
                            int rssi = result.getRssi();
                            android.bluetooth.le.ScanRecord scanRecord = result.getScanRecord();
                            byte[] scanRecordBytes = scanRecord != null ? scanRecord.getBytes() : new byte[0];
                            List<android.os.ParcelUuid> serviceUuids = scanRecord != null ? scanRecord.getServiceUuids() : null;

                            // 通知发现设备
                            mScanCallback.onFound(device, rssi, scanRecordBytes, serviceUuids);
                        }
                    }
                }
            }

            @Override
            public void onScanFailed(int errorCode) {
                super.onScanFailed(errorCode);
                YoBTSDKLog.e(TAG, "蓝牙扫描失败，错误码: " + errorCode);
                stopScan();
                mAddresses.clear();
            }
        };
    }
    
    /**
     * 设置扫描配置
     * @param config 扫描配置
     */
    public void setScanConfig(ScanConfig config) {
        if (config != null) {
            this.mScanConfig = config;
        }
    }
    
    @Override
    public void startScan(ScanCallback callback) {
        super.startScan(callback);
        
        if (!mScanning) {
            return;
        }
        
        try {
            // 获取扫描器
            if (mLeScanner == null && mBluetoothAdapter != null) {
                mLeScanner = mBluetoothAdapter.getBluetoothLeScanner();
            }
            
            if (mLeScanner == null) {
                YoBTSDKLog.e(TAG, "BluetoothLeScanner不可用");
                stopScan();
                return;
            }
            // 构建扫描设置
            ScanSettings.Builder builder = new ScanSettings.Builder()
                    .setScanMode(mScanConfig.getScanMode());

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                builder.setLegacy(true);
                builder.setScanMode(SCAN_MODE_BALANCED);
                builder.setPhy(ScanSettings.PHY_LE_ALL_SUPPORTED);
                builder.setNumOfMatches(ScanSettings.MATCH_NUM_MAX_ADVERTISEMENT);
                builder.setCallbackType(ScanSettings.CALLBACK_TYPE_ALL_MATCHES);
                builder.setMatchMode(ScanSettings.MATCH_MODE_AGGRESSIVE);
            }

            // 构建过滤器列表
            List<ScanFilter> filters = new ArrayList<>();
            
            // 如果配置了服务UUID过滤，添加到过滤器
            if (mScanConfig.getServiceUuids() != null && !mScanConfig.getServiceUuids().isEmpty()) {
                for (android.os.ParcelUuid uuid : mScanConfig.getServiceUuids()) {
                    ScanFilter filter = new ScanFilter.Builder()
                            .setServiceUuid(uuid)
                            .build();
                    filters.add(filter);
                }
            }
            mAddresses.clear();
            // 开始扫描
            mLeScanner.startScan(filters.isEmpty() ? null : filters, builder.build(), mLeScanCallback);
            
            // 设置超时
            setScanTimeout(mScanConfig.getScanTimeout());
            
        } catch (SecurityException e) {
            YoBTSDKLog.e(TAG, "缺少必要权限: " + e.getMessage());
            stopScan();
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "扫描失败: " + e.getMessage());
            stopScan();
        }
    }
    
    @Override
    public void stopScan() {
        if (mScanning && mLeScanner != null && mBluetoothAdapter != null && mBluetoothAdapter.isEnabled()) {
            try {
                mLeScanner.stopScan(mLeScanCallback);
            } catch (SecurityException e) {
                YoBTSDKLog.e(TAG, "停止扫描权限错误: " + e.getMessage());
            } catch (Exception e) {
                YoBTSDKLog.e(TAG, "停止扫描错误: " + e.getMessage());
            }
        }
        
        super.stopScan();
    }
    
    @Override
    public void close() {
        stopScan();
        mLeScanner = null;
        super.close();
    }
} 