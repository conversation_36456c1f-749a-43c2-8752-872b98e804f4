plugins {
    //id 'com.android.library'
    alias(libs.plugins.android.library)
}

android {
    namespace 'com.ggec.yotasdk'
    compileSdk 33
    defaultConfig {
        minSdkVersion 23
        versionCode 10001
        versionName "1.0.0.1"
        targetSdk 33
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"

        // 设置ndk编译的cpu架构
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8 
        targetCompatibility JavaVersion.VERSION_1_8
    }
    sourceSets {
        main {
            jniLibs.srcDir 'libs'
            jni.srcDirs = []
        }
    }

//    ndkVersion '21.1.6352462'
//
//    //设置CMakeLists文件的位置
//    externalNativeBuild {
//        cmake {
//            path "CMakeLists.txt"
//        }
//    }

//    sourceSets {
//        main {
//            jniLibs.srcDirs = ['jniLibs']
//        }
//    }

    lintOptions {
        abortOnError false
    }
    
    // 将发布任务配置为生成AAR
    publishing {
        singleVariant("release") {
            withSourcesJar()
            withJavadocJar()
        }
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.2.1'
}

// 生成基于小时的唯一标识符
def generateUniqueId() {
    def date = new Date()
    // 获取小时作为前两位
    def hour = date.format('HH')
    
    // 获取当前小时的递增序号的文件路径
    def counterFile = file("${rootProject.projectDir}/.hour_counter_ota")
    def currentHour = hour
    def counter = 1
    
    // 如果文件存在，读取上次的小时和计数
    if (counterFile.exists()) {
        def lines = counterFile.readLines()
        if (lines.size() >= 2) {
            def lastHour = lines[0].trim()
            def lastCounter = lines[1].trim().toInteger()
            
            // 如果小时相同，递增计数
            if (lastHour == currentHour) {
                counter = lastCounter + 1
                // 跳过数字4
                if (counter == 4) counter = 5
                if (counter > 9) counter = 1  // 超过9则重置为1
            }
        }
    }
    
    // 保存当前小时和计数到文件
    counterFile.text = "${currentHour}\n${counter}"
    
    // 返回3位数的唯一标识：小时(2位) + 递增数(1位)
    return "${hour}${counter}"
}

// 配置发布AAR到指定目录的task
tasks.register('exportReleaseAar', Copy) {
    dependsOn("assembleRelease")
    from("${buildDir}/outputs/aar")
    include("yota-release.aar")
    
    // 获取当前日期的月和日
    def date = new Date()
    def formattedDate = date.format('MMdd')
    
    // 生成基于小时的唯一标识符
    def uniqueId = generateUniqueId()
    
    // 目标目录
    def targetDir = "${rootProject.projectDir}/releases"
    into(targetDir)
    
    // 获取版本名
    def versionName = android.defaultConfig.versionName
    
    // 重命名AAR文件
    rename { String fileName ->
        "HS01OTASDK-V${versionName}-${formattedDate}-${uniqueId}-Android.aar"
    }
    
    doLast {
        println "=== AAR包已导出 ==="
        println "位置: ${targetDir}"
        println "文件名: HS01OTASDK-V${versionName}-${formattedDate}-${uniqueId}-Android.aar"
    }
    
    // 添加inputs和outputs的设置，让Gradle每次都执行此任务
    inputs.property("timestamp", System.currentTimeMillis())
    outputs.upToDateWhen { false }
}