package com.ggec.yobtsdkserver.sdkmanager;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 蓝牙权限管理类
 * 专门负责蓝牙相关权限的检查和请求
 */
public class BTPermissionManager {
    // 权限请求码
    public static final int REQUEST_BLUETOOTH_PERMISSIONS = 1001;

    // 权限常量
    public static final String BLUETOOTH_SCAN = "android.permission.BLUETOOTH_SCAN";
    public static final String BLUETOOTH_CONNECT = "android.permission.BLUETOOTH_CONNECT";
    public static final String ACCESS_COARSE_LOCATION = Manifest.permission.ACCESS_COARSE_LOCATION;

    // Android 12 SDK版本号
    private static final int ANDROID_12_SDK = 31;

    private BTPermissionManager() {
        // 私有构造函数，防止实例化
    }

    /**
     * 检查并请求蓝牙权限
     * @param activity Activity实例
     * @return 如果权限已全部授予返回true
     */
    public static boolean checkAndRequestPermissions(Activity activity) {
        List<String> permissionsNeeded = getRequiredPermissions(activity);

        if (!permissionsNeeded.isEmpty()) {
            ActivityCompat.requestPermissions(activity,
                    permissionsNeeded.toArray(new String[0]),
                    REQUEST_BLUETOOTH_PERMISSIONS);
            return false;
        }

        return true;
    }

    /**
     * 检查是否所有蓝牙权限都已授予
     * @param context 上下文
     * @return 如果所有权限已授予返回true
     */
    public static boolean hasBluetoothPermissions(Context context) {
        return getRequiredPermissions(context).isEmpty();
    }

    /**
     * 获取需要请求的权限列表
     * @param context 上下文
     * @return 需要请求的权限列表
     */
    private static List<String> getRequiredPermissions(Context context) {
        List<String> permissionsNeeded = new ArrayList<>();

        // 基础位置权限
        if (ContextCompat.checkSelfPermission(context, ACCESS_COARSE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {
            permissionsNeeded.add(ACCESS_COARSE_LOCATION);
        }

        // Android 12+特殊权限
        if (Build.VERSION.SDK_INT >= ANDROID_12_SDK) {
            if (ContextCompat.checkSelfPermission(context, BLUETOOTH_SCAN)
                    != PackageManager.PERMISSION_GRANTED) {
                permissionsNeeded.add(BLUETOOTH_SCAN);
            }
            if (ContextCompat.checkSelfPermission(context, BLUETOOTH_CONNECT)
                    != PackageManager.PERMISSION_GRANTED) {
                permissionsNeeded.add(BLUETOOTH_CONNECT);
            }
        }

        return permissionsNeeded;
    }

    /**
     * 处理权限请求结果
     *
     * @param permissions 权限数组
     * @param grantResults 授权结果数组
     * @return 如果所有请求的权限都被授予则返回true
     */
    public static boolean handlePermissionResult(String[] permissions, int[] grantResults) {
        if (permissions == null || grantResults == null || permissions.length != grantResults.length) {
            return false;
        }

        // 检查所有权限是否都被授予
        for (int result : grantResults) {
            if (result != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }

        return true;
    }
} 