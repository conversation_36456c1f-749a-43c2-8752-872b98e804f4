package com.ggec.bleservice.setting.controlcmd;

import com.ggec.bleservice.setting.Command;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 重置耳机触控设置命令
 * 负责将耳机所有触控功能重置为出厂默认设置
 */
public class ResetControlCommand extends Command {
    private static final String TAG = "ResetControlCommand";
    
    // 命令前缀，用于确认是重置控制命令
    private static final String COMMAND_PREFIX = "99EC860001";
    
    // 确认重置命令
    private static final String COMMAND_CONFIRM = "99EC86000100";
    
    // 命令校验和后缀
    private static final String COMMAND_SUFFIX = "1234";
    
    // 是否确认重置
    private final boolean isConfirm;
    
    /**
     * 构造方法
     * @param isConfirm 是否确认重置（true: 确认, false: 取消）
     */
    public ResetControlCommand(boolean isConfirm) {
        super();
        this.isConfirm = isConfirm;
        // 设置命令前缀
        setCommandPrefix("99EC86");
    }
    
    @Override
    public String getCommandData() {
        // 新协议只需支持确认重置，取消无需数据收发
        if (!isConfirm) {
            return null; // 如果是取消，则不发送命令
        }
        return COMMAND_CONFIRM + COMMAND_SUFFIX;
    }
    
    @Override
    public String parseResponse(String responseData) {
        YoBTSDKLog.d(TAG, "解析响应数据: " + responseData);
        
        if (!isConfirm) {
            // 取消无需指令收发，直接返回成功
            notifyCompletion(ResultCode.SUCCESS, 0, "取消恢复默认控制设置成功");
            return "取消恢复默认控制设置成功";
        }
        
        if (responseData == null || responseData.length() < 12) {
            notifyCompletion(ResultCode.FAILED, 0, "响应数据格式错误");
            return "响应数据格式错误";
        }
        
        // 获取响应命令前缀
        String responsePrefix = responseData.substring(0, 12); // 取前12个字符（6个字节）
        String expectedPrefix = COMMAND_CONFIRM.substring(0, 12);
        
        // 判断前缀是否匹配
        boolean isSuccess = responsePrefix.equals(expectedPrefix);
        
        // 对于重置命令，resultValue表示是否成功，1表示成功，0表示失败
        int resultValue = isSuccess ? 1 : 0;
        
        String result = isSuccess ? "恢复默认控制设置成功" : "恢复默认控制设置失败";
        
        // 通知命令完成（使用带结果值的回调）
        notifyCompletion(isSuccess ? ResultCode.SUCCESS : ResultCode.FAILED, resultValue, result);
        
        return result;
    }
    
    @Override
    public boolean isResponseMatch(String responseData) {
        // 取消无需指令收发
        if (!isConfirm) {
            return false;
        }
        
        // 检查响应前缀是否匹配
        if (responseData != null && responseData.length() >= 10) {
            String prefix = responseData.substring(0, 10);
            return COMMAND_PREFIX.equals(prefix);
        }
        return false;
    }
} 