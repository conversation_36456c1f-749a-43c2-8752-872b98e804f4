package com.bes.sdk.message;

import com.bes.sdk.utils.MessageID;

/**
 * Message to describe OTA info.
 */
public class OTAInfoMessage extends BaseMessage
{
    private OTAInfo otaInfo;

    public OTAInfoMessage(OTAInfo otaInfo) {
        this.otaInfo = otaInfo;
    }

    @Override
    public MessageID getMsgID() {
        return MessageID.OTA_STATUS;
    }

    @Override
    public OTAInfo getMsgContent() {
        return otaInfo;
    }
}
