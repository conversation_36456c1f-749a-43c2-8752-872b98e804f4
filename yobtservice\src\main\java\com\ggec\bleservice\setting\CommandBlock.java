package com.ggec.bleservice.setting;

import java.util.Map;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-29
 * Description: 
 * 代表一个应按顺序原子性执行的命令块。
 * 此类是抽象的，应被扩展以实现特定的命令序列。
 */
public abstract class CommandBlock implements IQueueable {

    /**
     * 获取命令块中下一个要执行的命令。
     *
     * @param previousCommand 刚执行完的命令，如果是命令块中的第一个命令，则为 null。
     * @return 下一个要执行的命令，如果命令块已完成，则为 null。
     */
    public abstract Command nextCommand(Command previousCommand);

    /**
     * 当整个命令块成功完成时调用。
     * 实现应处理最终结果。
     */
    public abstract void onBlockCompleted(Map<String, Object> results);


    /**
     * 当命令块中的任何命令失败时调用。
     *
     * @param code 失败命令的错误代码。
     * @param message 错误信息。
     */
    public abstract void onBlockFailed(int code, String message);
    
    /**
     * 获取命令块中命令之间的执行间隔（毫秒）
     * 子类可以重写此方法来指定自定义的间隔时间
     * @return 间隔时间，默认为100ms（比标准200ms快2倍，但保证稳定性）
     */
    public long getCommandInterval() {
        return 100; // 保守优化，平衡性能和稳定性
    }
    
    /**
     * 获取命令块完成后到下一个队列项的间隔（毫秒）
     * 子类可以重写此方法来指定自定义的间隔时间
     * @return 间隔时间，默认为标准间隔
     */
    public long getBlockCompletionInterval() {
        return 200; // 适中的完成间隔
    }
} 