package com.bes.bessdk.connect;

import android.bluetooth.BluetoothDevice;
import android.content.Context;

import com.bes.bessdk.BesSdkConstants.BesConnectState;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.sdk.connect.DeviceConnector;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;

public class BTService {

    protected static Object mOtaLock = new Object();

    public static byte[] curSendData = new byte[]{};

    public static boolean sendData(Context context, DeviceProtocol deviceProtocol, byte[] data, BluetoothDevice device) {
        if (context == null) {
            return false;
        }
        curSendData = data;
        synchronized (mOtaLock) {
            if (deviceProtocol == DeviceProtocol.PROTOCOL_BLE || deviceProtocol == DeviceProtocol.PROTOCOL_GATT_BR_EDR) {
                return BleConnector.getsConnector(context, null, null).write(data, device);
            } else if (deviceProtocol == DeviceProtocol.PROTOCOL_SPP) {
                return SppConnector.getsConnector(context, null).write(data, device);
            } else if (deviceProtocol == DeviceProtocol.PROTOCOL_USB) {
                return UsbConnector.getsConnector(context, null).write(data);
            }
        }
        return false;
    }

    public static boolean sendDataWithoutResponse(Context context, DeviceProtocol deviceProtocol, byte[] data, BluetoothDevice device) {
        if (context == null) {
            return false;
        }
        curSendData = data;
        synchronized (mOtaLock) {
            if (deviceProtocol == DeviceProtocol.PROTOCOL_BLE || deviceProtocol == DeviceProtocol.PROTOCOL_GATT_BR_EDR) {
                return BleConnector.getsConnector(context, null, null).writeWithoutResponse(data, device);
            } else if (deviceProtocol == DeviceProtocol.PROTOCOL_SPP) {
                return SppConnector.getsConnector(context, null).write(data, device);
            } else if (deviceProtocol == DeviceProtocol.PROTOCOL_USB) {
                return UsbConnector.getsConnector(context, null).writeWithoutResponse(data);
            }
        }
        return false;
    }

    public static boolean sendSppData(Context context, byte[] data) {
        if (context == null) {
            return false;
        }
        curSendData = data;
        synchronized (mOtaLock) {
            return SppConnector.getsConnector(context, null).write(data, null);
        }
    }

    public static void refreshConnectListener(Context context, DeviceProtocol deviceProtocol, DeviceConnector.ConnectionListener connectionListener, BluetoothDevice device) {
        synchronized (mOtaLock) {
            if (deviceProtocol == DeviceProtocol.PROTOCOL_BLE || deviceProtocol == DeviceProtocol.PROTOCOL_GATT_BR_EDR) {
                BleConnector.getsConnector(context, null, null).refreshConnectListener(connectionListener, device);
            } else if (deviceProtocol == DeviceProtocol.PROTOCOL_SPP) {
                SppConnector.getsConnector(context, null).refreshConnectListener(connectionListener, device);
            }
        }
    }

    public static void disconnect(HmDevice hmDevice) {
        synchronized (mOtaLock) {
            if (hmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE || hmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_GATT_BR_EDR) {
                BleConnector.getsConnector(null, null, null).disconnect(hmDevice);
            } else if (hmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_SPP) {
                SppConnector.getsConnector(null, null).disconnect(hmDevice);
            }
        }
    }

    public static BesConnectState getDeviceConnectState(Context context, BesServiceConfig serviceConfig) {
        synchronized (mOtaLock) {
            if (serviceConfig.getDeviceProtocol() == DeviceProtocol.PROTOCOL_BLE || serviceConfig.getDeviceProtocol() == DeviceProtocol.PROTOCOL_GATT_BR_EDR) {
                return BleConnector.getsConnector(context, null, null).getDeviceConnectState(serviceConfig);
            } else if (serviceConfig.getDeviceProtocol() == DeviceProtocol.PROTOCOL_SPP) {
                return SppConnector.getsConnector(context, null).getDeviceConnectState(serviceConfig);
            }
        }
        return BesConnectState.BES_CONFIG_ERROR;
    }

    public static byte[] getTotaAesKey(Context context, DeviceProtocol deviceProtocol, BluetoothDevice device) {
        synchronized (mOtaLock) {
            if (deviceProtocol == DeviceProtocol.PROTOCOL_BLE || deviceProtocol == DeviceProtocol.PROTOCOL_GATT_BR_EDR) {
                return BleConnector.getsConnector(context, null, null).getTotaAesKey(device);
            } else if (deviceProtocol == DeviceProtocol.PROTOCOL_SPP) {
                return SppConnector.getsConnector(context, null).getTotaAesKey(device);
            }
        }
        return new byte[0];
    }

    public static void saveTotaAesKey(Context context, DeviceProtocol deviceProtocol, byte[] data, BluetoothDevice device) {
        synchronized (mOtaLock) {
            if (deviceProtocol == DeviceProtocol.PROTOCOL_BLE || deviceProtocol == DeviceProtocol.PROTOCOL_GATT_BR_EDR) {
                BleConnector.getsConnector(context, null, null).saveTotaAesKey(data, device);
            } else if (deviceProtocol == DeviceProtocol.PROTOCOL_SPP) {
                SppConnector.getsConnector(context, null).saveTotaAesKey(data, device);
            }
        }
    }

    public static byte[] getCurSendData() {
        return curSendData;
    }
}
