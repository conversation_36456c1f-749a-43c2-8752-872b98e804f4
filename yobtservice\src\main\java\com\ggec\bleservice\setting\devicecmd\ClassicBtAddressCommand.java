package com.ggec.bleservice.setting.devicecmd;

import com.ggec.bleservice.setting.Command;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 获取经典蓝牙MAC地址命令
 * 负责从耳机获取经典蓝牙（SPP）的MAC地址信息
 */
public class ClassicBtAddressCommand extends Command {
    private static final String TAG = "ClassicBtAddressCommand";
    
    // 命令前缀，用于确认是获取经典蓝牙MAC地址命令
    private static final String COMMAND_PREFIX = "99EC9700";
    
    // 完整的发送命令
    private static final String COMMAND = "99EC97000100";
    
    // 命令校验和后缀
    private static final String COMMAND_SUFFIX = "1234";
    
    private String parsedMacAddress;

    /**
     * 构造方法
     */
    public ClassicBtAddressCommand() {
        super();
        // 设置命令前缀
        setCommandPrefix("99EC97");
    }
    
    @Override
    public String getCommandData() {
        return COMMAND + COMMAND_SUFFIX;
    }
    
    @Override
    public String parseResponse(String responseData) {
        YoBTSDKLog.d(TAG, "解析响应数据: " + responseData);
        
        if (responseData == null || responseData.length() < 20) {
            String errorMsg = "响应数据格式错误";
            notifyCompletion(ResultCode.FAILED, null, errorMsg);
            return errorMsg;
        }
        
        try {
            // 提取MAC地址部分（6个字节，12个16进制字符）
            String macHex = responseData.substring(10, 22);
            
            // 将MAC地址反向并转换为标准格式 XX:XX:XX:XX:XX:XX
            // 反向解析: 将字节顺序反转 (每2个十六进制字符表示一个字节)
            StringBuilder macAddress = new StringBuilder();
            for (int i = macHex.length() - 2; i >= 0; i -= 2) {
                if (macAddress.length() > 0) {
                    macAddress.append(":");
                }
                macAddress.append(macHex.substring(i, i + 2).toUpperCase());
            }
            
            String macAddressStr = macAddress.toString();
            this.parsedMacAddress = macAddressStr;
            String message = "获取经典蓝牙MAC地址成功";
            notifyCompletion(ResultCode.SUCCESS, macAddressStr, message);
            return message;
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "解析MAC地址失败", e);
            String errorMsg = "解析MAC地址失败";
            notifyCompletion(ResultCode.FAILED, null, errorMsg);
            return errorMsg;
        }
    }
    
    @Override
    public boolean isResponseMatch(String responseData) {
        // 检查响应前缀是否匹配
        if (responseData != null && responseData.length() >= 8) {
            String prefix = responseData.substring(0, 8);
            return COMMAND_PREFIX.equals(prefix);
        }
        return false;
    }

    public String getParsedMacAddress() {
        return parsedMacAddress;
    }
} 