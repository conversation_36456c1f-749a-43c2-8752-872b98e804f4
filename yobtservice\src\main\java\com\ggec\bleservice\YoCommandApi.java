package com.ggec.bleservice;

import android.bluetooth.BluetoothDevice;
import android.util.Log;

import com.ggec.bleservice.setting.CommandManager;
import com.ggec.bleservice.setting.CommandQueueCenter;
import com.ggec.bleservice.setting.CommandCallbackManager;
import com.ggec.bleservice.setting.TypedCommandCallback;
import com.ggec.bleservice.setting.batterycmd.BatteryInfoListener;
import com.ggec.bleservice.setting.controlcmd.ControlCommand;
import com.ggec.bleservice.setting.controlcmd.ResetControlCommand;
import com.ggec.bleservice.setting.devicecmd.ClassicBtAddressCommand;
import com.ggec.bleservice.setting.devicecmd.ClearPairingCommand;
import com.ggec.bleservice.setting.devicecmd.DeviceNameGetCommand;
import com.ggec.bleservice.setting.devicecmd.DeviceRenameCommand;
import com.ggec.bleservice.setting.devicecmd.EarColorCommand;
import com.ggec.bleservice.setting.devicecmd.FirmwareVersionCommand;
import com.ggec.bleservice.setting.devicecmd.GlobalStatusCommand;
import com.ggec.bleservice.setting.aggregatorcmd.AboutDeviceStatusAggregator;
import com.ggec.bleservice.setting.aggregatorcmd.HomePageStatusAggregator;
import com.ggec.bleservice.setting.functioncmd.EarLocationCommand;
import com.ggec.bleservice.setting.functioncmd.FallAlertCommand;
import com.ggec.bleservice.setting.functioncmd.FindAlertLeftCommand;
import com.ggec.bleservice.setting.functioncmd.FindAlertRightCommand;
import com.ggec.bleservice.setting.functioncmd.GameModeCommand;
import com.ggec.bleservice.setting.functioncmd.VoiceWakeupCommand;
import com.ggec.bleservice.setting.functioncmd.WearDetectionCommand;
import com.ggec.bleservice.setting.musiccmd.EQModeCommand;
import com.ggec.bleservice.setting.musiccmd.SoundQualityModeCommand;
import com.ggec.bleservice.setting.musiccmd.VolumeAdaptiveCommand;
import com.ggec.bleservice.setting.musiccmd.VolumeRemindCommand;

import java.util.HashMap;
import java.util.Map;
import java.util.LinkedHashMap;

/**
 * 命令API服务类
 * 为应用提供统一的命令接口，隐藏内部实现细节
 * 
 * 注意：此类是SDK的公开API之一，不会被混淆。
 * SDK内部实现类会被混淆以保护代码。
 */
public class YoCommandApi {
    private static final String TAG = "YoCommandApi";
    
    private static YoCommandApi instance;
    
    // 统一命令管理器
    private final CommandManager commandManager;
    
    // 命令队列中心
    private final CommandQueueCenter commandQueueCenter;
    
    // 耳机侧常量
    public static final int EAR_LEFT = 1;
    public static final int EAR_RIGHT = 2;

    // 点击类型常量
    public static final int TAP_TWICE = 1;
    public static final int TAP_TRIPLE = 2;
    
    // EQ模式常量
    public static final int EQ_MUSIC_MODE = 0;
    public static final int EQ_MOVIE_MODE = 1;
    public static final int EQ_NEWS_MODE = 2;
    public static final int EQ_ELDERLY_MODE = 3;
    
    // 耳机在充电盒内状态常量
    public static final int EARS_BOTH_OUT = 0;  // 两只耳机都不在盒内
    public static final int EAR_LEFT_IN = 1;    // 左耳在盒内，右耳不在
    public static final int EAR_RIGHT_IN = 2;   // 右耳在盒内，左耳不在
    public static final int EARS_BOTH_IN = 3;   // 两只耳机都在盒内

    /**
     * 命令结果状态码
     */
    public static final class CommandResultCode {
        public static final int SUCCESS = 0;    // 成功
        public static final int TIMEOUT = 1;    // 超时
        public static final int RESERVED = 2;   // 预留
        public static final int FAILED = 3;     // 失败
    }

    /**
     * 命令执行结果回调接口
     */
    public interface CommandResultCallback {
        /**
         * 命令执行完成回调
         * @param code 结果状态码 (参考 CommandResultCode)
         * @param resultValue 结果值，表示具体的命令完成状态，通常为0表示关闭，1表示开启
         */
        void onCommandResult(int code, int resultValue);
    }
    
    /**
     * 电池电量信息回调接口
     */
    public interface BatteryInfoCallback {
        /**
         * 电池电量更新回调
         * @param code 结果状态码
         * @param leftLevel 左耳电量（百分比）
         * @param rightLevel 右耳电量（百分比）
         * @param caseLevel 充电仓电量（百分比）
         */
        void onBatteryInfoUpdated(int code, String leftLevel, String rightLevel, String caseLevel);
    }
    
    /**
     * 充电盒充电状态回调接口
     */
    public interface CaseChargingStatusCallback {
        /**
         * 充电盒充电状态更新回调
         * @param code 结果状态码
         * @param isCharging 是否正在充电
         */
        void onCaseChargingStatusUpdated(int code, boolean isCharging);
    }
    
    /**
     * 耳机在充电盒内状态回调接口
     */
    public interface EarInCaseStatusCallback {
        /**
         * 耳机在充电盒内状态更新回调
         * @param code 结果状态码
         * @param status 状态值
         *               0 - 两只耳机都不在盒内
         *               1 - 左耳在盒内，右耳不在
         *               2 - 右耳在盒内，左耳不在
         *               3 - 两只耳机都在盒内
         * @param leftInCase 左耳是否在充电盒内
         * @param rightInCase 右耳是否在充电盒内
         */
        void onEarInCaseStatusUpdated(int code, int status, boolean leftInCase, boolean rightInCase);
    }
    
    /**
     * 电池状态变化回调接口
     * 当任何电池信息（电量、充电状态）发生变化时调用
     */
    public interface BatteryStateCallback {
        /**
         * 电池状态已更新
         * 调用此回调后，可以通过 getLeftBatteryLevel(), getRightBatteryLevel(),
         * getCaseBatteryLevel(), isCaseCharging() 等方法获取最新状态
         */
        void onBatteryStateChanged();
    }

    /**
     * EQ模式变化回调接口
     * 发生变化时调用
     */
    public interface EQModeCallback {
        /**
         * EQ模式已更新
         * @param mode 新的EQ模式
         *             0 - 音乐模式
         *             1 - 电影模式
         *             2 - 新闻模式
         *             3 - 老年模式
         */
        void listenEQMode(int mode);
    }

    /**
     * 游戏模式变化回调接口
     * 发生变化时调用
     */
    public interface GameModeCallback {
        /**
         * 游戏模式已更新
         * @param enabled true表示游戏模式已启用，false表示游戏模式已禁用
         */
        void listenGameMode(boolean enabled);
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized YoCommandApi getInstance() {
        if (instance == null) {
            instance = new YoCommandApi();
        }
        return instance;
    }
    
    /**
     * 私有构造方法
     */
    private YoCommandApi() {
        // 获取统一命令管理器实例
        commandManager = CommandManager.getInstance();
        
        // 获取命令队列中心实例
        commandQueueCenter = CommandQueueCenter.getInstance();
    }
    
    /**
     * 释放资源
     */
    public void release() {
        // 释放命令队列中心资源
        commandQueueCenter.release();
        
        // 释放命令管理器资源
        commandManager.release();
        
        instance = null;
    }

    /**
     * 设置设备是否就绪
     * 当设备连接成功并且服务发现完成后，设置为true
     * 当设备断开连接时，设置为false
     * @param isReady 设备是否就绪
     */
    public void setDeviceReady(boolean isReady) {
        CommandQueueCenter.setDeviceReady(isReady);
    }
    
    //=========== 音乐设置相关命令 ===========
    
    /**
     * 设置音量自适应开关
     * @param enabled 是否启用
     * @param callback 结果回调
     */
    public void setVolumeAdaptive(boolean enabled, final CommandResultCallback callback) {
        if (callback != null) {
            commandManager.setCommandResultValueCallback((code, resultValue, result) -> 
                callback.onCommandResult(code, resultValue), VolumeAdaptiveCommand.class.getSimpleName());
        }
        
        commandManager.setVolumeAdaptive(enabled);
    }
    
    /**
     * 设置音质模式
     * @param isHighQuality true表示高音质模式，false表示高续航模式
     * @param callback 结果回调
     */
    public void setSoundQualityMode(boolean isHighQuality, final CommandResultCallback callback) {
        if (callback != null) {
            commandManager.setCommandResultValueCallback((code, resultValue, result) -> 
                callback.onCommandResult(code, resultValue), SoundQualityModeCommand.class.getSimpleName());
        }
        
        commandManager.setSoundQualityMode(isHighQuality);
    }
    
    /**
     * 设置EQ模式
     * @param mode EQ模式
     *             0 - 音乐模式
     *             1 - 电影模式
     *             2 - 新闻模式
     *             3 - 老年人模式
     * @param callback 结果回调
     */
    public void setEQMode(int mode, final CommandResultCallback callback) {
        if (callback != null) {
            commandManager.setCommandResultValueCallback((code, resultValue, result) -> 
                callback.onCommandResult(code, resultValue), EQModeCommand.class.getSimpleName());
        }
        
        commandManager.setEQMode(mode);
    }
    
    /**
     * 设置音量提醒开关
     * @param enabled 是否启用
     * @param callback 结果回调
     */
    public void setVolumeRemind(boolean enabled, final CommandResultCallback callback) {
        if (callback != null) {
            commandManager.setCommandResultValueCallback((code, resultValue, result) -> 
                callback.onCommandResult(code, resultValue), VolumeRemindCommand.class.getSimpleName());
        }
        
        commandManager.setVolumeRemind(enabled);
    }
    
    //=========== 功能设置相关命令 ===========
    
    /**
     * 设置佩戴检测功能开关
     * @param enabled 是否启用
     * @param callback 结果回调
     */
    public void setWearDetection(boolean enabled, final CommandResultCallback callback) {
        if (callback != null) {
            commandManager.setCommandResultValueCallback((code, resultValue, result) -> 
                callback.onCommandResult(code, resultValue), WearDetectionCommand.class.getSimpleName());
        }
        
        commandManager.setWearDetection(enabled);
    }
    
    /**
     * 设置语音唤醒功能开关
     * @param enabled 是否启用
     * @param callback 结果回调
     */
    public void setVoiceWakeup(boolean enabled, final CommandResultCallback callback) {
        if (callback != null) {
            commandManager.setCommandResultValueCallback((code, resultValue, result) -> 
                callback.onCommandResult(code, resultValue), VoiceWakeupCommand.class.getSimpleName());
        }
        
        commandManager.setVoiceWakeup(enabled);
    }
    
    /**
     * 设置游戏模式开关
     * @param enabled 是否启用
     * @param callback 结果回调
     */
    public void setGameMode(boolean enabled, final CommandResultCallback callback) {
        if (callback != null) {
            commandManager.setCommandResultValueCallback((code, resultValue, result) -> 
                callback.onCommandResult(code, resultValue), GameModeCommand.class.getSimpleName());
        }
        
        commandManager.setGameMode(enabled);
    }
    
    /**
     * 设置掉落提醒功能开关
     * @param enabled 是否启用
     * @param callback 结果回调
     */
    public void setFallAlert(boolean enabled, final CommandResultCallback callback) {
        if (callback != null) {
            commandManager.setCommandResultValueCallback((code, resultValue, result) -> 
                callback.onCommandResult(code, resultValue), FallAlertCommand.class.getSimpleName());
        }
        
        commandManager.setFallAlert(enabled);
    }
    
    /**
     * 设置寻找耳机功能开关
     * @param enabled 是否启用
     * @param callback 结果回调
     */
    public void setEarLocation(boolean enabled, final CommandResultCallback callback) {
        if (callback != null) {
            commandManager.setCommandResultValueCallback((code, resultValue, result) -> 
                callback.onCommandResult(code, resultValue), EarLocationCommand.class.getSimpleName());
        }
        
        commandManager.setEarLocation(enabled);
    }
    
    /**
     * 控制左耳响铃
     * 发送命令让左耳机响铃或停止，帮助用户查找左耳机
     * @param start true为响铃, false为停止
     * @param callback 结果回调
     */
    public void findAlertLeft(boolean start, final CommandResultCallback callback) {
        if (callback != null) {
            commandManager.setCommandResultValueCallback((code, resultValue, result) -> 
                callback.onCommandResult(code, resultValue), FindAlertLeftCommand.class.getSimpleName());
        }
        
        commandManager.findAlertLeft(start);
    }
    
    /**
     * 控制右耳响铃
     * 发送命令让右耳机响铃或停止，帮助用户查找右耳机
     * @param start true为响铃, false为停止
     * @param callback 结果回调
     */
    public void findAlertRight(boolean start, final CommandResultCallback callback) {
        if (callback != null) {
            commandManager.setCommandResultValueCallback((code, resultValue, result) -> 
                callback.onCommandResult(code, resultValue), FindAlertRightCommand.class.getSimpleName());
        }
        
        commandManager.findAlertRight(start);
    }
    
    //=========== 控制设置相关命令 ===========
    
    /**
     * 设置触控控制功能
     * @param earSide 耳机侧（1-左耳，2-右耳）
     * @param tapType 点击类型（1-双击，2-三击）
     * @param function 功能代码
     *                 0 - 播放/暂停
     *                 1 - 音量+
     *                 2 - 音量-
     *                 3 - 上一曲
     *                 4 - 下一曲
     *                 5 - 语音助手
     *                 6 - EQ切换
     *                 7 - 游戏模式
     * @param callback 结果回调，只接收状态码，不接收resultValue
     */
    public void setControlFunction(int earSide, int tapType, int function, final CommandResultCallback callback) {
        if (callback != null) {
            commandManager.setCommandResultValueCallback((code, resultValue, result) -> 
                callback.onCommandResult(code, resultValue),ControlCommand.class.getSimpleName());
        }
        
        commandManager.setControlFunction(earSide, tapType, function);
    }
    
    /**
     * 恢复默认控制设置
     * @param callback 结果回调，只接收状态码，不接收resultValue
     */
    public void resetControlSettings(final CommandResultCallback callback) {
        if (callback != null) {
            commandManager.setCommandResultValueCallback((code, resultValue, result) -> 
                callback.onCommandResult(code, resultValue), ResetControlCommand.class.getSimpleName());
        }
        
        commandManager.resetControlSettings();
    }
    
    //=========== 电池电量相关命令 ===========
    
    /**
     * 获取所有电池电量信息
     * @param callback 电池信息回调
     */
    public void getBatteryInfo(final BatteryInfoCallback callback) {
        if (callback == null) {
            return;
        }
        
        // 设置电量更新监听
        commandManager.setBatteryInfoCallback((code, leftLevel, rightLevel, caseLevel) -> 
                callback.onBatteryInfoUpdated(code, leftLevel, rightLevel, caseLevel));
        
        // 获取所有电量
        commandManager.getBatteryInfo();
    }
    
    /**
     * 获取充电盒充电状态
     * @param callback 充电状态回调
     */
    public void getCaseChargingStatus(final CaseChargingStatusCallback callback) {
        if (callback == null) {
            return;
        }
        
        // 设置充电状态更新监听
        commandManager.setCaseChargingCallback((code, isCharging) -> 
                callback.onCaseChargingStatusUpdated(code, isCharging));
        
        // 获取充电盒充电状态
        commandManager.getCaseChargingStatus();
    }
    
    /**
     * 获取耳机在充电盒内状态
     * @param callback 耳机在充电盒内状态回调
     */
    public void getEarInCaseStatus(final EarInCaseStatusCallback callback) {
        if (callback == null) {
            return;
        }
        
        // 设置耳机在充电盒内状态更新监听
        commandManager.setEarInCaseStatusCallback((code, status, leftInCase, rightInCase) -> 
                callback.onEarInCaseStatusUpdated(code, status, leftInCase, rightInCase));
        
        // 获取耳机在充电盒内状态
        commandManager.requestEarInCaseStatus();
    }
    
    /**
     * 获取所有电池完整信息（包括电量和充电状态）
     * @param batteryCallback 电池信息回调
     * @param chargingCallback 充电状态回调
     */
    public void getFullBatteryInfo(final BatteryInfoCallback batteryCallback, 
                                  final CaseChargingStatusCallback chargingCallback) {
        if (batteryCallback != null) {
            commandManager.setBatteryInfoCallback((code, leftLevel, rightLevel, caseLevel) -> 
                    batteryCallback.onBatteryInfoUpdated(code, leftLevel, rightLevel, caseLevel));
        }
        
        if (chargingCallback != null) {
            commandManager.setCaseChargingCallback((code, isCharging) -> 
                    chargingCallback.onCaseChargingStatusUpdated(code, isCharging));
        }
        
        // 获取所有电池信息
        commandManager.getFullBatteryInfo();
    }
    

    
    /**
     * 获取左耳电池电量
     * @return 电池电量字符串（百分比数字或"--"）
     */
    public String getLeftBatteryLevel() {
        return commandManager.getLeftBatteryLevel();
    }
    
    /**
     * 获取右耳电池电量
     * @return 电池电量字符串（百分比数字或"--"）
     */
    public String getRightBatteryLevel() {
        return commandManager.getRightBatteryLevel();
    }
    
    /**
     * 获取充电仓电池电量
     * @return 电池电量字符串（百分比数字或"--"）
     */
    public String getCaseBatteryLevel() {
        return commandManager.getCaseBatteryLevel();
    }
    
    /**
     * 获取充电盒是否正在充电
     * @return 是否正在充电
     */
    public boolean isCaseCharging() {
        return commandManager.isCaseCharging();
    }
    
    /**
     * 获取耳机在充电盒内状态
     * @return 状态值 (0:都不在盒子内，1:左耳在，2:右耳在，3:都在盒子内)
     */
    public int getEarInCaseStatus() {
        return commandManager.getEarInCaseStatus();
    }
    
    /**
     * 左耳是否在充电盒内
     * @return 是否在充电盒内
     */
    public boolean isLeftEarInCase() {
        return commandManager.isLeftEarInCase();
    }
    
    /**
     * 右耳是否在充电盒内
     * @return 是否在充电盒内
     */
    public boolean isRightEarInCase() {
        return commandManager.isRightEarInCase();
    }
    
    //=========== 应用设置相关命令 ===========
    
    /**
     * 回调接口：耳机颜色结果回调
     */
    public interface EarColorCallback {
        /**
         * 获取到耳机颜色结果回调
         * @param code 结果状态码 (参考 CommandResultCode)
         * @param colorData 颜色原始数据
         */
        void onEarColorResult(int code, String colorData);
    }
    
    /**
     * 回调接口：固件版本结果回调
     */
    public interface FirmwareVersionCallback {
        /**
         * 获取到固件版本结果回调
         * @param code 结果状态码 (参考 CommandResultCode)
         * @param version 版本号字符串
         */
        void onFirmwareVersionResult(int code, String version);
    }
    
    /**
     * 回调接口：经典蓝牙MAC地址结果回调
     */
    public interface ClassicBtAddressCallback {
        /**
         * 获取到经典蓝牙MAC地址结果回调
         * @param code 结果状态码 (参考 CommandResultCode)
         * @param macAddress MAC地址字符串（格式：XX:XX:XX:XX:XX:XX）
         */
        void onClassicBtAddressResult(int code, String macAddress);
    }
    
    /**
     * 回调接口：设备型号名称结果回调
     */
    public interface DeviceModelNameCallback {
        /**
         * 获取到设备型号名称结果回调
         * @param code 结果状态码 (参考 CommandResultCode)
         * @param deviceName 设备型号名称
         */
        void onDeviceModelNameResult(int code, String deviceName);
    }
    
    /**
     * 回调接口：设备全局状态结果回调
     */
    public interface GlobalStatusCallback {
        /**
         * 获取到设备全局状态结果回调
         * @param code 结果状态码 (参考 CommandResultCode)
         * @param statusMap 状态字典，格式如 {81=01, 87=01, ...}
         */
        void onGlobalStatusResult(int code, Map<String, String> statusMap);
    }
    
    /**
     * 回调接口：首页聚合状态结果回调
     */
    public interface HomePageStatusCallback {
        /**
         * 获取到首页聚合状态结果回调
         * @param code 结果状态码 (参考 CommandResultCode)
         * @param statusMap 状态字典
         */
        void onHomePageStatusResult(int code, Map<String, String> statusMap);
    }
    
    /**
     * 回调接口：关于设备聚合状态结果回调
     */
    public interface AboutDeviceStatusCallback {
        /**
         * 获取到关于设备聚合状态结果回调
         * @param code 结果状态码 (参考 CommandResultCode)
         * @param statusMap 状态字典
         */
        void onAboutDeviceStatusResult(int code, Map<String, String> statusMap);
    }
    
    /**
     * 获取耳机颜色
     * @param callback 结果回调
     */
    public void getEarColor(final EarColorCallback callback) {
        if (callback != null) {
            commandManager.setCommandDataCallback((code, yobackdata, result) -> {
                String colorData = (yobackdata != null) ? yobackdata.toString() : null;
                callback.onEarColorResult(code, colorData);
            }, EarColorCommand.class.getSimpleName());
        }
        
        commandManager.getEarColor();
    }
    
    /**
     * 清除配对记录
     * @param callback 结果回调
     */
    public void clearPairing(final CommandResultCallback callback) {
        if (callback != null) {
            commandManager.setCommandResultValueCallback((code, resultValue, result) -> 
                callback.onCommandResult(code, resultValue), ClearPairingCommand.class.getSimpleName());
        }
        
        commandManager.clearPairing();
    }
    
    /**
     * 获取固件版本号
     * @param callback 结果回调
     */
    public void getFirmwareVersion(final FirmwareVersionCallback callback) {
        if (callback != null) {
            commandManager.setCommandDataCallback((code, yobackdata, result) -> {
                String version = (yobackdata != null) ? yobackdata.toString() : null;
                callback.onFirmwareVersionResult(code, version);
            }, FirmwareVersionCommand.class.getSimpleName());
        }
        
        commandManager.getFirmwareVersion();
    }
    
    /**
     * 修改耳机名称
     * @param deviceName 要设置的设备名称，最大支持210字节（UTF-8编码）
     * @param callback 结果回调
     */
    public void setDeviceName(String deviceName, final CommandResultCallback callback) {
        if (callback != null) {
            commandManager.setCommandResultValueCallback((code, resultValue, result) -> 
                callback.onCommandResult(code, resultValue), DeviceRenameCommand.class.getSimpleName());
        }
        
        commandManager.setDeviceName(deviceName);
    }
    
    /**
     * 获取经典蓝牙（SPP）MAC地址
     * @param callback 结果回调
     */
    public void getDeviceMacAddress(final ClassicBtAddressCallback callback) {
        if (callback != null) {
            commandManager.setCommandDataCallback((code, yobackdata, result) -> {
                String macAddress = (yobackdata != null) ? yobackdata.toString() : null;
                callback.onClassicBtAddressResult(code, macAddress);
            }, ClassicBtAddressCommand.class.getSimpleName());
        }
        
        commandManager.getClassicBtAddress();
    }
    
    /**
     * 获取蓝牙设备型号名称
     * @param callback 结果回调
     */
    public void getDeviceModelName(final DeviceModelNameCallback callback) {
        if (callback != null) {
            commandManager.setCommandDataCallback((code, yobackdata, result) -> {
                String deviceName = (yobackdata != null) ? yobackdata.toString() : null;
                callback.onDeviceModelNameResult(code, deviceName);
            }, DeviceNameGetCommand.class.getSimpleName());
        }
        
        commandManager.getDeviceModelName();
    }
    
    /**
     * 获取全局状态（包括电量、版本号等所有状态信息）
     * @param callback 回调接口，如果为null则不返回结果，只执行命令
     */
    public void getGlobalStatus(final GlobalStatusCallback callback) {
        if (callback != null) {
            // 检查队列中是否已有 GlobalStatusCommand，如果有则拒绝创建新的
            if (commandQueueCenter.hasGlobalStatusCommand()) {
                Log.w(TAG, "队列中已存在GlobalStatusCommand，拒绝创建重复命令");
                callback.onGlobalStatusResult(CommandResultCode.FAILED, new LinkedHashMap<>());
                return;
            }
            
            // 创建类型化回调，只处理 GlobalStatusCommand 的完成事件
            TypedCommandCallback<GlobalStatusCommand> typedCallback = new TypedCommandCallback<GlobalStatusCommand>(GlobalStatusCommand.class) {
                @Override
                public void onTypedCommandCompleted(GlobalStatusCommand command, int code, String result) {
                    // 只处理最终结果，忽略中间状态
                    if (result != null && result.contains("数据块聚合中")) {
                        return; // 忽略中间状态的通知
                    }
                    
                    callback.onGlobalStatusResult(code, new HashMap<>());
                }

                @Override
                public void onTypedCommandCompleted(GlobalStatusCommand command, int code, Object yobackdata, String result) {
                    // 只处理最终结果，忽略中间状态  
                    if (result != null && result.contains("数据块聚合中")) {
                        return; // 忽略中间状态的通知
                    }
                    
                    if (yobackdata != null && yobackdata instanceof Map) {
                        Map<String, String> statusMap = new HashMap<>();
                        
                        // 转换Map数据
                        @SuppressWarnings("unchecked")
                        Map<String, String> dataMap = (Map<String, String>)yobackdata;
                        statusMap.putAll(dataMap);
                        
                        callback.onGlobalStatusResult(code, statusMap);
                    } else if (code != CommandResultCode.SUCCESS) {
                        // 如果是失败情况，也需要通知
                        callback.onGlobalStatusResult(code, new HashMap<>());
                    }
                    // 忽略中间状态的回调（result为"数据块聚合中..."时）
                }
            };
            
            // 为 GlobalStatusCommand 类型注册类型特定回调，避免被系统强制清理
            CommandCallbackManager.getInstance().registerTypeCallback(
                "GlobalStatusCommand", 
                typedCallback, 
                true // 一次性回调，在收到结果后自动清理
            );
            
            // 创建并执行命令
            GlobalStatusCommand command = new GlobalStatusCommand();
            commandQueueCenter.enqueueCommand(command);
        } else {
            // 如果没有提供回调，直接执行命令
            commandManager.getGlobalStatus();
        }
    }
    
    /**
     * 获取首页聚合状态
     * 一次性获取电量、固件版本等信息，并以数据字典格式返回
     * @param callback 回调接口
     */
    public void getHomePageStatus(final HomePageStatusCallback callback) {
        if (callback == null) {
            Log.w(TAG, "getHomePageStatus - 回调不能为空");
            return;
        }
        
        // 调用新的HomePageStatusAggregator V3
        new HomePageStatusAggregator(this).fetchStatus(callback);
    }
    
    /**
     * 获取关于设备页面聚合信息
     * @param callback 结果回调
     */
    public void getAboutDeviceStatus(final AboutDeviceStatusCallback callback) {
        AboutDeviceStatusAggregator aggregator = new AboutDeviceStatusAggregator(callback);
        commandQueueCenter.enqueueBlock(aggregator);
    }

    /**
     * 开始电池状态监听
     * 此方法将启动一个后台监听器，持续接收电池状态的主动上报
     * 并在状态变化时通过回调通知。
     * 
     * 注意：电量监听启动过程包含多个初始化命令的执行，为确保系统稳定，
     * 启动完成后会自动暂停命令队列500ms，然后才会继续执行其他命令。
     * 
     * @param callback 状态变化时的回调
     */
    public void startBatteryMonitor(final BatteryStateCallback callback) {
        if (callback == null) {
            Log.w(TAG, "startBatteryMonitor 失败: callback 为空");
            return;
        }
        
        Log.d(TAG, "开始电池状态监听");
        BatteryInfoListener.getInstance().startListening(callback::onBatteryStateChanged);
    }

    /**
     * 监听游戏模式变化
     * @param callback 游戏模式变化回调
     */
    public void listenGameMode(GameModeCallback callback) {
        commandManager.listenGameMode(callback);
    }

    /**
     * 监听EQ模式变化
     *  @param callback EQ模式变化回调
     **/
    public void listenEQMode(EQModeCallback callback) {
        commandManager.listenEQMode(callback);
    }
    
    /**
     * 停止电池状态监听
     * 停止后将不再接收电池状态更新，以节省资源。
     */
    public void stopBatteryMonitor() {
        Log.d(TAG, "停止电池状态监听");
        BatteryInfoListener.getInstance().stopListening();
    }
    
    /**
     * 清理所有全局回调
     * 用于防止回调污染，建议在Activity的onPause或onDestroy中调用
     */
    public void clearAllGlobalCallbacks() {
        Log.d(TAG, "清理所有全局回调");
        commandManager.clearAllGlobalCallbacks();
    }
} 