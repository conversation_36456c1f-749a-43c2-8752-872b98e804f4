package com.ggec.hs01.activity;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.Toast;
import android.bluetooth.BluetoothDevice;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.ggec.hs01.R;
import com.ggec.bleservice.YoBLEApi;
import com.ggec.bleservice.YoCommandApi;
import com.ggec.hs01.view.HMSwitch;
import com.ggec.hs01.view.HMButton;
import com.ggec.hs01.GGECHSApplication;
import android.util.Log;
import android.os.Handler;


import com.ggec.hs01.view.HMSwitch.OnCheckedChangeListener;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 功能设置界面
 * 允许用户自定义耳机的功能设置
 */
public class FunctionSettingsActivity extends AppCompatActivity {
    private static final String TAG = "FunctionSettingsActivity";
    
    // 添加YoCommandApi变量
    private YoCommandApi commandApi;
    
    // YoBLEApi 实例
    private YoBLEApi bleApi;

    // 蓝牙事件监听器
    private YoBLEApi.BleListener bleListener;

    // 界面控件
    private HMSwitch switchWearDetection;
    private HMSwitch switchVadWakeup;
    private HMSwitch switchGameMode;
    private HMSwitch switchFallAlert;
    private HMSwitch switchEarLocation;

    // 按钮替换开关
    private HMSwitch switchFindAlertLeft;
    private HMSwitch switchFindAlertRight;

    private Handler handler = new Handler();
    private Runnable leftRunnable;
    private Runnable rightRunnable;
    private boolean isActiveReport = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_function_settings);
        
        // 获取YoBLEApi实例
        bleApi = ((GGECHSApplication) getApplication()).getBleApi();
        
        // 获取YoCommandApi实例
        commandApi = ((GGECHSApplication) getApplication()).getCommandApi();
        
        // 初始化返回按钮
        initBackButton();
        
        // 初始化界面控件
        initViews();

        // 初始化蓝牙监听器
        initBleListener();
        
        // 设置内容区域适应系统UI，保留内边距防止内容被状态栏遮挡
//        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.function_settings_main), (v, insets) -> {
//            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
//            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
//            return insets;
//        });

        commandApi.listenGameMode(enabled -> runOnUiThread(() -> {
            isActiveReport = true;
            switchGameMode.setChecked(enabled);
            isActiveReport = false;
        }));
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        bleApi.registerListener(bleListener);
        // 确保已连接设备，如果没有则返回
        if (!bleApi.isDeviceConnected()) {
            Toast.makeText(this, "请先连接设备", Toast.LENGTH_SHORT).show();
            finish();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        bleApi.unregisterListener(bleListener);
        
        // 清理全局回调，防止污染其他Activity的命令执行
        if (commandApi != null) {
            commandApi.clearAllGlobalCallbacks();
            Log.d(TAG, "已清理FunctionSettingsActivity的全局回调");
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 移除回调，防止内存泄漏
        handler.removeCallbacks(leftRunnable);
        handler.removeCallbacks(rightRunnable);
        
        // 确保Activity销毁时清理所有回调
        if (commandApi != null) {
            commandApi.clearAllGlobalCallbacks();
            commandApi.listenGameMode(null);
            Log.d(TAG, "Activity销毁时已清理全局回调");
        }
    }

    /**
     * 初始化蓝牙事件监听器
     */
    private void initBleListener() {
        bleListener = new YoBLEApi.BleListener() {
            @Override
            public void onDeviceDisconnected(BluetoothDevice device, boolean disconnected) {
                runOnUiThread(() -> {
                    if (disconnected) {
                        Toast.makeText(FunctionSettingsActivity.this, "蓝牙连接已断开", Toast.LENGTH_LONG).show();
                        finish();
                    }
                });
            }
        };
    }
    
    /**
     * 初始化返回按钮
     */
    private void initBackButton() {
        ImageView btnBack = findViewById(R.id.btn_back);
        btnBack.setOnClickListener(v -> finish());
    }
    
    /**
     * 初始化界面控件
     */
    private void initViews() {
        // 佩戴检测开关
        switchWearDetection = findViewById(R.id.switch_wear_detection);
        switchWearDetection.setOnCheckedChangeListener(new OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(HMSwitch buttonView, boolean isChecked) {
                Log.d(TAG, "佩戴检测开关状态变更: " + isChecked);
                
                // 使用YoCommandApi设置佩戴检测
                commandApi.setWearDetection(isChecked, (code, resultValue) -> {
                    runOnUiThread(() -> {
                        // 显示结果消息
                        String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? 
                                "设置成功" : "设置失败";
                        Toast.makeText(FunctionSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                        
                        // 如果失败，恢复开关状态
//                        if (code != YoCommandApi.CommandResultCode.SUCCESS) {
//                            switchWearDetection.setChecked(!isChecked);
//                        }
                    });
                });
            }
        });
        
        // 语音唤醒开关
        switchVadWakeup = findViewById(R.id.switch_vad_wakeup);
        switchVadWakeup.setOnCheckedChangeListener(new OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(HMSwitch buttonView, boolean isChecked) {
                Log.d(TAG, "语音唤醒开关状态变更: " + isChecked);
                
                // 使用YoCommandApi设置语音唤醒
                commandApi.setVoiceWakeup(isChecked, (code, resultValue) -> {
                    runOnUiThread(() -> {
                        // 显示结果消息
                        String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? 
                                "设置成功" : "设置失败";
                        Toast.makeText(FunctionSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                        
                        // 如果失败，恢复开关状态
//                        if (code != YoCommandApi.CommandResultCode.SUCCESS) {
//                            switchVadWakeup.setChecked(!isChecked);
//                        }
                    });
                });
            }
        });
        
        // 游戏模式开关
        switchGameMode = findViewById(R.id.switch_game_mode);
        switchGameMode.setOnCheckedChangeListener(new OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(HMSwitch buttonView, boolean isChecked) {
                if(isActiveReport){
                    return;
                }
                Log.d(TAG, "游戏模式开关状态变更: " + isChecked);
                
                // 使用YoCommandApi设置游戏模式
                commandApi.setGameMode(isChecked, (code, resultValue) -> {
                    runOnUiThread(() -> {
                        // 显示结果消息
                        String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? 
                                "设置成功" : "设置失败";
                        Toast.makeText(FunctionSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                        
                        // 如果失败，恢复开关状态
//                        if (code != YoCommandApi.CommandResultCode.SUCCESS) {
//                            switchGameMode.setChecked(!isChecked);
//                        }
                    });
                });
            }
        });
        
        // 掉落提醒开关
        switchFallAlert = findViewById(R.id.switch_fall_alert);
        switchFallAlert.setOnCheckedChangeListener(new OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(HMSwitch buttonView, boolean isChecked) {
                Log.d(TAG, "掉落提醒开关状态变更: " + isChecked);
                
                // 使用YoCommandApi设置掉落提醒
                commandApi.setFallAlert(isChecked, (code, resultValue) -> {
                    runOnUiThread(() -> {
                        // 显示结果消息
                        String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? 
                                "设置成功" : "设置失败";
                        Toast.makeText(FunctionSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                        
                        // 如果失败，恢复开关状态
//                        if (code != YoCommandApi.CommandResultCode.SUCCESS) {
//                            switchFallAlert.setChecked(!isChecked);
//                        }
                    });
                });
            }
        });
        
        // 寻找耳机开关
        switchEarLocation = findViewById(R.id.switch_ear_location);
        switchEarLocation.setOnCheckedChangeListener(new OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(HMSwitch buttonView, boolean isChecked) {
                Log.d(TAG, "寻找耳机开关状态变更: " + isChecked);
                
                // 使用YoCommandApi设置寻找耳机功能
                commandApi.setEarLocation(isChecked, (code, resultValue) -> {
                    runOnUiThread(() -> {
                        // 显示结果消息
                        String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? 
                                "设置成功" : "设置失败";
                        Toast.makeText(FunctionSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                        
                        // 如果失败，恢复开关状态
//                        if (code != YoCommandApi.CommandResultCode.SUCCESS) {
//                            switchEarLocation.setChecked(!isChecked);
//                        }
                    });
                });
            }
        });
        
        // 寻找提示音开关 - 左耳响铃
        switchFindAlertLeft = findViewById(R.id.switch_find_alert_left);
        switchFindAlertLeft.setOnCheckedChangeListener(new OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(HMSwitch buttonView, boolean isChecked) {
                if (isChecked) {
                    Log.d(TAG, "触发左耳响铃");
                    commandApi.findAlertLeft(true, (code, resultValue) -> {
                        runOnUiThread(() -> {
                            String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? "左耳响铃已触发" : "左耳响铃触发失败";
                            Toast.makeText(FunctionSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                            if (code != YoCommandApi.CommandResultCode.SUCCESS) {
                                buttonView.setChecked(false);
                            } else {
                                // 30秒后自动关闭
                                leftRunnable = () -> buttonView.setChecked(false);
                                handler.postDelayed(leftRunnable, 30000);
                            }
                        });
                    });
                } else {
                    Log.d(TAG, "停止左耳响铃");
                    handler.removeCallbacks(leftRunnable); // 移除定时器
                    commandApi.findAlertLeft(false, (code, resultValue) -> {
                        runOnUiThread(() -> {
                            String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? "左耳响铃已停止" : "左耳响铃停止失败";
                            Toast.makeText(FunctionSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                        });
                    });
                }
            }
        });

        // 寻找提示音开关 - 右耳响铃
        switchFindAlertRight = findViewById(R.id.switch_find_alert_right);
        switchFindAlertRight.setOnCheckedChangeListener(new OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(HMSwitch buttonView, boolean isChecked) {
                if (isChecked) {
                    Log.d(TAG, "触发右耳响铃");
                    commandApi.findAlertRight(true, (code, resultValue) -> {
                        runOnUiThread(() -> {
                            String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? "右耳响铃已触发" : "右耳响铃触发失败";
                            Toast.makeText(FunctionSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                            if (code != YoCommandApi.CommandResultCode.SUCCESS) {
                                buttonView.setChecked(false);
                            } else {
                                // 30秒后自动关闭
                                rightRunnable = () -> buttonView.setChecked(false);
                                handler.postDelayed(rightRunnable, 30000);
                            }
                        });
                    });
                } else {
                    Log.d(TAG, "停止右耳响铃");
                    handler.removeCallbacks(rightRunnable); // 移除定时器
                    commandApi.findAlertRight(false, (code, resultValue) -> {
                        runOnUiThread(() -> {
                            String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? "右耳响铃已停止" : "右耳响铃停止失败";
                            Toast.makeText(FunctionSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                        });
                    });
                }
            }
        });
    }
} 