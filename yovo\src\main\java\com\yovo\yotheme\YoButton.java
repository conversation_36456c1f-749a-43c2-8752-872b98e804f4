package com.yovo.yotheme;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.RippleDrawable;
import android.graphics.drawable.StateListDrawable;
import android.os.Build;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.widget.TextView;

/**
 * 自定义Yo按钮
 * 包含样式和背景定义，便于移植到其他项目
 * 不依赖任何第三方库，纯原生实现
 */
public class YoButton extends TextView {
    // 设计颜色
    private static final int COLOR_NORMAL = 0xFF0A59F7; // 宇宙蓝（默认）
    private static final int COLOR_PRESSED = 0xFF0048D4; // 深蓝（按下）
    private static final int COLOR_TEXT = 0xFFFFFFFF;    // 文本颜色（白色）

    // 尺寸常量
    private static final int DEFAULT_CORNER_RADIUS_DP = 32;
    private static final int DEFAULT_MIN_WIDTH_DP = 100;     // 从120减小到100
    private static final int DEFAULT_MIN_HEIGHT_DP = 40;     // 从48减小到40
    private static final int DEFAULT_PADDING_HORIZONTAL_DP = 16; // 从24减小到16
    private static final int DEFAULT_PADDING_VERTICAL_DP = 8;    // 从12减小到8
    private static final int DEFAULT_TEXT_SIZE_SP = 16;

    // 文本自适应常量
    private static final int MIN_TEXT_SIZE_SP = 12;
    private static final int MAX_TEXT_SIZE_SP = 18;
    private static final int AUTO_SIZE_STEP_GRANULARITY_SP = 1;

    public YoButton(Context context) {
        super(context);
        init(context, null);
    }

    public YoButton(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public YoButton(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        // 创建背景
        setBackground(createButtonBackground());
        
        // 设置文本颜色
        setTextColor(COLOR_TEXT);
        
        // 设置文本样式
        setTextSize(TypedValue.COMPLEX_UNIT_SP, DEFAULT_TEXT_SIZE_SP);
        setAllCaps(false);
        setTypeface(getTypeface(), android.graphics.Typeface.BOLD);
        
        // 设置布局参数
        setMinWidth(dpToPx(context, DEFAULT_MIN_WIDTH_DP));
        setMinHeight(dpToPx(context, DEFAULT_MIN_HEIGHT_DP));
        setPadding(
                dpToPx(context, DEFAULT_PADDING_HORIZONTAL_DP),
                dpToPx(context, DEFAULT_PADDING_VERTICAL_DP),
                dpToPx(context, DEFAULT_PADDING_HORIZONTAL_DP),
                dpToPx(context, DEFAULT_PADDING_VERTICAL_DP)
        );
        
        // 设置居中
        setGravity(android.view.Gravity.CENTER);
        
        // 设置可点击和可聚焦
        setClickable(true);
        setFocusable(true);
        
        // 取消默认的阴影（Android 5.0+特性）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            setStateListAnimator(null);
            setElevation(0);
        }
        
        // 设置文本自动调整大小功能
        setupAutoSizeText();
    }

    /**
     * 设置文本自动调整大小
     * 使用原生API，避免依赖androidx
     */
    private void setupAutoSizeText() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // 对于API 26及以上，使用原生的自动调整文本大小功能
            setAutoSizeTextTypeWithDefaults(TextView.AUTO_SIZE_TEXT_TYPE_UNIFORM);
            setAutoSizeTextTypeUniformWithConfiguration(
                    MIN_TEXT_SIZE_SP,
                    MAX_TEXT_SIZE_SP,
                    AUTO_SIZE_STEP_GRANULARITY_SP,
                    TypedValue.COMPLEX_UNIT_SP
            );
        } else {
            // 对于API 26以下，使用默认大小
            setTextSize(TypedValue.COMPLEX_UNIT_SP, DEFAULT_TEXT_SIZE_SP);
            // 注：如果需要在低版本支持自动调整文本大小，可以在这里实现自定义逻辑
            // 例如根据控件宽度动态调整文字大小，或使用Paint.measureText()计算
        }
    }

    /**
     * 创建按钮背景
     * 实现与bg_hm_button.xml相同的效果
     */
    private StateListDrawable createButtonBackground() {
        StateListDrawable stateListDrawable = new StateListDrawable();
        
        // 按下状态背景
        GradientDrawable pressedDrawable = new GradientDrawable();
        pressedDrawable.setColor(COLOR_PRESSED);
        pressedDrawable.setCornerRadius(dpToPx(getContext(), DEFAULT_CORNER_RADIUS_DP));
        
        // 普通状态背景
        GradientDrawable normalDrawable = new GradientDrawable();
        normalDrawable.setColor(COLOR_NORMAL);
        normalDrawable.setCornerRadius(dpToPx(getContext(), DEFAULT_CORNER_RADIUS_DP));
        
        // 添加不同状态的背景
        stateListDrawable.addState(new int[]{android.R.attr.state_pressed}, pressedDrawable);
        stateListDrawable.addState(new int[]{}, normalDrawable);
        
        return stateListDrawable;
    }
    
    /**
     * dp转px工具方法
     */
    private int dpToPx(Context context, float dp) {
        return (int) TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP,
                dp,
                context.getResources().getDisplayMetrics()
        );
    }
    
    /**
     * 设置按钮主色调
     */
    public void setButtonColor(int normalColor, int pressedColor) {
        StateListDrawable background = (StateListDrawable) getBackground();
        
        // 更新按钮背景颜色
        GradientDrawable normalDrawable = new GradientDrawable();
        normalDrawable.setColor(normalColor);
        normalDrawable.setCornerRadius(dpToPx(getContext(), DEFAULT_CORNER_RADIUS_DP));
        
        GradientDrawable pressedDrawable = new GradientDrawable();
        pressedDrawable.setColor(pressedColor);
        pressedDrawable.setCornerRadius(dpToPx(getContext(), DEFAULT_CORNER_RADIUS_DP));
        
        StateListDrawable newBackground = new StateListDrawable();
        newBackground.addState(new int[]{android.R.attr.state_pressed}, pressedDrawable);
        newBackground.addState(new int[]{}, normalDrawable);
        
        setBackground(newBackground);
    }
    
    /**
     * 设置圆角半径
     */
    public void setCornerRadius(float radiusDp) {
        StateListDrawable background = createButtonBackground();
        setBackground(background);
    }
    
    /**
     * 设置按钮尺寸
     * @param widthDp 宽度(dp)
     * @param heightDp 高度(dp)
     * @param horizontalPaddingDp 水平内边距(dp)
     * @param verticalPaddingDp 垂直内边距(dp)
     */
    public void setButtonSize(int widthDp, int heightDp, int horizontalPaddingDp, int verticalPaddingDp) {
        Context context = getContext();
        
        if (widthDp > 0) {
            setMinWidth(dpToPx(context, widthDp));
        }
        
        if (heightDp > 0) {
            setMinHeight(dpToPx(context, heightDp));
        }
        
        setPadding(
            dpToPx(context, horizontalPaddingDp),
            dpToPx(context, verticalPaddingDp),
            dpToPx(context, horizontalPaddingDp),
            dpToPx(context, verticalPaddingDp)
        );
    }
} 