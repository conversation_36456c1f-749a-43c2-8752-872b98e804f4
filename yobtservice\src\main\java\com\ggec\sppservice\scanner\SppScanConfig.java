package com.ggec.sppservice.scanner;

import java.util.UUID;

/** 
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * SPP扫描配置类
 * 使用建造者模式支持链式调用
 */
public class SppScanConfig {
    // SPP服务的UUID
    public static final UUID SPP_UUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB");
    
    // 默认超时时间12秒
    private static final long DEFAULT_TIMEOUT = 12000;
    
    // 扫描超时时间（毫秒）
    private long scanTimeout = DEFAULT_TIMEOUT;
    
    // 设备名称过滤
    private String deviceNameFilter;
    
    // 是否仅显示经典蓝牙设备（默认为true，过滤掉BLE设备）
    private boolean classicBluetoothOnly = true;

    // 设备mac地址过滤
    private String deviceMacFilter;
    
    /**
     * 创建默认配置
     */
    public static SppScanConfig createDefault() {
        return new SppScanConfig();
    }
    
    /**
     * 创建新的构建器
     */
    public static Builder newBuilder() {
        return new Builder();
    }
    
    /**
     * 设置扫描超时时间
     */
    public SppScanConfig setScanTimeout(long timeoutMillis) {
        this.scanTimeout = timeoutMillis > 0 ? timeoutMillis : DEFAULT_TIMEOUT;
        return this;
    }
    
    /**
     * 设置设备名称过滤
     */
    public SppScanConfig setDeviceNameFilter(String deviceName) {
        this.deviceNameFilter = deviceName;
        return this;
    }
    
    /**
     * 设置是否仅显示经典蓝牙设备
     */
    public SppScanConfig setClassicBluetoothOnly(boolean classicOnly) {
        this.classicBluetoothOnly = classicOnly;
        return this;
    }

    /**
     * 设置设备mac地址过滤
     */
    public SppScanConfig setDeviceMacFilter(String deviceMac) {
        this.deviceMacFilter = deviceMac;
        return this;
    }
    
    /**
     * 获取扫描超时时间
     */
    public long getScanTimeout() {
        return scanTimeout;
    }
    
    /**
     * 获取设备名称过滤
     */
    public String getDeviceNameFilter() {
        return deviceNameFilter;
    }

    /**
     * 获取设备mac地址过滤
     */
    public String getDeviceMacFilter() {
        return deviceMacFilter;
    }
    
    /**
     * 获取是否仅显示经典蓝牙设备
     */
    public boolean isClassicBluetoothOnly() {
        return classicBluetoothOnly;
    }
    
    /**
     * 扫描配置构建器
     * 支持链式调用方法
     */
    public static class Builder {
        private final SppScanConfig config = new SppScanConfig();
        
        /**
         * 设置扫描超时时间
         * @param timeoutMillis 超时时间（毫秒）
         */
        public Builder timeout(long timeoutMillis) {
            config.setScanTimeout(timeoutMillis);
            return this;
        }
        
        /**
         * 设置设备名称过滤
         * @param deviceName 设备名称
         */
        public Builder nameFilter(String deviceName) {
            config.setDeviceNameFilter(deviceName);
            return this;
        }

        /**
         * 设置设备mac地址过滤
         * @param deviceMac 设备mac地址
         */
        public Builder macFilter(String deviceMac) {
            config.setDeviceMacFilter(deviceMac);
            return this;
        }
        
        /**
         * 设置是否仅显示经典蓝牙设备
         * @param classicOnly 是否仅显示经典蓝牙设备
         */
        public Builder classicBluetoothOnly(boolean classicOnly) {
            config.setClassicBluetoothOnly(classicOnly);
            return this;
        }
        
        /**
         * 构建SppScanConfig实例
         */
        public SppScanConfig build() {
            return config;
        }
    }
} 