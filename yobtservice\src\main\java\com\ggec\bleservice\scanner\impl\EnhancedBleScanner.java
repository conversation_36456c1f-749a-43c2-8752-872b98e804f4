//package com.ggec.bleservice.scanner.impl;
//
//import android.Manifest;
//import android.bluetooth.BluetoothAdapter;
//import android.bluetooth.BluetoothDevice;
//import android.bluetooth.le.BluetoothLeScanner;
//import android.bluetooth.le.ScanFilter;
//import android.bluetooth.le.ScanResult;
//import android.bluetooth.le.ScanSettings;
//import android.content.Context;
//import android.content.pm.PackageManager;
//import android.location.Location;
//import android.location.LocationListener;
//import android.location.LocationManager;
//import android.os.Build;
//import android.os.Bundle;
//import android.os.Handler;
//import android.os.Looper;
//import android.os.ParcelUuid;
//import com.ggec.yobtsdkserver.utils.YoBTSDKLog;
//
//import androidx.core.app.ActivityCompat;
//
//import com.ggec.bleservice.scanner.IScanner;
//import com.ggec.bleservice.scanner.ScanCallback;
//import com.ggec.bleservice.scanner.ScanConfig;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.ConcurrentHashMap;
//
///**
// * 增强型蓝牙扫描器实现
// * 结合位置服务和nRF Connect扫描机制，提供更精确的设备扫描结果
// */
//public class EnhancedBleScanner extends BaseScanner implements IScanner {
//    private static final String TAG = "EnhancedBleScanner";
//
//    private final Context context;
//    private final BluetoothAdapter bluetoothAdapter;
//    private BluetoothLeScanner bleScanner;
//    private ScanSettings scanSettings;
//    private List<ScanFilter> scanFilters;
//    private final Handler handler = new Handler(Looper.getMainLooper());
//    private ScanConfig scanConfig;
//    private ScanCallback scanCallback;
//    private boolean scanning = false;
//
//    // 位置服务相关
//    private LocationManager locationManager;
//    private Location lastLocation;
//    private final Map<String, DeviceLocationData> deviceLocationMap = new ConcurrentHashMap<>();
//    private static final float LOCATION_DISTANCE_THRESHOLD = 5.0f; // 设备距离阈值（米）
//
//    // 设备信号强度历史数据
//    private final Map<String, List<Integer>> rssiHistory = new ConcurrentHashMap<>();
//    private static final int MAX_RSSI_HISTORY = 10;
//
//    // nRF Connect 风格的扫描回调
//    private final android.bluetooth.le.ScanCallback nrfScanCallback = new android.bluetooth.le.ScanCallback() {
//        @Override
//        public void onScanResult(int callbackType, ScanResult result) {
//            super.onScanResult(callbackType, result);
//            if (result == null || result.getDevice() == null) return;
//
//            BluetoothDevice device = result.getDevice();
//            int rssi = result.getRssi();
//
//            // 保持与ModernBleScanner一致的scanRecord处理
//            android.bluetooth.le.ScanRecord scanRecord = result.getScanRecord();
//            byte[] scanRecordBytes = scanRecord != null ? scanRecord.getBytes() : new byte[0];
//
//            // 保持与ModernBleScanner一致的serviceUuids处理
//            List<ParcelUuid> serviceUuids = scanRecord != null ? scanRecord.getServiceUuids() : null;
//
//            // 更新设备的RSSI历史
//            updateRssiHistory(device.getAddress(), rssi);
//
//            // 更新设备位置数据
//            updateDeviceLocation(device.getAddress(), rssi);
//
//            // 通知回调，保持参数格式一致
//            if (scanCallback != null) {
//                scanCallback.onFound(device, rssi, scanRecordBytes, serviceUuids);
//            }
//        }
//
//        @Override
//        public void onBatchScanResults(List<ScanResult> results) {
//            super.onBatchScanResults(results);
//            for (ScanResult result : results) {
//                onScanResult(0, result);
//            }
//        }
//
//        @Override
//        public void onScanFailed(int errorCode) {
//            super.onScanFailed(errorCode);
//            YoBTSDKLog.e(TAG, "蓝牙扫描失败，错误码: " + errorCode);
//            stopScan();
//        }
//    };
//
//    // 位置更新监听器
//    private final LocationListener locationListener = new LocationListener() {
//        @Override
//        public void onLocationChanged(Location location) {
//            if (location != null) {
//                lastLocation = location;
//                YoBTSDKLog.d(TAG, "位置更新: " + location.getLatitude() + ", " + location.getLongitude());
//            }
//        }
//
//        // 必须实现的其他方法
//        @Override
//        public void onProviderEnabled(String provider) {}
//
//        @Override
//        public void onProviderDisabled(String provider) {}
//
//        @Override
//        public void onStatusChanged(String provider, int status, Bundle extras) {
//            // Android R (API 30)开始废弃，但需要实现以兼容旧版本
//        }
//    };
//
//    /**
//     * 构造函数
//     * @param context 上下文
//     */
//    public EnhancedBleScanner(Context context) {
//        super(context);
//        this.context = context.getApplicationContext();
//
//        // 获取蓝牙适配器
//        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
//        if (bluetoothAdapter != null) {
//            bleScanner = bluetoothAdapter.getBluetoothLeScanner();
//        }
//
//        // 初始化位置服务
//        initLocationService();
//
//        // 默认扫描设置
//        createDefaultScanSettings();
//    }
//
//    /**
//     * 初始化位置服务
//     */
//    private void initLocationService() {
//        locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
//
//        // 检查位置权限
//        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) ==
//                PackageManager.PERMISSION_GRANTED) {
//
//            // 尝试获取最后已知位置
//            if (locationManager != null) {
//                lastLocation = locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER);
//                if (lastLocation == null) {
//                    lastLocation = locationManager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER);
//                }
//
//                // 注册位置更新
//                try {
//                    locationManager.requestLocationUpdates(
//                            LocationManager.GPS_PROVIDER,
//                            1000,   // 最小时间间隔（毫秒）
//                            1.0f,   // 最小距离变化（米）
//                            locationListener
//                    );
//                } catch (SecurityException e) {
//                    YoBTSDKLog.e(TAG, "请求位置更新失败: " + e.getMessage());
//                }
//            }
//        } else {
//            YoBTSDKLog.w(TAG, "没有ACCESS_FINE_LOCATION权限，位置增强功能将不可用");
//        }
//    }
//
//    /**
//     * 创建默认的扫描设置
//     */
//    private void createDefaultScanSettings() {
//        // nRF Connect 风格的扫描设置
//        ScanSettings.Builder settingsBuilder = new ScanSettings.Builder()
//                .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)  // 高性能扫描模式
//                .setReportDelay(0);  // 实时报告结果
//
//        // Android 8.0+ 设置
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            //settingsBuilder.setLegacy(false);  // 使用新的扫描API
//            settingsBuilder.setPhy(ScanSettings.PHY_LE_ALL_SUPPORTED);  // 支持所有PHY
//            settingsBuilder.setMatchMode(ScanSettings.MATCH_MODE_AGGRESSIVE);  // 积极匹配模式
//        }
//
//        // Android 10+ 设置 (Android Q = API 29)
//        if (Build.VERSION.SDK_INT >= 29) { // Build.VERSION_CODES.Q = 29
//            settingsBuilder.setNumOfMatches(ScanSettings.MATCH_NUM_MAX_ADVERTISEMENT);  // 匹配最大广告数
//        }
//
//        scanSettings = settingsBuilder.build();
//        scanFilters = new ArrayList<>();  // 默认无过滤器
//    }
//
//    /**
//     * 更新设备的RSSI历史
//     * @param deviceAddress 设备地址
//     * @param rssi RSSI值
//     */
//    private void updateRssiHistory(String deviceAddress, int rssi) {
//        List<Integer> history = rssiHistory.computeIfAbsent(deviceAddress, k -> new ArrayList<>());
//
//        history.add(rssi);
//        if (history.size() > MAX_RSSI_HISTORY) {
//            history.remove(0);
//        }
//    }
//
//    /**
//     * 获取设备的平均RSSI值
//     * @param deviceAddress 设备地址
//     * @return 平均RSSI值，如果没有历史记录则返回0
//     */
//    public int getAverageRssi(String deviceAddress) {
//        List<Integer> history = rssiHistory.get(deviceAddress);
//        if (history == null || history.isEmpty()) {
//            return 0;
//        }
//
//        int sum = 0;
//        for (Integer rssi : history) {
//            sum += rssi;
//        }
//
//        return sum / history.size();
//    }
//
//    /**
//     * 根据RSSI值估算设备距离
//     * @param rssi RSSI值
//     * @return 估计的距离（米）
//     */
//    private double estimateDistance(int rssi) {
//        // 简单的RSSI到距离转换模型
//        // 实际应用中可能需要更复杂的算法
//        int txPower = -59; // 假设的发射功率，实际应用中可从设备广播中获取
//        if (rssi == 0) {
//            return -1.0; // 未知距离
//        }
//        double ratio = rssi * 1.0 / txPower;
//        if (ratio < 1.0) {
//            return Math.pow(ratio, 10);
//        } else {
//            return 0.89976 * Math.pow(ratio, 7.7095) + 0.111;
//        }
//    }
//
//    /**
//     * 更新设备位置数据
//     * @param deviceAddress 设备地址
//     * @param rssi 信号强度
//     */
//    private void updateDeviceLocation(String deviceAddress, int rssi) {
//        if (lastLocation == null) return;
//
//        // 估计设备距离
//        double distance = estimateDistance(rssi);
//
//        DeviceLocationData locationData = deviceLocationMap.computeIfAbsent(
//                deviceAddress, k -> new DeviceLocationData());
//
//        // 更新设备位置数据
//        locationData.lastRssi = rssi;
//        locationData.estimatedDistance = distance;
//        locationData.lastUpdate = System.currentTimeMillis();
//        locationData.latitude = lastLocation.getLatitude();
//        locationData.longitude = lastLocation.getLongitude();
//    }
//
//    /**
//     * 获取设备的位置数据
//     * @param deviceAddress 设备地址
//     * @return 设备位置数据，如果没有则返回null
//     */
//    public DeviceLocationData getDeviceLocation(String deviceAddress) {
//        return deviceLocationMap.get(deviceAddress);
//    }
//
//    /**
//     * 获取附近的设备
//     * @param maxDistance 最大距离（米）
//     * @return 设备地址列表
//     */
//    public List<String> getNearbyDevices(double maxDistance) {
//        List<String> nearbyDevices = new ArrayList<>();
//
//        for (Map.Entry<String, DeviceLocationData> entry : deviceLocationMap.entrySet()) {
//            if (entry.getValue().estimatedDistance <= maxDistance) {
//                nearbyDevices.add(entry.getKey());
//            }
//        }
//
//        return nearbyDevices;
//    }
//
//    @Override
//    public void startScan(ScanCallback callback) {
//        if (scanning || bluetoothAdapter == null || bleScanner == null || callback == null) {
//            YoBTSDKLog.e(TAG, "无法启动扫描：参数无效或已在扫描中");
//            return;
//        }
//
//        this.scanCallback = callback;
//
//        try {
//            // 权限检查 - 使用适合当前Android版本的权限
//            boolean hasPermission = true;
//
//            if (Build.VERSION.SDK_INT >= 31) { // Build.VERSION_CODES.S = 31 (Android 12)
//                // Android 12+ 需要BLUETOOTH_SCAN权限
//                hasPermission = ActivityCompat.checkSelfPermission(context,
//                        "android.permission.BLUETOOTH_SCAN") == PackageManager.PERMISSION_GRANTED;
//            } else {
//                // 旧版Android需要位置权限和普通蓝牙权限
//                hasPermission = ActivityCompat.checkSelfPermission(context,
//                        Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED &&
//                        ActivityCompat.checkSelfPermission(context,
//                        Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED;
//            }
//
//            if (!hasPermission) {
//                YoBTSDKLog.e(TAG, "缺少必要的蓝牙权限，无法扫描");
//                return;
//            }
//
//            // 清除历史数据
//            rssiHistory.clear();
//            deviceLocationMap.clear();
//
//            // 使用nRF Connect风格的扫描参数启动扫描
//            bleScanner.startScan(scanFilters, scanSettings, nrfScanCallback);
//            scanning = true;
//
//            // 通知扫描开始
//            if (scanCallback != null) {
//                handler.post(scanCallback::onScanStart);
//            }
//
//            // 设置扫描超时
//            if (scanConfig != null && scanConfig.getScanTimeout() > 0) {
//                handler.postDelayed(this::stopScan, scanConfig.getScanTimeout());
//            }
//        } catch (Exception e) {
//            YoBTSDKLog.e(TAG, "启动扫描时出错: " + e.getMessage());
//            scanning = false;
//        }
//    }
//
//    @Override
//    public void stopScan() {
//        if (!scanning || bluetoothAdapter == null || bleScanner == null) {
//            return;
//        }
//
//        try {
//            // 权限检查 - 使用适合当前Android版本的权限
//            boolean hasPermission = true;
//
//            if (Build.VERSION.SDK_INT >= 31) { // Build.VERSION_CODES.S = 31 (Android 12)
//                // Android 12+ 需要BLUETOOTH_SCAN权限
//                hasPermission = ActivityCompat.checkSelfPermission(context,
//                        "android.permission.BLUETOOTH_SCAN") == PackageManager.PERMISSION_GRANTED;
//            } else {
//                // 旧版Android需要普通蓝牙权限
//                hasPermission = ActivityCompat.checkSelfPermission(context,
//                        Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED;
//            }
//
//            if (!hasPermission) {
//                YoBTSDKLog.e(TAG, "缺少必要的蓝牙权限，无法停止扫描");
//                return;
//            }
//
//            // 停止扫描
//            bleScanner.stopScan(nrfScanCallback);
//            scanning = false;
//
//            // 通知扫描结束
//            if (scanCallback != null) {
//                handler.post(scanCallback::onScanFinish);
//            }
//        } catch (Exception e) {
//            YoBTSDKLog.e(TAG, "停止扫描时出错: " + e.getMessage());
//        } finally {
//            scanning = false;
//        }
//    }
//
//    @Override
//    public void setScanConfig(ScanConfig config) {
//        this.scanConfig = config;
//
//        // 根据扫描配置更新扫描设置
//        if (config != null) {
//            ScanSettings.Builder settingsBuilder = new ScanSettings.Builder();
//
//            // 扫描模式
//            switch (config.getScanMode()) {
//                case ScanConfig.SCAN_MODE_LOW_POWER:
//                    settingsBuilder.setScanMode(ScanSettings.SCAN_MODE_LOW_POWER);
//                    break;
//                case ScanConfig.SCAN_MODE_BALANCED:
//                    settingsBuilder.setScanMode(ScanSettings.SCAN_MODE_BALANCED);
//                    break;
//                case ScanConfig.SCAN_MODE_LOW_LATENCY: // 使用正确的常量
//                default:
//                    settingsBuilder.setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY);
//                    break;
//            }
//
//            // Android 8.0+ 设置
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//                settingsBuilder.setLegacy(false);
//                settingsBuilder.setPhy(ScanSettings.PHY_LE_ALL_SUPPORTED);
//                settingsBuilder.setMatchMode(ScanSettings.MATCH_MODE_AGGRESSIVE);
//            }
//
//            scanSettings = settingsBuilder.build();
//
//            // 设置过滤器
//            scanFilters = new ArrayList<>();
//            if (config.getFeatureCodeFilter() != null && !config.getFeatureCodeFilter().isEmpty()) {
//                // 可以根据需要添加UUID过滤器
//            }
//        }
//    }
//
//    @Override
//    public void close() {
//        stopScan();
//
//        // 停止位置更新
//        if (locationManager != null) {
//            try {
//                locationManager.removeUpdates(locationListener);
//            } catch (SecurityException e) {
//                YoBTSDKLog.e(TAG, "移除位置更新监听器失败: " + e.getMessage());
//            }
//        }
//
//        // 清除数据
//        rssiHistory.clear();
//        deviceLocationMap.clear();
//        handler.removeCallbacksAndMessages(null);
//    }
//
//    /**
//     * 设备位置数据类
//     */
//    public static class DeviceLocationData {
//        public int lastRssi;                // 最后一次RSSI
//        public double estimatedDistance;    // 估计距离（米）
//        public long lastUpdate;             // 最后更新时间
//        public double latitude;             // 设备纬度
//        public double longitude;            // 设备经度
//    }
//}