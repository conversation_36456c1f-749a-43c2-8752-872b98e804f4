package com.ggec.bleservice.setting;

import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-29
 * Description: 
 * 命令基类
 * 负责定义所有命令的通用结构和行为，包括命令状态管理、回调通知机制和基本命令接口
 */
public abstract class Command implements IQueueable {
    private static final String TAG = "Command";
    
    /**
     * 命令结果状态码
     */
    public static final class ResultCode {
        public static final int SUCCESS = 0;    // 成功
        public static final int TIMEOUT = 1;    // 超时
        public static final int RESERVED = 2;   // 预留
        public static final int FAILED = 3;     // 失败
    }

    /**
     * 命令状态枚举
     */
    public enum State {
        QUEUED,     // 已加入队列
        SENDING,    // 发送中
        SENT,       // 已发送
        RECEIVING,  // 接收中
        COMPLETED,  // 已完成
        FAILED      // 失败
    }
    
    // 当前命令状态
    private State state = State.QUEUED;
    
    // 命令ID，用于标识命令
    private final String commandId;
    
    // 命令类型，基于类名
    private final String commandType;
    
    // 命令前缀，用于响应匹配和分发
    private String commandPrefix;

    private boolean isCommandFinish;
    
    // 命令回调管理器实例
    private static final CommandCallbackManager callbackManager = CommandCallbackManager.getInstance();

    /**
     * 命令回调接口
     */
    public interface CommandCallback {
        /**
         * 命令执行完成回调
         * @param command 命令对象
         * @param code 结果状态码
         * @param result 执行结果数据
         */
        void onCommandCompleted(Command command, int code, String result);
        
        /**
         * 命令执行完成回调（带数据）
         * @param command 命令对象
         * @param code 结果状态码
         * @param yobackdata 返回的原始数据，失败时为null
         * @param result 执行结果数据
         */
        void onCommandCompleted(Command command, int code, Object yobackdata, String result);

        /**
         * 命令执行完成回调（带结果值）
         * @param command 命令对象
         * @param code 结果状态码
         * @param resultValue 结果值，表示具体的命令完成状态
         * @param result 执行结果数据
         */
        void onCommandCompleted(Command command, int code, int resultValue, String result);
    }
    
    /**
     * 构造方法
     */
    public Command() {
        // 生成唯一命令ID
        this.commandId = generateCommandId();
        // 设置命令类型
        this.commandType = getClass().getSimpleName();
        // 初始状态为QUEUED
        this.state = State.QUEUED;
    }
    
    /**
     * 生成唯一命令ID
     */
    private String generateCommandId() {
        return getClass().getSimpleName() + "_" + System.currentTimeMillis();
    }
    

    
    /**
     * 获取命令ID
     */
    public String getCommandId() {
        return commandId;
    }
    
    /**
     * 获取命令类型
     */
    public String getCommandType() {
        return commandType;
    }
    
    /**
     * 获取当前状态
     */
    public State getState() {
        return state;
    }
    
    /**
     * 设置状态
     * @param state 新状态
     */
    public void setState(State state) {
        this.state = state;
        YoBTSDKLog.d(TAG, commandId + " 状态变更: " + state);
    }
    
    /**
     * 获取命令前缀，用于响应匹配和分发
     * @return 命令前缀
     */
    public String getCommandPrefix() {
        return commandPrefix;
    }
    
    /**
     * 设置命令前缀
     * @param prefix 命令前缀
     */
    protected void setCommandPrefix(String prefix) {
        this.commandPrefix = prefix;
    }
    
    /**
     * 获取命令数据
     * @return 十六进制格式的命令数据
     */
    public abstract String getCommandData();
    
    /**
     * 解析响应数据
     * @param responseData 响应数据（十六进制格式）
     * @return 解析后的结果
     */
    public abstract String parseResponse(String responseData);

    public boolean isCommandFinish(){
        return isCommandFinish;
    }

    /**
     * 检查响应数据是否匹配当前命令
     * @param responseData 响应数据
     * @return 是否匹配
     */
    public abstract boolean isResponseMatch(String responseData);
    
    /**
     * 获取命令的超时时间（毫秒）
     * @return 超时时间，默认800ms
     */
    public long getTimeoutMs() {
        return 2000;
    }
    
    // ========== 新增的回调注册方法 ==========
    
    /**
     * 为当前命令注册一次性回调
     * @param callback 回调接口
     * @param commandTypeFilter 命令类型过滤器（可为null表示不过滤）
     */
    public void registerOneTimeCallback(CommandCallback callback, String commandTypeFilter) {
        callbackManager.registerCommandCallback(commandId, callback, true, commandTypeFilter);
    }
    
    /**
     * 为当前命令注册一次性回调（无类型过滤）
     * @param callback 回调接口
     */
    public void registerOneTimeCallback(CommandCallback callback) {
        registerOneTimeCallback(callback, commandType);
    }
    
    /**
     * 为当前命令注册持久回调
     * @param callback 回调接口
     * @param commandTypeFilter 命令类型过滤器（可为null表示不过滤）
     */
    public void registerPersistentCallback(CommandCallback callback, String commandTypeFilter) {
        callbackManager.registerCommandCallback(commandId, callback, false, commandTypeFilter);
    }
    
    /**
     * 为当前命令注册持久回调（无类型过滤）
     * @param callback 回调接口
     */
    public void registerPersistentCallback(CommandCallback callback) {
        registerPersistentCallback(callback, commandType);
    }
    
    /**
     * 为当前命令类型注册一次性回调
     * @param callback 回调接口
     */
    public static void registerTypeOneTimeCallback(String commandType, CommandCallback callback) {
        CommandCallbackManager.getInstance().registerTypeCallback(commandType, callback, true);
    }
    
    /**
     * 为当前命令类型注册持久回调
     * @param callback 回调接口
     */
    public static void registerTypePersistentCallback(String commandType, CommandCallback callback) {
        CommandCallbackManager.getInstance().registerTypeCallback(commandType, callback, false);
    }
    
    // ========== 类型安全回调注册方法 ==========
    
    /**
     * 为当前命令注册类型安全的一次性回调
     * @param callback 类型安全的回调接口
     */
    public <T extends Command> void registerTypedOneTimeCallback(TypedCommandCallback<T> callback) {
        callbackManager.registerCommandCallback(commandId, callback, true, callback.getCommandTypeName());
    }
    
    /**
     * 为当前命令注册类型安全的持久回调
     * @param callback 类型安全的回调接口
     */
    public <T extends Command> void registerTypedPersistentCallback(TypedCommandCallback<T> callback) {
        callbackManager.registerCommandCallback(commandId, callback, false, callback.getCommandTypeName());
    }
    
    /**
     * 为指定命令类型注册类型安全的一次性回调
     * @param callback 类型安全的回调接口
     */
    public static <T extends Command> void registerTypedTypeOneTimeCallback(TypedCommandCallback<T> callback) {
        CommandCallbackManager.getInstance().registerTypeCallback(callback.getCommandTypeName(), callback, true);
    }
    
    /**
     * 为指定命令类型注册类型安全的持久回调
     * @param callback 类型安全的回调接口
     */
    public static <T extends Command> void registerTypedTypePersistentCallback(TypedCommandCallback<T> callback) {
        CommandCallbackManager.getInstance().registerTypeCallback(callback.getCommandTypeName(), callback, false);
    }
    
    /**
     * 为全局注册类型安全的一次性回调
     * @param callback 类型安全的回调接口
     */
    public static <T extends Command> void registerTypedGlobalOneTimeCallback(TypedCommandCallback<T> callback) {
        CommandCallbackManager.getInstance().registerGlobalCallback(callback, true, callback.getCommandTypeName());
    }
    
    /**
     * 为全局注册类型安全的持久回调
     * @param callback 类型安全的回调接口
     */
    public static <T extends Command> void registerTypedGlobalPersistentCallback(TypedCommandCallback<T> callback) {
        CommandCallbackManager.getInstance().registerGlobalCallback(callback, false, callback.getCommandTypeName());
    }
    
    /**
     * 移除当前命令的所有回调
     */
    public void removeAllCallbacks() {
        callbackManager.removeCommandCallbacks(commandId);
    }
    
    // ========== 增强的通知方法 ==========
    
    /**
     * 通知命令完成
     * @param code 结果状态码
     * @param result 执行结果
     */
    protected void notifyCompletion(int code, String result) {
        // 通过CommandCallbackManager分发回调
        isCommandFinish = true;
        callbackManager.dispatchCommandCompleted(this, code, result);
    }
    
    /**
     * 通知命令完成（带数据）
     * @param code 结果状态码
     * @param yobackdata 返回的原始数据，失败时为null
     * @param result 附加消息
     */
    protected void notifyCompletion(int code, Object yobackdata, String result) {
        // 通过CommandCallbackManager分发回调
        isCommandFinish = true;
        callbackManager.dispatchCommandCompleted(this, code, yobackdata, result);
    }

    /**
     * 通知命令完成（带结果值）
     * @param code 结果状态码
     * @param resultValue 结果值，表示具体的命令完成状态
     * @param result 执行结果
     */
    protected void notifyCompletion(int code, int resultValue, String result) {
        // 通过CommandCallbackManager分发回调
        isCommandFinish = true;
        callbackManager.dispatchCommandCompleted(this, code, resultValue, result);
    }
    
    /**
     * 通知CommandQueueCenter命令已真正完成，需要清理引用
     * 主要用于聚合命令等需要多次响应的命令
     */
    protected void notifyCommandReallyCompleted(int code) {
        isCommandFinish = true;
        if(code == ResultCode.TIMEOUT) {
          CommandQueueCenter.getInstance().reset();
        }
        CommandQueueCenter.getInstance().onCommandReallyCompleted();
    }
} 