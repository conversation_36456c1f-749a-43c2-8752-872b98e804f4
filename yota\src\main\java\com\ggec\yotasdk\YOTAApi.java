package com.ggec.yotasdk;

import android.content.Context;

import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.OTAStatus;

import java.util.ArrayList;

/**
 * YOTA API接口定义
 * 对外提供OTA升级相关功能
 */
public interface YOTAApi {
    
    /**
     * OTA升级状态枚举
     * 对外暴露的OTA状态，避免外部直接依赖SDK内部类
     */
    enum Status {
        UNKNOWN("未知状态"),
        STARTED("开始升级"),
        UPDATING("升级中"),
        VERIFYING("验证中"),
        SUCCEED("升级成功"),
        FAILED("升级失败"),
        CANCELED("升级取消");
        
        private final String name;
        
        Status(String name) {
            this.name = name;
        }
        
        public String getName() {
            return name;
        }
        
        /**
         * 将内部SDK状态转换为对外状态
         */
        static Status fromOTAStatus(OTAStatus status) {
            if (status == OTAStatus.STATUS_UNKNOWN) return UNKNOWN;
            if (status == OTAStatus.STATUS_STARTED) return STARTED;
            if (status == OTAStatus.STATUS_UPDATING) return UPDATING;
            if (status == OTAStatus.STATUS_VERIFYING) return VERIFYING;
            if (status == OTAStatus.STATUS_SUCCEED) return SUCCEED;
            if (status == OTAStatus.STATUS_FAILED) return FAILED;
            if (status == OTAStatus.STATUS_CANCELED) return CANCELED;
            return UNKNOWN;
        }
    }
    
    /**
     * 开始升级
     * SDK将自动查找已连接的设备和默认固件文件进行升级。
     *
     * @return 是否成功启动升级流程
     */
    boolean startUpgrade();
    
    /**
     * 开始对特定设备的升级
     * @param device 指定进行升级的设备
     * @return 是否成功启动升级流程
     */
    boolean startUpgrade(HmDevice device);

    /**
     * 开始对特定MAC地址的设备进行升级
     *
     * @param macAddress 指定进行升级的设备MAC地址 (格式 XX:XX:XX:XX:XX:XX)
     * @return 是否成功启动升级流程
     */
    boolean startUpgrade(String macAddress);
    
    /**
     * 设置OTA升级文件路径
     * 
     * @param filePath OTA升级文件路径
     */
    void setOtaFilePath(String filePath);
    
    /**
     * 获取已连接的SPP设备列表
     * 
     * @return 已连接的SPP设备列表
     */
    ArrayList<HmDevice> getConnectedSppDevices();
    
    /**
     * 注册升级进度监听器
     * 
     * @param listener 进度监听器
     */
    void setProgressListener(ProgressListener listener);
    
    /**
     * 注册状态回调监听器
     * 
     * @param listener 状态监听器
     */
    void setStatusListener(StatusListener listener);
    
    /**
     * 取消升级
     * 
     * @return 是否成功取消
     */
    boolean cancelUpgrade();
    
    /**
     * 检查SDK是否已初始化
     * 
     * @return SDK是否已初始化
     */
    boolean isInitialized();
    
    /**
     * 检查是否正在进行OTA升级
     * 
     * @return 是否正在升级
     */
    boolean isUpgrading();
    
    /**
     * 获取SDK实例（单例模式）
     * 获取YOTAApi唯一实例的方法
     * 
     * @param context 应用上下文
     * @return YOTAApi实例
     */
    static YOTAApi getInstance(Context context) {
        return YOTAManager.getInstance(context);
    }
    
    /**
     * 进度监听器接口
     */
    interface ProgressListener {
        /**
         * 当进度更新时回调
         * 
         * @param progress 升级进度 (0-100)
         */
        void onProgressChanged(float progress);
    }
    
    /**
     * 状态监听器接口
     */
    interface StatusListener {
        /**
         * 当状态改变时回调
         * 
         * @param status 当前OTA状态
         */
        void onStatusChanged(Status status);
        
        /**
         * 当发生错误时回调
         * 
         * @param errorCode 错误码
         * @param message 错误信息
         */
        void onError(int errorCode, String message);
        
        /**
         * 当升级成功完成时回调
         */
        void onSuccess();
    }
}
