# YOTA SDK 集成指南

| 项目     | 内容       |
| -------- | ---------- |
| 版本号   | V1.0.0.0  |
| 修改日期 | 2025/07/07 |
| 编写人员 | 杨庭锋     |

## 简介
本文档旨在帮助开发者正确、高效地集成和使用YOTA SDK，并规避在实践中遇到的常见问题和"陷阱"。遵循本文档的指引，可以显著减少调试时间，确保OTA功能稳定运行。

---

## 一、 核心集成流程

1.  **获取SDK实例**:
    通过应用程序类获取`YOTAApi`的实例。SDK使用严格的单例模式管理，确保全局只有一个实例。
    ```java
    // 在Activity或Service中，通过应用程序类获取实例（推荐方式）
    YOTAApi yotaApi = ((GGECHSApplication)getApplication()).getOtaApi();
    ```

2.  **设置监听器**:
    为实例设置进度和状态监听器，以接收升级过程中的反馈。
    ```java
    // 设置进度监听器
    yotaApi.setProgressListener(new YOTAApi.ProgressListener() {
        @Override
        public void onProgressChanged(float progress) {
            // 处理进度更新，progress范围为0-100
        }
    });
    
    // 设置状态监听器
    yotaApi.setStatusListener(new YOTAApi.StatusListener() {
        @Override
        public void onStatusChanged(YOTAApi.Status status) {
            // 处理状态变更
            // 可用状态：UNKNOWN, STARTED, UPDATING, VERIFYING, SUCCEED, FAILED, CANCELED
        }
        
        @Override
        public void onError(int errorCode, String message) {
            // 处理错误
        }
        
        @Override
        public void onSuccess() {
            // 处理升级成功
        }
    });
    ```

3.  **准备固件文件**:
    将OTA升级所需的固件文件（如 `test.bin`）放置在App的私有文件目录中。正确的路径为：
    `/storage/emulated/0/Android/data/YOUR_APPLICATION_PACKAGE_NAME/files/test.bin`

4.  **开始升级**:
    调用`startUpgrade`方法启动升级流程。SDK会自动查找已连接的设备并使用默认路径的固件文件。
    ```java
    boolean result = yotaApi.startUpgrade();
    if (result) {
        // 升级流程已启动
    } else {
        // 启动失败
    }
    ```

---

## 二、 关键注意事项（必读）

### 1. SDK初始化与单例模式

YOTA SDK强制使用单例模式进行实例管理，确保全局只有一个实例。SDK内部自动处理所有初始化工作，包括FileUtils和默认路径设置，只需通过getInstance方法获取实例即可。

**获取实例**:
```java
// 应用程序类中的方法
public YOTAApi getOtaApi() {
    if (mOtaApi == null && mOtaEnabled) {
        // 使用YOTAApi接口获取实例（单例模式）
        mOtaApi = YOTAApi.getInstance(getApplicationContext());
    }
    return mOtaApi;
}

// 在Activity中获取实例
YOTAApi yotaApi = ((GGECHSApplication)getApplication()).getOtaApi();
```

### 2. 创建应用私有目录

虽然Android系统会为应用创建私有数据目录，但在某些情况下，具体的`files`子目录可能不会被自动创建。为了确保固件文件能够被稳定地写入和读取，强烈建议在应用首次启动时主动检查并创建该目录。

应用程序类`GGECHSApplication`已包含此功能：

```java
// GGECHSApplication.java 中
private void createPrivateDirectory() {
    File filesDir = getExternalFilesDir(null);
    if (filesDir == null) {
        Log.e(TAG, "无法获取外部存储的私有目录。");
        return;
    }

    if (!filesDir.exists()) {
        Log.d(TAG, "私有目录不存在，正在创建: " + filesDir.getAbsolutePath());
        if (filesDir.mkdirs()) {
            Log.i(TAG, "私有目录创建成功。");
        } else {
            Log.e(TAG, "私有目录创建失败。");
        }
    } else {
        Log.i(TAG, "私有目录已存在: " + filesDir.getAbsolutePath());
    }
}
```

### 3. Android系统权限

这是集成过程中最容易出错的环节。为了适配最新的Android系统特性（如分区存储），必须采用版本化的权限策略。

#### 3.1 清单文件静态声明 (`AndroidManifest.xml`)

请确保您的主应用模块的`AndroidManifest.xml`文件中包含了以下针对不同安卓版本的权限配置。这种方式可以保证App在不同设备上申请最恰当的权限。

```xml
<!-- 蓝牙相关权限 (无需改动) -->
<uses-permission android:name="android.permission.BLUETOOTH" android:maxSdkVersion="30" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" android:maxSdkVersion="30" />
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

<!-- 存储权限：适配分区存储模型 -->
<!-- Android 12L (API 32) 及以下版本使用 -->
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
<!-- Android 9 (API 28) 及以下版本使用 -->
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="28" />

<!-- Android 13 (API 33) 及以上版本的媒体权限 -->
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
<uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
<uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

<!-- 通话状态权限 (关键) -->
<uses-permission android:name="android.permission.READ_PHONE_STATE" />
```

**重要**: 请务必**删除**`<application>`标签中的 `android:requestLegacyExternalStorage="true"` 属性，以完全启用分区存储。

#### 3.2 运行时动态请求

对于Android 6.0 (API 23) 及以上系统，必须在代码中根据系统版本动态请求用户授权。以下是一个适配了新版权限的完整请求模板：

```java
public class YourActivity extends AppCompatActivity {

    private static final int REQUEST_PERMISSIONS_CODE = 101;

    // 在按钮点击或Activity启动时调用此方法
    private void checkAndRequestPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            List<String> permissionsToRequest = new ArrayList<>();
            String[] requiredPermissions;

            // 根据Android版本确定需要请求的权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) { // Android 13 (API 33)
                requiredPermissions = new String[]{
                        Manifest.permission.READ_MEDIA_IMAGES,
                        Manifest.permission.READ_MEDIA_VIDEO,
                        Manifest.permission.READ_MEDIA_AUDIO,
                        Manifest.permission.READ_PHONE_STATE
                };
            } else { // Android 6 ~ 12
                requiredPermissions = new String[]{
                        Manifest.permission.READ_EXTERNAL_STORAGE,
                        Manifest.permission.READ_PHONE_STATE
                };
            }

            for (String permission : requiredPermissions) {
                if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                    permissionsToRequest.add(permission);
                }
            }

            if (!permissionsToRequest.isEmpty()) {
                // 请求尚未授予的权限
                ActivityCompat.requestPermissions(this,
                        permissionsToRequest.toArray(new String[0]),
                        REQUEST_PERMISSIONS_CODE);
            } else {
                // 所有权限都已被授予，直接执行业务逻辑
                startOtaUpgrade();
            }
        } else {
            // Android 6.0 以下系统，权限在安装时已授予
            startOtaUpgrade();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_PERMISSIONS_CODE) {
            boolean allPermissionsGranted = true;
            for (int grantResult : grantResults) {
                if (grantResult != PackageManager.PERMISSION_GRANTED) {
                    allPermissionsGranted = false;
                    break;
                }
            }

            if (allPermissionsGranted) {
                // 用户授予了所有权限
                startOtaUpgrade();
            } else {
                // 用户拒绝了至少一个权限
                Toast.makeText(this, "需要所有权限才能进行OTA升级", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private void startOtaUpgrade() {
        // 获取OTA API实例
        YOTAApi yotaApi = ((GGECHSApplication)getApplication()).getOtaApi();
        
        // 开始OTA升级
        boolean result = yotaApi.startUpgrade();
        if (result) {
            // 升级流程已启动
        } else {
            // 启动失败
        }
    }
}
```

---

## 三、 API 说明

### 1. YOTAApi 接口方法

| 方法 | 描述 |
|------|------|
| `static YOTAApi getInstance(Context context)` | 获取YOTAApi单例实例，SDK内部自动初始化 |
| `boolean startUpgrade()` | 使用默认配置启动升级，自动查找已连接设备和默认固件文件 |
| `void setProgressListener(ProgressListener listener)` | 设置进度监听器 |
| `void setStatusListener(StatusListener listener)` | 设置状态监听器 |
| `boolean cancelUpgrade()` | 取消当前升级 |
| `boolean isInitialized()` | 检查SDK是否已初始化 |
| `boolean isUpgrading()` | 检查是否正在升级 |
| `ArrayList<HmDevice> getConnectedSppDevices()` | 获取已连接的SPP设备列表 |
| `void setOtaFilePath(String filePath)` | 设置OTA升级文件路径 |

### 2. Status 状态枚举

YOTAApi中定义了以下OTA状态枚举：

| 状态 | 名称 | 描述 |
|------|------|------|
| `UNKNOWN` | 未知状态 | 初始状态或无法识别的状态 |
| `STARTED` | 开始升级 | 升级流程已启动 |
| `UPDATING` | 升级中 | 正在传输固件数据 |
| `VERIFYING` | 验证中 | 固件传输完成，正在验证 |
| `SUCCEED` | 升级成功 | 升级流程成功完成 |
| `FAILED` | 升级失败 | 升级过程中发生错误 |
| `CANCELED` | 升级取消 | 升级已被用户或系统取消 |

---

## 四、 常见问题与日志分析

当OTA失败时，请重点关注Logcat中的以下关键日志：

1.  **错误：`OTA file not found`**
    *   **原因**: App在错误的路径下寻找固件文件。
    *   **解决方案**: 确认固件文件放置在正确的位置：`/storage/emulated/0/Android/data/YOUR_PACKAGE_NAME/files/test.bin`

2.  **错误：`java.io.FileNotFoundException: ... open failed: EACCES (Permission denied)`**
    *   **原因**: App没有获取到**运行时**的存储读取权限(`READ_EXTERNAL_STORAGE`)。
    *   **解决方案**: 参照`3.2 运行时动态请求`章节，实现动态权限请求逻辑。

3.  **崩溃：`java.lang.SecurityException: ... has no android.permission.READ_PHONE_STATE`**
    *   **原因**: App没有获取到`READ_PHONE_STATE`权限，但SDK内部尝试检查通话状态以防止升级被打断。
    *   **解决方案**: 参照第三节，在清单文件中声明并在运行时请求该权限。

4.  **错误：`Activity类中直接使用YOTAManager`**
    *   **原因**: Activity直接引用了SDK内部实现类，造成高耦合。
    *   **解决方案**: 通过应用程序类获取YOTAApi接口实例：`((GGECHSApplication)getApplication()).getOtaApi()`

5.  **错误：`多个SDK实例导致的状态不一致`**
    *   **原因**: 尝试通过非单例方式创建多个实例。
    *   **解决方案**: 只使用`YOTAApi.getInstance(context)`或应用程序类的`getOtaApi()`方法获取实例。

6.  **错误：`FileUtils not initialized`**
    *   **原因**: 此错误已由SDK自动解决。SDK初始化时现在自动初始化FileUtils。
    *   **解决方案**: 无需特殊处理，SDK内部已自动处理。

遵循以上规范，即可解决绝大部分集成和使用中的问题。 