package com.bes.sdk.scan;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public interface ScanFilter {
    /**
     * Check if given param object is useful
     * @param scanRecord
     * @param parser scanRecord parser.
     * @return true param object is useful, false ignore the given param object.
     */
    boolean filter(@NonNull Object scanRecord, @Nullable Object parser);
}
