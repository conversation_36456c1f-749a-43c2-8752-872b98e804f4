<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:padding="8dp">

    <ImageView
        android:id="@+id/iv_ble_bluetooth_icon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/ic_public_bluetooth"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/layout_device_info"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tv_ble_device_rssi"
        app:layout_constraintStart_toEndOf="@+id/iv_ble_bluetooth_icon"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_ble_device_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/font_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="1dp"
            tools:text="蓝牙设备名称" />

        <TextView
            android:id="@+id/tv_ble_device_address"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/colorAccent"
            android:textSize="13sp"
            android:layout_marginBottom="1dp"
            tools:text="00:11:22:33:44:55" />

        <TextView
            android:id="@+id/tv_ble_classic_mac"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/font_secondary"
            android:textSize="11sp"
            android:layout_marginBottom="1dp"
            tools:text="经典蓝牙MAC地址: 00:11:22:33:44:55" />

        <!-- 颜色显示TextView - 已注释以减少卡片高度 -->
        <!--
        <TextView
            android:id="@+id/tv_ble_color"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/font_secondary"
            android:textSize="12sp"
            tools:text="颜色: 黑色" />
        -->

        <!-- 特征码显示TextView - 已注释以减少卡片高度 -->
        <!--
        <TextView
            android:id="@+id/tv_ble_feature_code"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/font_secondary"
            android:textSize="12sp"
            tools:text="featurecode: AB12CD34" />
        -->

        <TextView
            android:id="@+id/tv_spp_connection_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/font_secondary"
            android:textSize="11sp"
            tools:text="经典蓝牙连接状态: 未连接" />

    </LinearLayout>

    <TextView
        android:id="@+id/tv_ble_device_rssi"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/font_secondary"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/layout_device_info"
        app:layout_constraintBottom_toBottomOf="@+id/layout_device_info"
        tools:text="-75 dBm" />

</androidx.constraintlayout.widget.ConstraintLayout> 