package com.ggec.bleservice.setting.devicecmd;

import android.os.Handler;
import android.os.Looper;

import com.ggec.bleservice.YoBLEApi;
import com.ggec.bleservice.setting.Command;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-29
 * Description:
 * 大状态获取命令 (聚合模式 V3 - 清单驱动)
 *
 * 这个命令用于触发设备返回其全部状态。它采用清单驱动和动态超时机制来优化速度和可靠性。
 * 1.  **清单机制**: 命令启动时，会生成一个包含所有预期数据包类型（如电量、固件等）的清单。
 * 2.  **动态超时**:
 *     -   **初始超时 (500ms)**: 等待第一个包的到达。
 *     -   **包间短超时 (150ms)**: 收到一个包后，等待下一个包的短时间窗口。
 * 3.  **快速完成**: 一旦清单上的所有项目都被接收，命令会立即成功返回，无需等待超时。
 *
 * 这个新机制旨在显著减少获取完整状态所需的平均时间，同时保证数据的完整性。
 */
public class GlobalStatusCommand extends Command {
    private static final String TAG = "GlobalStatusCommand";

    // 命令前缀
    private static final String PREFIX_GLOBAL_STATUS = "99EC98";
    private static final String PREFIX_BATTERY = "99EC80";
    private static final String PREFIX_UNKNOWN_95 = "99EC95";

    // 发送命令数据
    private static final String COMMAND_DATA = "99EC98000100" + "1234";

    // 功能类型标识 (public, 供外部解析器使用)
    public static final byte TYPE_WEAR_DETECTION = (byte) 0x87;  // 佩戴检测
    public static final byte TYPE_VOICE_WAKEUP = (byte) 0x88;    // 语音控制
    public static final byte TYPE_GAME_MODE = (byte) 0x89;       // 游戏模式
    public static final byte TYPE_FALL_ALERT = (byte) 0x8A;      // 掉落提醒
    public static final byte TYPE_SOUND_QUALITY = (byte) 0x81;   // 音质模式
    public static final byte TYPE_VOLUME_ADAPTIVE = (byte) 0x82; // 音量自适应
    public static final byte TYPE_EQ_MODE = (byte) 0x83;         // EQ模式
    public static final byte TYPE_VOLUME_REMIND = (byte) 0x84;   // 听力保护/音量提醒
    public static final byte TYPE_KEY_CONTROL = (byte) 0x85;     // 按键控制
    public static final byte TYPE_FIRMWARE = (byte) 0x94;        // 固件版本


    // --- 聚合状态管理 (V3) ---
    public enum AggregationState { IDLE, WAITING_FOR_FIRST_PACKET, AGGREGATING, FINISHED }
    private AggregationState currentState = AggregationState.IDLE;

    // 清单，用于跟踪待接收的数据包类型
    private final Set<String> manifest = new HashSet<>();
    private final Map<String, String> dictResult = new HashMap<>();

    // 动态超时 - 针对实际网络延迟进行优化
    private static final long INITIAL_TIMEOUT_MS = 800;  // 等待第一个包的超时，增加以应对队列延迟
    private static final long INTER_PACKET_TIMEOUT_MS = 500; // 包之间的超时，增加容错时间
    private static final long FINAL_MAIN_PACKET_TIMEOUT_MS = 1200; // 专用于等待主包的最终超时，增加等待时间

    private final Handler timeoutHandler = new Handler(Looper.getMainLooper());
    private final AtomicBoolean completionNotified = new AtomicBoolean(false);
    private final Runnable timeoutRunnable = this::onTimeout;
    private final Runnable finalTimeoutRunnable = this::onFinalTimeout;
    
    // 防止CommandQueueCenter过早设置COMPLETED状态的标志
    private volatile boolean allowStateChange = true;

    /**
     * 构造方法
     */
    public GlobalStatusCommand() {
        super();
        setCommandPrefix(PREFIX_GLOBAL_STATUS); // 主命令前缀
    }
    
    /**
     * 重写setState方法，防止CommandQueueCenter过早设置COMPLETED状态
     * 只有在聚合真正完成时才允许设置COMPLETED状态
     */
    @Override
    public void setState(State state) {
        // 如果试图设置为COMPLETED，但聚合还没完成，则阻止此操作
        if (state == State.COMPLETED && currentState != AggregationState.FINISHED && !allowStateChange) {
            YoBTSDKLog.d(TAG, "阻止过早的COMPLETED状态设置，聚合状态: " + currentState);
            return;
        }
        
        // 其他状态变更正常处理
        super.setState(state);
    }
    
    /**
     * 获取当前聚合状态
     * @return 当前聚合状态
     */
    public AggregationState getCurrentState() {
        return currentState;
    }

    @Override
    public String getCommandData() {
        // 在执行命令前，检查设备是否就绪
        if (!YoBLEApi.getInstance().isDeviceReady()) {
            YoBTSDKLog.w(TAG, "设备未就绪，无法执行 GlobalStatusCommand。");
            // 直接通知命令失败，并给出原因
            notifyCompletion(ResultCode.FAILED, "设备未就绪");
            return null; // 返回null以阻止命令发送
        }
        
        // 1. 重置所有状态
        this.currentState = AggregationState.WAITING_FOR_FIRST_PACKET;
        this.dictResult.clear();
        this.completionNotified.set(false);
        this.allowStateChange = false; // 禁用COMPLETED状态的过早设置

        // 2. 初始化清单，定义我们期望收到的所有数据包类型
        this.manifest.clear();
        this.manifest.add(PREFIX_GLOBAL_STATUS);
        this.manifest.add(PREFIX_BATTERY);
        this.manifest.add(PREFIX_UNKNOWN_95);

        // 3. 启动初始超时，等待第一个数据包
        timeoutHandler.postDelayed(timeoutRunnable, INITIAL_TIMEOUT_MS);
        //YoBTSDKLog.d(TAG, "命令已启动，等待第一个包，超时: " + INITIAL_TIMEOUT_MS + "ms");

        return COMMAND_DATA;
    }

    /**
     * 仅当命令未终结时，才匹配响应
     */
    @Override
    public boolean isResponseMatch(String responseData) {
        if (currentState == AggregationState.FINISHED || responseData == null) {
            return false;
        }
        return responseData.startsWith(PREFIX_GLOBAL_STATUS) ||
                responseData.startsWith(PREFIX_BATTERY) ||
                responseData.startsWith(PREFIX_UNKNOWN_95);
    }

    /**
     * 解析响应并更新清单。如果清单完成，则立即结束命令。
     */
    @Override
    public String parseResponse(String responseData) {
        if (currentState == AggregationState.FINISHED) {
            return "命令已完成，忽略后续包";
        }

        // 收到任何包都意味着设备有响应，切换到聚合状态
        if (currentState == AggregationState.WAITING_FOR_FIRST_PACKET) {
            currentState = AggregationState.AGGREGATING;
        }

        // 取消旧的超时，为新的一轮包间超时做准备
        timeoutHandler.removeCallbacks(timeoutRunnable);

        YoBTSDKLog.d(TAG, "收到数据包: " + responseData);

        try {
            if (responseData.startsWith(PREFIX_GLOBAL_STATUS)) {
                dictResult.putAll(parseGlobalStatusPacket(responseData));
                manifest.remove(PREFIX_GLOBAL_STATUS);
                timeoutHandler.removeCallbacks(finalTimeoutRunnable); // 如果主包到了，就取消专门等它的那个超时
            } else if (responseData.startsWith(PREFIX_BATTERY)) {
                dictResult.putAll(parseBatteryPacket(responseData));
                // 电池包可能回来多个（左右耳、充电仓），但只要收到一个，就认为满足了清单要求
                manifest.remove(PREFIX_BATTERY);
            } else if (responseData.startsWith(PREFIX_UNKNOWN_95)) {
                dictResult.putAll(parse95Packet(responseData));
                manifest.remove(PREFIX_UNKNOWN_95);
            }
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "解析数据包失败: " + responseData, e);
        }

        // 检查清单是否已全部完成
        if (manifest.isEmpty()) {
            YoBTSDKLog.i(TAG, "清单完成，所有预期数据包均已收到。");
            finalizeCommand(ResultCode.SUCCESS, "聚合成功");
        } else {
            // 如果清单未完成，则重置包间短超时，等待下一个包
            //YoBTSDKLog.d(TAG, "清单未完成，等待剩余项: " + manifest + ". 重置包间超时 " + INTER_PACKET_TIMEOUT_MS + "ms");
            timeoutHandler.postDelayed(timeoutRunnable, INTER_PACKET_TIMEOUT_MS);
        }

        return "数据块聚合中..."; // 中间状态
    }

    /**
     * 当超时发生时调用
     */
    private void onTimeout() {
        if (currentState == AggregationState.WAITING_FOR_FIRST_PACKET) {
            YoBTSDKLog.e(TAG, "初始超时！未收到任何数据包。");
            finalizeCommand(ResultCode.TIMEOUT, "获取状态超时，设备无响应");
        } else {
            // 包间超时，意味着快速数据流中断
            if (manifest.contains(PREFIX_GLOBAL_STATUS) && manifest.size() == 1) {
                // 如果清单中只剩下主包，说明快速包已收完，现在专门等待主包
                //YoBTSDKLog.d(TAG, "快速包聚合完毕，特别等待主状态包，超时: " + FINAL_MAIN_PACKET_TIMEOUT_MS + "ms");
                timeoutHandler.postDelayed(finalTimeoutRunnable, FINAL_MAIN_PACKET_TIMEOUT_MS);

            } else if (manifest.isEmpty()) {
                // 这种情况理论上不会发生，因为清单为空时会立即完成，但作为保险
                //YoBTSDKLog.i(TAG, "包间超时，但清单已完成，视为成功。");
                finalizeCommand(ResultCode.SUCCESS, "聚合成功");
            } else {
                YoBTSDKLog.w(TAG, "包间超时！数据不完整，缺失项: " + manifest);
                // 数据不完整时，我们将其标记为超时失败，以确保只有在所有数据都收到时才算成功。
                finalizeCommand(ResultCode.TIMEOUT, "聚合超时，数据不完整");
            }
        }
    }

    /**
     * 当最终超时（专门等待主数据包）发生时调用
     */
    private void onFinalTimeout() {
        YoBTSDKLog.e(TAG, "最终超时！主状态包 (" + PREFIX_GLOBAL_STATUS + ") 未在规定时间内到达。");
        // 主包丢失，标记为超时失败，不返回部分数据。
        finalizeCommand(ResultCode.TIMEOUT, "聚合失败，主状态包丢失");
    }

    /**
     * 终结命令，整理并发送最终结果
     * @param code 结果码
     * @param message 结果消息
     */
    private void finalizeCommand(int code, String message) {
        // 确保所有回调和定时器都被清理
        timeoutHandler.removeCallbacks(timeoutRunnable);
        timeoutHandler.removeCallbacks(finalTimeoutRunnable);
        currentState = AggregationState.FINISHED;
        
        // 允许状态变更，并设置最终状态
        allowStateChange = true;
        if (code == ResultCode.SUCCESS) {
            super.setState(State.COMPLETED);
        } else {
            super.setState(State.FAILED);
        }

        if (completionNotified.compareAndSet(false, true)) {
            // 在通知完成前，检查部分解析失败的情况
            if (code == ResultCode.SUCCESS && !dictResult.isEmpty()) {
                long nullCount = dictResult.values().stream().filter(v -> v == null).count();
                if (nullCount >= 2) {
                    YoBTSDKLog.w(TAG, "部分状态解析失败（" + nullCount + "项），请检查设备状态。聚合结果将继续返回。");
                }
            }
            
            if (dictResult.isEmpty() && code == ResultCode.SUCCESS) {
                YoBTSDKLog.w(TAG, "聚合成功，但未解析出任何有效数据");
                notifyCompletion(ResultCode.FAILED, "无有效数据");
            } else if (code == ResultCode.SUCCESS) {
                YoBTSDKLog.i(TAG, "聚合完成，返回最终结果 (" + message + ")，共 " + dictResult.size() + " 项");
                notifyCompletion(ResultCode.SUCCESS, new HashMap<>(dictResult), message);
            } else {
                YoBTSDKLog.e(TAG, "超时，聚合失败: " + message);
                notifyCompletion(code, message);
            }
            
            // 通知CommandQueueCenter聚合命令真正完成
            notifyCommandReallyCompleted(code);
        }
    }


    /**
     * 解析全局状态数据包 (99EC98...)
     * @param responseData 响应数据
     * @return 包含所有解析出的状态项的Map
     */
    public static Map<String, String> parseGlobalStatusPacket(String responseData) {
        Map<String, String> resultMap = new HashMap<>();
        if (responseData == null || !responseData.startsWith(PREFIX_GLOBAL_STATUS) || responseData.length() < 14) {
            YoBTSDKLog.w(TAG, "全局状态包格式错误或长度不足");
            return resultMap;
        }

        try {
            int startIndex = PREFIX_GLOBAL_STATUS.length();
            String lengthHex = responseData.substring(startIndex, startIndex + 4);
            int dataLengthInBytes = Integer.parseInt(lengthHex, 16);
            int dataLengthInChars = dataLengthInBytes * 2;

            int dataStartIndex = startIndex + 4;
            int dataEndIndex = dataStartIndex + dataLengthInChars;

            if (responseData.length() < dataEndIndex) {
                YoBTSDKLog.w(TAG, "全局状态包数据段不完整");
                return resultMap;
            }

            String statusData = responseData.substring(dataStartIndex, dataEndIndex);
            parseAllStatusItems(statusData, resultMap);
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "解析全局状态包失败", e);
        }
        return resultMap;
    }

    /**
     * 解析电池信息数据包 (99EC80...)
     * @param responseData 响应数据
     * @return 包含电池信息的Map
     */
    public static Map<String, String> parseBatteryPacket(String responseData) {
        Map<String, String> resultMap = new HashMap<>();
        // 格式: 99EC80 0002 01 5B 1234 (类型01, 电量5B)
        if (responseData == null || !responseData.startsWith(PREFIX_BATTERY) || responseData.length() < 16) {
            YoBTSDKLog.w(TAG, "电池信息包长度不足");
            return resultMap;
        }
        try {
            String typeHex = responseData.substring(10, 12); // "01"左耳, "02"右耳, "03"充电仓
            String valueHex = responseData.substring(12, 14);
            int value = Integer.parseInt(valueHex, 16);

            // 验证电量值的有效性，如果无效则设为"--"
            String batteryValue;
            if (value >= 0 && value <= 100) {
                batteryValue = String.valueOf(value);
            } else {
                YoBTSDKLog.w(TAG, "电池电量超出有效范围: " + value + "，设置为--");
                batteryValue = "--";
            }

            String key = String.format("80_%s", typeHex); // 使用 %s 保持 "01", "02" 等格式
            resultMap.put(key, batteryValue);
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "解析电池信息包失败", e);
            // 解析失败时，根据类型设置默认的"--"值
            try {
                String typeHex = responseData.substring(10, 12);
                String key = String.format("80_%s", typeHex);
                resultMap.put(key, "--");
            } catch (Exception ex) {
                // 如果连类型都无法解析，则忽略
                YoBTSDKLog.e(TAG, "无法解析电池包类型", ex);
            }
        }
        return resultMap;
    }

    /**
     * 解析未知的95类型数据包 (99EC95...)
     * @param responseData 响应数据
     * @return 包含解析出的数据的Map
     */
    public static Map<String, String> parse95Packet(String responseData) {
        Map<String, String> resultMap = new HashMap<>();
        // 格式: 99EC95 0001 00 1234
        if (responseData == null || !responseData.startsWith(PREFIX_UNKNOWN_95) || responseData.length() < 16) {
            YoBTSDKLog.w(TAG, "95类型包长度不足");
            return resultMap;
        }
        try {
            String valueHex = responseData.substring(10, 12);
            resultMap.put("95", valueHex);
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "解析95类型包失败", e);
        }
        return resultMap;
    }

    /**
     * 解析状态数据字符串，提取所有TLV格式的状态项，并填充到结果Map中
     * @param statusData 状态数据字符串
     * @param resultMap  用于存储结果的Map
     */
    private static void parseAllStatusItems(String statusData, Map<String, String> resultMap) {
        int index = 0;
        while (index < statusData.length()) {
            if (index + 6 > statusData.length()) {
                break;
            }
            try {
                String typeHex = statusData.substring(index, index + 2);
                String lengthHex = statusData.substring(index + 2, index + 4);
                byte type = (byte) Integer.parseInt(typeHex, 16);
                int length = Integer.parseInt(lengthHex, 16);

                if (index + 4 + length * 2 > statusData.length()) {
                    break;
                }
                String valueHex = statusData.substring(index + 4, index + 4 + length * 2);
                parseSingleStatusItem(type, valueHex, resultMap);
                index += 4 + length * 2;
            } catch (Exception e) {
                YoBTSDKLog.e(TAG, "解析状态项时出错，跳过当前项", e);
                index += 2; // 尝试移动到下一个可能的项
            }
        }
    }

    /**
     * 解析单个状态项并将其放入结果Map
     * @param type 状态类型
     * @param valueHex 状态值（十六进制字符串）
     * @param resultMap 用于存储结果的Map
     */
    private static void parseSingleStatusItem(byte type, String valueHex, Map<String, String> resultMap) {
        String typeKey = String.format("%02X", type);
        try {
            switch (type) {
                case TYPE_WEAR_DETECTION:
                    resultMap.put(typeKey, "01".equals(valueHex) ? "01" : "00");
                    break;
                case TYPE_VOICE_WAKEUP:
                    resultMap.put(typeKey, "01".equals(valueHex) ? "01" : "00");
                    break;
                case TYPE_GAME_MODE:
                    resultMap.put(typeKey, "01".equals(valueHex) ? "01" : "00");
                    break;
                case TYPE_FALL_ALERT:
                    resultMap.put(typeKey, "01".equals(valueHex) ? "01" : "00");
                    break;
                case TYPE_SOUND_QUALITY:
                    resultMap.put(typeKey, "01".equals(valueHex) ? "01" : "00");
                    break;
                case TYPE_VOLUME_ADAPTIVE:
                    resultMap.put(typeKey, "01".equals(valueHex) ? "01" : "00");
                    break;
                case TYPE_EQ_MODE:
                    resultMap.put(typeKey, valueHex);
                    break;
                case TYPE_VOLUME_REMIND:
                    resultMap.put(typeKey, valueHex.endsWith("01") ? "01" : "00");
                    break;
                case TYPE_KEY_CONTROL:
                    parseKeyControlStatus(valueHex, resultMap);
                    break;
                case TYPE_FIRMWARE:
                    StringBuilder output = new StringBuilder();
                    for (int i = 0; i < valueHex.length(); i += 2) {
                        String str = valueHex.substring(i, i + 2);
                        output.append((char) Integer.parseInt(str, 16));
                    }
                    resultMap.put(typeKey, output.toString());
                    break;
                default:
                    // 对于未知的类型，可以选择记录或忽略
                    YoBTSDKLog.w(TAG, "发现未知的状态类型: " + typeKey);
                    break;
            }
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "解析状态项失败: 类型=" + typeKey + ", 值=" + valueHex, e);
            // 发生异常时，如果不是按键控制（它有自己的错误处理），则将值设为null
            if (type != TYPE_KEY_CONTROL) {
                resultMap.put(typeKey, null);
            }
        }
    }

    /**
     * 解析按键控制状态并将其放入结果Map
     * @param keyControlData 按键控制数据
     * @param resultMap 用于存储结果的Map
     */
    private static void parseKeyControlStatus(String keyControlData, Map<String, String> resultMap) {
        String key0 = String.format("%02X_0", TYPE_KEY_CONTROL);
        String key1 = String.format("%02X_1", TYPE_KEY_CONTROL);
        String key2 = String.format("%02X_2", TYPE_KEY_CONTROL);
        String key3 = String.format("%02X_3", TYPE_KEY_CONTROL);

        if (keyControlData != null && keyControlData.length() >= 8) {
            try {
                resultMap.put(key0, keyControlData.substring(0, 2));
                resultMap.put(key1, keyControlData.substring(2, 4));
                resultMap.put(key2, keyControlData.substring(4, 6));
                resultMap.put(key3, keyControlData.substring(6, 8));
            } catch (Exception e) {
                YoBTSDKLog.e(TAG, "解析按键控制状态失败: " + keyControlData, e);
                resultMap.put(key0, null);
                resultMap.put(key1, null);
                resultMap.put(key2, null);
                resultMap.put(key3, null);
            }
        } else {
            YoBTSDKLog.w(TAG, "按键控制数据格式不正确或长度不足: " + keyControlData);
            resultMap.put(key0, null);
            resultMap.put(key1, null);
            resultMap.put(key2, null);
            resultMap.put(key3, null);
        }
    }
}