package com.ggec.bleservice.scanner;

import android.os.ParcelUuid;

import com.ggec.yobtsdkserver.utils.HexUtil;

import java.nio.ByteBuffer;
import java.util.List;
import java.util.UUID;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-17
 * Description:
 * 蓝牙扫描数据解析器
 * 用于从服务UUID解析BLE扫描记录数据的辅助类。
 */
public class ScanDataParser {

    /**
     * 完整的FeatureCode
     */
    private static final String FULL_FEATURE_CODE = "86103800900000100000fffe";
    private static final byte[] FULL_FEATURE_CODE_PREFIX_BYTES = HexUtil.hexStringToBytes(FULL_FEATURE_CODE.substring(0, 18)); // 前9个字节

    /**
     * 从扫描结果的服务UUID列表中解析我们自定义的数据。
     *
     * @param serviceUuids The service UUID list from the scan record.
     * @return 解析后的数据
     */
    public static ParsedAdvertisingData parseFromServiceUuid(List<ParcelUuid> serviceUuids) {
        if (serviceUuids == null || serviceUuids.isEmpty()) {
            return null;
        }

        for (ParcelUuid parcelUuid : serviceUuids) {
            ParsedAdvertisingData parsedData = parseSingleUuid(parcelUuid.getUuid());
            if (parsedData != null) {
                return parsedData; // Found and parsed a matching UUID
            }
        }
        return null; // No matching UUID found
    }

    /**
     * 解析单个UUID
     * @param uuid
     * @return
     */
    private static ParsedAdvertisingData parseSingleUuid(UUID uuid) {
        if (uuid == null) {
            return null;
        }

        ByteBuffer bb = ByteBuffer.wrap(new byte[16]);
        bb.putLong(uuid.getMostSignificantBits());
        bb.putLong(uuid.getLeastSignificantBits());
        byte[] uuidBytes = bb.array();

        // 1. Feature Code 解析和校验
        byte[] featureCodeBytes = new byte[9];
        // The feature code is in the last 9 bytes of the UUID
        System.arraycopy(uuidBytes, 7, featureCodeBytes, 0, 9);

        // 将字节顺序反转
        byte[] reversedFeatureCodeBytes = new byte[9];
        for (int i = 0; i < 9; i++) {
            reversedFeatureCodeBytes[i] = featureCodeBytes[8 - i];
        }

        // 比较前缀是否匹配
        boolean featureMatch = true;
        for (int i = 0; i < FULL_FEATURE_CODE_PREFIX_BYTES.length; i++) {
            if (reversedFeatureCodeBytes[i] != FULL_FEATURE_CODE_PREFIX_BYTES[i]) {
                featureMatch = false;
                break;
            }
        }

        if (!featureMatch) {
            return null;
        }
        
        String featureCode = FULL_FEATURE_CODE;

        // 2. MAC地址解析 (前6个字节)
        byte[] macBytes = new byte[6];
        System.arraycopy(uuidBytes, 0, macBytes, 0, 6);

        StringBuilder macAddressBuilder = new StringBuilder();
        // The MAC address is stored in reverse order in the UUID
        for (int i = macBytes.length - 1; i >= 0; i--) {
            macAddressBuilder.append(String.format("%02X", macBytes[i]));
            if (i != 0) {
                macAddressBuilder.append(":");
            }
        }
        String macAddress = macAddressBuilder.toString();

        // 3. 颜色解析 (第7个字节)
        byte[] colorByte = new byte[1];
        colorByte[0] = uuidBytes[6];
        String color = HexUtil.bytesToHexString(colorByte);

        return new ParsedAdvertisingData(featureCode, color, macAddress, uuid);
    }
} 