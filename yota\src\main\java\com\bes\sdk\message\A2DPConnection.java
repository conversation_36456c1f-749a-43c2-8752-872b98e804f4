package com.bes.sdk.message;

import com.bes.sdk.device.HmDevice;

import java.io.Serializable;
import java.util.List;

/**
 * Anc status definition
 */
public class A2DPConnection implements Serializable
{
    /**
     * A2DP connected status
     * <li>true - connected;
     * <li>false - disconnected;
     */
    private boolean connected;

    /**
     * Address list of phones which A2DP connected with speaker/headphone.
     */
    private List<String> addressList;

    public boolean isConnected() {
        return connected;
    }

    public void setConnected(boolean connected) {
        this.connected = connected;
    }

    /**
     * Get address list of phones which A2DP connected with speaker/headphone.
     * @return address list of phones which A2DP connected with speaker/headphone.
     */
    public List<String> getAddressList() {
        return addressList;
    }

    public void setAddressList(List<String> addressList) {
        this.addressList = addressList;
    }

    @Override
    public String toString() {
        return "A2DPConnection{" +
                "connected=" + connected +
                '}';
    }
}
