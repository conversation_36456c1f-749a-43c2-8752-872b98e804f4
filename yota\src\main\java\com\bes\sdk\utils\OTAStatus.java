package com.bes.sdk.utils;

public enum OTAStatus
{
    STATUS_UNKNOWN     (-1, "Status Unknown"),
    STATUS_STARTED     (0, "Status Started"),
    STATUS_UPDATING     (1, "Status Updating"),
    STATUS_PAUSED     (2, "Status Paused"),
    STATUS_CANCELED    (3, "Status Canceled"),
    STATUS_VERIFYING    (4, "Status Verifying"),
    STATUS_VERIFIED    (5, "Status Verifyed"),
    STATUS_FAILED      (6, "Status Failed"),
    STATUS_SUCCEED     (7, "Status Succeed"),
    STATUS_REBOOT      (8, "Status Reboot");

    private int mValue;
    private String mName;

    OTAStatus(int value, String name) {
        mValue = value;
        mName = name;
    }

    public int getValue() {
        return mValue;
    }

    public String getName() {
        return mName;
    }
}