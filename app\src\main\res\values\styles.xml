<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 沉浸式状态栏和导航栏样式 -->
    <style name="ImmersiveSystemBars">
        <item name="android:background">@color/comp_background_gray</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
    </style>
    
    <!-- 统一的开关样式 - 与MusicSettingsActivity中的开关大小一致 -->
    <style name="StandardSwitch">
        <item name="android:layout_width">50dp</item>
        <item name="android:layout_height">25dp</item>
    </style>
    
    <!-- 标签页文字样式 -->
    <style name="TabTextAppearance" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">normal</item>
    </style>
    
    <!-- 选中标签页文字样式 -->
    <style name="TabTextAppearanceSelected" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>
</resources> 