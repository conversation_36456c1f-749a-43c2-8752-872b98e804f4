package com.ggec.bleservice.ota;

import android.os.Handler;
import android.os.Looper;

import com.ggec.bleservice.YoBLEApi;
import com.ggec.bleservice.YoCommandApi;
import com.ggec.bleservice.core.BleDataService;
import com.ggec.bleservice.setting.Command;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * OTA电量检测器 (已适配新的电量获取机制)
 * 专门负责检测耳机电量是否满足OTA升级条件
 * 
 * 更新记录:
 * - 适配新的电量获取API，使用 getBatteryInfo() 替代 getFullBatteryInfo()
 * - 保持独立获取单个电量的能力，避免聚合机制的复杂性
 * - 修复电量获取时序问题：等待所有电量都获取完成后再判断OTA条件
 * - 优化回调逻辑：只有当左耳、右耳、充电盒电量都>0时才结束等待
 */
public class OtaBatteryChecker {
    
    private static final String TAG = "OtaBatteryChecker";
    
    // 电量阈值常量，用于OTA检查
    private static final int OTA_BATTERY_THRESHOLD = 30;
    
    // 主线程Handler
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    // 单例实例
    private static OtaBatteryChecker instance;
    
    /**
     * 获取单例实例
     * @return OtaBatteryChecker实例
     */
    public static synchronized OtaBatteryChecker getInstance() {
        if (instance == null) {
            instance = new OtaBatteryChecker();
        }
        return instance;
    }
    
    /**
     * 私有构造方法
     */
    private OtaBatteryChecker() {
        // 私有构造方法，防止外部实例化
    }
    
    /**
     * 解析电量字符串为整数
     * @param batteryStr 电量字符串（数字或"--"）
     * @return 电量整数值，如果是"--"或解析失败则返回0
     */
    private int parseBatteryLevel(String batteryStr) {
        if (batteryStr == null || "--".equals(batteryStr)) {
            return 0;
        }
        try {
            return Integer.parseInt(batteryStr);
        } catch (NumberFormatException e) {
            YoBTSDKLog.w(TAG, "解析电量字符串失败: " + batteryStr);
            return 0;
        }
    }

    /**
     * 检查电池电量是否满足OTA条件
     * 左右耳电量都必须大于阈值 (30%)
     * @return true 如果满足OTA条件，false 否则
     */
    public boolean checkBatteryLevel() {
        if (!BleDataService.getInstance().isDeviceReady()) {
            YoBTSDKLog.e(TAG, "设备未就绪，无法检查OTA电量条件");
            return false;
        }
        
        // 获取当前左右耳电量并解析为整数
        String leftBatteryLevel = YoCommandApi.getInstance().getLeftBatteryLevel();
        String rightBatteryLevel = YoCommandApi.getInstance().getRightBatteryLevel();
        
        // 如果电量值为0，可能是缓存中没有有效数据，尝试主动获取一次
        if (leftBatteryLevel.equals("--") || rightBatteryLevel.equals("--")) {
            YoBTSDKLog.i(TAG, "电量数据可能不是最新，尝试主动获取");
            
            // 使用同步方式获取最新电量（仅获取电量信息，不包含充电状态）
            try {
                final Object lock = new Object();
                final boolean[] completed = {false};
                final String[] batteryLevels = {"--", "--", "--"}; // 左耳、右耳、充电盒
                
                YoCommandApi.getInstance().getBatteryInfo(new YoCommandApi.BatteryInfoCallback() {
                    @Override
                    public void onBatteryInfoUpdated(int code, String leftLevel, String rightLevel, String caseLevel) {
                        synchronized (lock) {
                            if (code == Command.ResultCode.SUCCESS) {
                                batteryLevels[0] = leftLevel;
                                batteryLevels[1] = rightLevel;
                                batteryLevels[2] = caseLevel;
                                YoBTSDKLog.d(TAG, "同步电量回调: 左耳=" + leftLevel + "%, 右耳=" + rightLevel + "%, 充电盒=" + caseLevel + "%");
                                
                                // 检查是否所有电量都已获取完成（都不为0）
                                if (!leftLevel.equals("--") && !rightLevel.equals("--") && !caseLevel.equals("--")) {
                                    YoBTSDKLog.d(TAG, "同步获取所有电量已完成");
                                    completed[0] = true;
                                    lock.notify();
                                } else {
                                    YoBTSDKLog.d(TAG, "同步电量部分完成，继续等待: L=" + leftLevel + " R=" + rightLevel + " C=" + caseLevel);
                                }
                            } else {
                                YoBTSDKLog.e(TAG, "获取电量失败，状态码: " + code);
                                // 失败时也结束等待
                                completed[0] = true;
                                lock.notify();
                            }
                        }
                    }
                });
                
                // 等待电量获取完成，最多等待2秒
                synchronized (lock) {
                    if (!completed[0]) {
                        try {
                            lock.wait(2000);
                        } catch (InterruptedException e) {
                            // 忽略中断异常
                        }
                    }
                }
                
                // 更新电量值
                if (completed[0]) {
                    leftBatteryLevel = batteryLevels[0];
                    rightBatteryLevel = batteryLevels[1];
                    YoBTSDKLog.i(TAG, "已获取最新电量数据: 左耳=" + leftBatteryLevel + "%, 右耳=" + rightBatteryLevel + "%");
                } else {
                    YoBTSDKLog.e(TAG, "获取电量超时");
                }
            } catch (Exception e) {
                YoBTSDKLog.e(TAG, "获取电量异常: " + e.getMessage());
            }
        }
        
        // 检查电量是否都大于阈值
        boolean isOtaReady = (!leftBatteryLevel.equals("--") && !rightBatteryLevel.equals("--") &&
                parseBatteryLevel(leftBatteryLevel) > OTA_BATTERY_THRESHOLD &&
                parseBatteryLevel(rightBatteryLevel) > OTA_BATTERY_THRESHOLD);
        
        YoBTSDKLog.i(TAG, "OTA电量检查: 左耳=" + leftBatteryLevel + "%, 右耳=" + rightBatteryLevel + 
                   "%, 是否满足条件: " + isOtaReady);
        
        return isOtaReady;
    }
    
    /**
     * 电池状态回调接口
     */
    public interface BatteryCheckCallback {
        /**
         * 电池状态检查结果回调
         * @param ready 是否满足OTA条件
         * @param leftBattery 左耳电量百分比
         * @param rightBattery 右耳电量百分比
         */
        void onBatteryCheckResult(boolean ready, String leftBattery, String rightBattery);
    }
    
    /**
     * 异步检查耳机电量是否满足OTA条件
     * 使用独立的电量获取API，避免聚合机制的复杂性
     * @param callback 电池状态回调
     */
    public void checkBatteryLevelAsync(final BatteryCheckCallback callback) {
        if (callback == null) {
            return;
        }
        
        // 如果设备未就绪，直接返回不满足条件
        if (!BleDataService.getInstance().isDeviceReady()) {
            mainHandler.post(() -> callback.onBatteryCheckResult(false, "--", "--"));
            YoBTSDKLog.e(TAG, "设备未就绪，无法检查OTA电量条件");
            return;
        }
        
        // 使用线程执行异步检查
        new Thread(() -> {
            try {
                YoBTSDKLog.d(TAG, "开始异步电量检查，使用独立电量获取API");
                
                // 等待电量获取完成的标志
                final Object lock = new Object();
                final boolean[] completed = {false};
                final String[] batteryLevels = {"--", "--", "--"}; // 左耳、右耳、充电盒
                
                // 异步获取所有电量（使用独立API，避免聚合机制）
                YoCommandApi.getInstance().getBatteryInfo(new YoCommandApi.BatteryInfoCallback() {
                    @Override
                    public void onBatteryInfoUpdated(int code, String leftLevel, String rightLevel, String caseLevel) {
                        synchronized (lock) {
                            // 只有在命令成功时才更新电量值
                            if (code == Command.ResultCode.SUCCESS) {
                                batteryLevels[0] = leftLevel;
                                batteryLevels[1] = rightLevel;
                                batteryLevels[2] = caseLevel;
                                YoBTSDKLog.d(TAG, "电量回调: 左耳=" + leftLevel + "%, 右耳=" + rightLevel + "%, 充电盒=" + caseLevel + "%");
                                
                                // 检查是否所有电量都已获取完成（都不为0）
                                if (!leftLevel.equals("--") && !rightLevel.equals("--") && !caseLevel.equals("--")) {
                                    YoBTSDKLog.d(TAG, "所有电量已获取完成，结束等待");
                                    completed[0] = true;
                                    lock.notify();
                                } else {
                                    YoBTSDKLog.d(TAG, "电量部分完成，继续等待: L=" + leftLevel + " R=" + rightLevel + " C=" + caseLevel);
                                }
                            } else {
                                YoBTSDKLog.e(TAG, "获取电量失败，状态码: " + code);
                                // 失败时也结束等待，避免无限等待
                                completed[0] = true;
                                lock.notify();
                            }
                        }
                    }
                });
                
                // 等待电量获取完成，最多等待4秒
                synchronized (lock) {
                    if (!completed[0]) {
                        try {
                            YoBTSDKLog.d(TAG, "等待电量获取完成...");
                            lock.wait(4000);
                        } catch (InterruptedException e) {
                            YoBTSDKLog.e(TAG, "等待电量获取被中断", e);
                        }
                    }
                }
                
                // 检查是否超时
                if (!completed[0]) {
                    YoBTSDKLog.e(TAG, "电量检查超时");
                }
                
                // 检查电量是否满足OTA条件
                final String leftBattery = batteryLevels[0];
                final String rightBattery = batteryLevels[1];
                final String caseBattery = batteryLevels[2];
                final boolean isReady = (!leftBattery.equals("--") && !rightBattery.equals("--") &&
                        parseBatteryLevel(leftBattery) > OTA_BATTERY_THRESHOLD &&
                        parseBatteryLevel(rightBattery) > OTA_BATTERY_THRESHOLD);
                
                YoBTSDKLog.i(TAG, "OTA异步电量检查完成: 左耳=" + leftBattery + "%, 右耳=" + rightBattery + 
                          "%, 充电盒=" + caseBattery + "%, 是否满足条件: " + isReady);
                
                // 在主线程中回调结果
                mainHandler.post(() -> callback.onBatteryCheckResult(isReady, leftBattery, rightBattery));
                
            } catch (Exception e) {
                YoBTSDKLog.e(TAG, "获取电量异常: " + e.getMessage());
                // 发生异常时，在主线程中返回不满足条件
                mainHandler.post(() -> callback.onBatteryCheckResult(false, "--", "--"));
            }
        }).start();
    }
    
    /**
     * 获取OTA电量阈值
     * @return 电量阈值百分比
     */
    public int getBatteryThreshold() {
        return OTA_BATTERY_THRESHOLD;
    }
    
    /**
     * 释放资源
     */
    public void release() {
        instance = null;
    }
} 