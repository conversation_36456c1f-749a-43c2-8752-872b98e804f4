<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission
        android:name="android.permission.BLUETOOTH"/>
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        tools:targetApi="s" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADVERTISE"
        tools:targetApi="s" />

    <!-- 存储权限：适配分区存储模型 -->
    <!-- Android 12L (API 32) 及以下版本使用 -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <!-- Android 9 (API 28) 及以下版本使用 -->
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>

    <!-- Android 13 (API 33) 及以上版本的媒体权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

    <!-- 通话状态权限 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />

    <uses-feature
        android:name="android.hardware.bluetooth_le"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.bluetooth"
        android:required="true" />

    <uses-permission android:name="android.hardware.usb.host" />
    <uses-feature android:name="android.hardware.usb.host" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <application
        android:name=".GGECHSApplication"
        android:allowBackup="true"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher_y"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round_y"
        android:supportsRtl="true"
        android:theme="@style/Theme.HS01"
        tools:targetApi="31">
        <activity
            android:name="com.ggec.spptest.SppTestActivity"
            android:screenOrientation="portrait"
            android:exported="false"></activity>
        <activity
            android:name=".activity.MainActivity"
            android:screenOrientation="portrait"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".otatest.OtaDemoActivity"
            android:exported="false"></activity>
        <activity
            android:name=".activity.BluetoothConnectActivity"
            android:screenOrientation="portrait"
            android:exported="false" />
        <activity
            android:name=".activity.MusicSettingsActivity"
            android:screenOrientation="portrait"
            android:exported="false" />
        <activity
            android:name=".activity.ControlTestActivity"
            android:screenOrientation="portrait"
            android:exported="false" />
        <activity
            android:name=".activity.ControlSettingsActivity"
            android:screenOrientation="portrait"
            android:exported="false" />
        <activity
            android:name=".activity.FunctionSettingsActivity"
            android:screenOrientation="portrait"
            android:exported="false" />
        <activity
            android:name=".activity.AppSettingsActivity"
            android:screenOrientation="portrait"
            android:exported="false" />
        <activity
            android:name=".activity.OtaUpdateActivity"
            android:screenOrientation="portrait"
            android:exported="false" />
    </application>

</manifest>