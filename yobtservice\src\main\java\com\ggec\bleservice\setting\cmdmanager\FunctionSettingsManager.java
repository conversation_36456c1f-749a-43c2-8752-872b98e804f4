package com.ggec.bleservice.setting.cmdmanager;

import com.ggec.bleservice.YoCommandApi;
import com.ggec.bleservice.setting.CommandQueueCenter;
import com.ggec.bleservice.setting.functioncmd.WearDetectionCommand;
import com.ggec.bleservice.setting.functioncmd.VoiceWakeupCommand;
import com.ggec.bleservice.setting.functioncmd.GameModeCommand;
import com.ggec.bleservice.setting.functioncmd.FallAlertCommand;
import com.ggec.bleservice.setting.functioncmd.EarLocationCommand;
import com.ggec.bleservice.setting.functioncmd.FindAlertLeftCommand;
import com.ggec.bleservice.setting.functioncmd.FindAlertRightCommand;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 功能设置管理器
 * 负责管理所有耳机功能相关的设置，包括佩戴检测、语音唤醒、游戏模式、掉落提醒等
 */
public class FunctionSettingsManager extends BaseCommandManager {
    private static final String TAG = "FunctionSettingsManager";
    
    // 单例实例
    private static FunctionSettingsManager instance;
    
    /**
     * 获取单例实例
     */
    public static synchronized FunctionSettingsManager getInstance() {
        if (instance == null) {
            instance = new FunctionSettingsManager();
        }
        return instance;
    }
    
    /**
     * 私有构造方法
     */
    private FunctionSettingsManager() {
        super();
    }
    
    /**
     * 释放资源
     */
    @Override
    public void release() {
        super.release();
        instance = null;
    }
    
    /**
     * 设置佩戴检测开关
     * @param enabled 是否启用
     */
    public void setWearDetection(boolean enabled) {
        YoBTSDKLog.i(TAG, "设置佩戴检测: " + (enabled ? "开启" : "关闭"));
        
        // 创建佩戴检测命令
        WearDetectionCommand command = new WearDetectionCommand(enabled);
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 设置语音唤醒(VAD)开关
     * @param enabled 是否启用
     */
    public void setVoiceWakeup(boolean enabled) {
        YoBTSDKLog.i(TAG, "设置语音唤醒: " + (enabled ? "开启" : "关闭"));
        
        // 创建语音唤醒命令
        VoiceWakeupCommand command = new VoiceWakeupCommand(enabled);
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 设置游戏模式开关
     * @param enabled 是否启用
     */
    public void setGameMode(boolean enabled) {
        YoBTSDKLog.i(TAG, "设置游戏模式: " + (enabled ? "开启" : "关闭"));
        
        // 创建游戏模式命令
        GameModeCommand command = new GameModeCommand(enabled);
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 设置掉落提醒开关
     * @param enabled 是否启用
     */
    public void setFallAlert(boolean enabled) {
        YoBTSDKLog.i(TAG, "设置掉落提醒: " + (enabled ? "开启" : "关闭"));
        
        // 创建掉落提醒命令
        FallAlertCommand command = new FallAlertCommand(enabled);
        
        // 执行命令
        createAndEnqueueCommand(command);
    }

    /**
     * 设置耳机位置功能开关
     * @param enabled 是否启用
     */
    public void setEarLocation(boolean enabled) {
        YoBTSDKLog.i(TAG, "设置耳机位置: " + (enabled ? "开启" : "关闭"));
        
        // 创建耳机位置命令
        EarLocationCommand command = new EarLocationCommand(enabled);
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 触发左耳响铃或停止
     * @param start true为响铃, false为停止
     */
    public void findAlertLeft(boolean start) {
        YoBTSDKLog.i(TAG, "触发左耳机" + (start ? "响铃" : "停止"));
        FindAlertLeftCommand command = new FindAlertLeftCommand(start);
        createAndEnqueueCommand(command);
    }

    /**
     * 触发右耳响铃或停止
     * @param start true为响铃, false为停止
     */
    public void findAlertRight(boolean start) {
        YoBTSDKLog.i(TAG, "触发右耳机" + (start ? "响铃" : "停止"));
        FindAlertRightCommand command = new FindAlertRightCommand(start);
        createAndEnqueueCommand(command);
    }

    private YoCommandApi.GameModeCallback gameModeCallback;

    /**
     * 监听游戏模式变化
     * @param callback 游戏模式变化回调
     */
    public void listenGameMode(YoCommandApi.GameModeCallback callback) {
        this.gameModeCallback = callback;
    }

    @Override
    protected String getManagerTag() {
        return TAG;
    }

    @Override
    protected void handleUnsolicitedResponse(String data) {
        // 检查是否有命令正在执行，如果有则让命令优先处理数据
        if (CommandQueueCenter.getInstance().isExecuting()) {
            YoBTSDKLog.d(TAG, "有命令正在执行，暂不处理主动上报数据: " + data);
            return;
        }

        YoBTSDKLog.d(TAG, "处理主动上报数据: " + data);
        if (data == null || data.length() < 12) {
            return;
        }
        String prefix = data.substring(0, 6);

        try {
            if (prefix.equals("99EC89")) { // 游戏模式
                // 获取命令状态字节
                String statusByteHex = data.substring(10, 12);
                try {
                    // 将十六进制状态转换为整数
                    int mode = Integer.parseInt(statusByteHex, 16);
                    // 通知命令完成
                    if(gameModeCallback != null) {
                        gameModeCallback.listenGameMode(mode == 1);
                    }
                } catch (NumberFormatException e) {
                    YoBTSDKLog.e(TAG, "解析EQ模式状态失败: " + statusByteHex, e);
                }
            }
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "解析电池主动上报数据失败: " + data, e);
        }
    }


    @Override
    protected boolean isDataRelevant(String data) {
        // 检查数据前缀是否与功能设置相关
        if (data != null && data.length() >= 6) {
            String prefix = data.substring(0, 6); // 取前6个字符 (99ECxx)
            
            // 佩戴检测 99EC87
            // 语音唤醒 99EC88
            // 游戏模式 99EC89
            // 掉落提醒 99EC8A
            // 寻找耳机 99EC8B
            // 寻找提示音 99EC8C
            return "99EC87".equals(prefix) || "99EC88".equals(prefix) || 
                   "99EC89".equals(prefix) || "99EC8A".equals(prefix) ||
                   "99EC8B".equals(prefix) || "99EC8C".equals(prefix);
        }
        return false;
    }
} 