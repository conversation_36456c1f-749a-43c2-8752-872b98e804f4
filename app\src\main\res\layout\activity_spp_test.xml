<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/spp_test_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    tools:context="com.ggec.spptest.SppTestActivity">

    <!-- 顶部标题栏 -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/btn_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:padding="12dp"
                android:src="@android:drawable/ic_menu_revert"
                android:contentDescription="返回"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="SPP设备测试"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/btn_refresh"
                app:layout_constraintStart_toEndOf="@+id/btn_back"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/btn_refresh"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:padding="12dp"
                android:src="@android:drawable/ic_menu_rotate"
                android:contentDescription="刷新"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>

    <!-- 扫描状态 -->
    <TextView
        android:id="@+id/tv_scan_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:text="准备就绪"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <ProgressBar
        android:id="@+id/progress_scanning"
        style="?android:attr/progressBarStyleSmall"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tv_scan_status"
        app:layout_constraintStart_toEndOf="@+id/tv_scan_status"
        app:layout_constraintTop_toTopOf="@+id/tv_scan_status" />

    <!-- 扫描按钮 -->
    <LinearLayout
        android:id="@+id/filter_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_scan_status">

        <Button
            android:id="@+id/btn_scan_devices"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="扫描设备"
            style="@style/Widget.AppCompat.Button.Colored" />

    </LinearLayout>

    <!-- 连接状态区域 -->
    <LinearLayout
        android:id="@+id/layout_connection_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="8dp"
        android:orientation="vertical"
        android:visibility="gone"
        android:background="#E8F5E9"
        android:padding="12dp"
        app:layout_constraintTop_toBottomOf="@+id/filter_layout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:id="@+id/tv_connection_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="连接状态: 未连接"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tv_connected_device"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="设备: 无"
            android:textSize="14sp" />

        <ProgressBar
            android:id="@+id/progress_connecting"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:indeterminate="true"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_disconnect"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="断开连接"
                android:enabled="false"
                style="@style/Widget.AppCompat.Button" />

            <Button
                android:id="@+id/btn_send_test"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="发送测试数据"
                android:enabled="false"
                style="@style/Widget.AppCompat.Button.Colored" />
        </LinearLayout>
    </LinearLayout>

    <!-- 无设备提示 -->
    <TextView
        android:id="@+id/tv_no_devices"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="16dp"
        android:text="暂无设备"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout_connection_status" />

    <!-- 设备列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_spp_devices"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:background="#F5F5F5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout_connection_status" />

</androidx.constraintlayout.widget.ConstraintLayout> 