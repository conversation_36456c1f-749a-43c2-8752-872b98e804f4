<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- 选项说明 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="请选择该操作对应的功能："
            android:textSize="13sp"
            android:textColor="@color/font_secondary"
            android:layout_marginBottom="4dp"/>

        <!-- 控制功能选项 -->
        <RadioGroup
            android:id="@+id/radio_group_functions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 播放+暂停 -->
            <RadioButton
                android:id="@+id/radio_play_pause"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="播放+暂停"
                android:textSize="15sp"
                android:textColor="@color/font_primary"
                android:layout_marginBottom="2dp"
                android:paddingVertical="4dp"
                android:paddingHorizontal="12dp"
                android:background="?attr/selectableItemBackground"/>

            <!-- 音量加 -->
            <RadioButton
                android:id="@+id/radio_volume_up"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="音量加"
                android:textSize="15sp"
                android:textColor="@color/font_primary"
                android:layout_marginBottom="2dp"
                android:paddingVertical="4dp"
                android:paddingHorizontal="12dp"
                android:background="?attr/selectableItemBackground"/>

            <!-- 音量减 -->
            <RadioButton
                android:id="@+id/radio_volume_down"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="音量减"
                android:textSize="15sp"
                android:textColor="@color/font_primary"
                android:layout_marginBottom="2dp"
                android:paddingVertical="4dp"
                android:paddingHorizontal="12dp"
                android:background="?attr/selectableItemBackground"/>

            <!-- 上一曲 -->
            <RadioButton
                android:id="@+id/radio_previous_track"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="上一曲"
                android:textSize="15sp"
                android:textColor="@color/font_primary"
                android:layout_marginBottom="2dp"
                android:paddingVertical="4dp"
                android:paddingHorizontal="12dp"
                android:background="?attr/selectableItemBackground"/>

            <!-- 下一曲 -->
            <RadioButton
                android:id="@+id/radio_next_track"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="下一曲"
                android:textSize="15sp"
                android:textColor="@color/font_primary"
                android:layout_marginBottom="2dp"
                android:paddingVertical="4dp"
                android:paddingHorizontal="12dp"
                android:background="?attr/selectableItemBackground"/>

            <!-- 语音助手 -->
            <RadioButton
                android:id="@+id/radio_voice_assistant"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="语音助手"
                android:textSize="15sp"
                android:textColor="@color/font_primary"
                android:layout_marginBottom="2dp"
                android:paddingVertical="4dp"
                android:paddingHorizontal="12dp"
                android:background="?attr/selectableItemBackground"/>

            <!-- EQ切换 -->
            <RadioButton
                android:id="@+id/radio_eq_switch"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="EQ切换"
                android:textSize="15sp"
                android:textColor="@color/font_primary"
                android:layout_marginBottom="2dp"
                android:paddingVertical="4dp"
                android:paddingHorizontal="12dp"
                android:background="?attr/selectableItemBackground"/>

            <!-- 游戏模式 -->
            <RadioButton
                android:id="@+id/radio_game_mode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="游戏模式"
                android:textSize="15sp"
                android:textColor="@color/font_primary"
                android:layout_marginBottom="2dp"
                android:paddingVertical="4dp"
                android:paddingHorizontal="12dp"
                android:background="?attr/selectableItemBackground"/>

            <!-- 无 -->
            <RadioButton
                android:id="@+id/radio_none"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="无"
                android:textSize="15sp"
                android:textColor="@color/font_primary"
                android:layout_marginBottom="2dp"
                android:paddingVertical="4dp"
                android:paddingHorizontal="12dp"
                android:background="?attr/selectableItemBackground"/>

        </RadioGroup>

    </LinearLayout>
</ScrollView> 