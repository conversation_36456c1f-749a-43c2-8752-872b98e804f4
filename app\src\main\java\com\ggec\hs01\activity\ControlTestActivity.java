package com.ggec.hs01.activity;

import android.os.Bundle;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.ggec.bleservice.YoCommandApi;
import com.ggec.hs01.GGECHSApplication;
import com.ggec.hs01.R;
import com.ggec.hs01.view.HMButton;

public class ControlTestActivity extends AppCompatActivity {

    private YoCommandApi commandApi;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_control_test);

        commandApi = ((GGECHSApplication) getApplication()).getCommandApi();

        initBackButton();
        initControlButtons();

//        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.control_test_main), (v, insets) -> {
//            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
//            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
//            return insets;
//        });
    }

    private void initBackButton() {
        ImageView btnBack = findViewById(R.id.btn_back);
        btnBack.setOnClickListener(v -> finish());
    }

    private void initControlButtons() {
        HMButton btnLeftTripleEq = findViewById(R.id.btn_left_triple_eq);
        HMButton btnLeftTriplePrev = findViewById(R.id.btn_left_triple_prev);
        HMButton btnRightTripleAssist = findViewById(R.id.btn_right_triple_assist);
        HMButton btnRightTriplePlay = findViewById(R.id.btn_right_triple_play);

        // --- New Buttons for Double Tap ---
        // Right ear double tap
        HMButton btnRightDoubleNext = findViewById(R.id.btn_right_double_next);
        HMButton btnRightDoubleAssist = findViewById(R.id.btn_right_double_assist);
        HMButton btnRightDoublePlay = findViewById(R.id.btn_right_double_play);
        // Left ear double tap
        HMButton btnLeftDoublePrev = findViewById(R.id.btn_left_double_prev);
        HMButton btnLeftDoubleAssist = findViewById(R.id.btn_left_double_assist);
        HMButton btnLeftDoubleEq = findViewById(R.id.btn_left_double_eq);
        HMButton btnLeftDoubleNone = findViewById(R.id.btn_left_double_none);

        btnLeftTripleEq.setOnClickListener(v -> setControlFunction(
                YoCommandApi.EAR_LEFT,
                YoCommandApi.TAP_TRIPLE,
                6, // EQ切换
                "左耳三击设置为EQ切换"
        ));

        btnLeftTriplePrev.setOnClickListener(v -> setControlFunction(
                YoCommandApi.EAR_LEFT,
                YoCommandApi.TAP_TRIPLE,
                3, // 上一曲
                "左耳三击设置为上一曲"
        ));

        btnRightTripleAssist.setOnClickListener(v -> setControlFunction(
                YoCommandApi.EAR_RIGHT,
                YoCommandApi.TAP_TRIPLE,
                5, // 语音助手
                "右耳三击设置为语音助手"
        ));

        btnRightTriplePlay.setOnClickListener(v -> setControlFunction(
                YoCommandApi.EAR_RIGHT,
                YoCommandApi.TAP_TRIPLE,
                0, // 播放/暂停
                "右耳三击设置为播放/暂停"
        ));

        // --- Listeners for Double Tap ---
        // 1. 右耳双击设置下一曲
        btnRightDoubleNext.setOnClickListener(v -> setControlFunction(
                YoCommandApi.EAR_RIGHT,
                YoCommandApi.TAP_TWICE,
                4, // 下一曲
                "右耳双击设置为下一曲"
        ));

        // 2. 右耳双击设置语音助手
        btnRightDoubleAssist.setOnClickListener(v -> setControlFunction(
                YoCommandApi.EAR_RIGHT,
                YoCommandApi.TAP_TWICE,
                5, // 语音助手
                "右耳双击设置为语音助手"
        ));

        // 3. 右耳双击设置暂停+播放
        btnRightDoublePlay.setOnClickListener(v -> setControlFunction(
                YoCommandApi.EAR_RIGHT,
                YoCommandApi.TAP_TWICE,
                0, // 播放/暂停
                "右耳双击设置为播放/暂停"
        ));

        // 4. 左耳双击设置上一曲
        btnLeftDoublePrev.setOnClickListener(v -> setControlFunction(
                YoCommandApi.EAR_LEFT,
                YoCommandApi.TAP_TWICE,
                3, // 上一曲
                "左耳双击设置为上一曲"
        ));

        // 5. 左耳双击设置语音助手
        btnLeftDoubleAssist.setOnClickListener(v -> setControlFunction(
                YoCommandApi.EAR_LEFT,
                YoCommandApi.TAP_TWICE,
                5, // 语音助手
                "左耳双击设置为语音助手"
        ));

        // 6. 左耳双击设置EQ切换
        btnLeftDoubleEq.setOnClickListener(v -> setControlFunction(
                YoCommandApi.EAR_LEFT,
                YoCommandApi.TAP_TWICE,
                6, // EQ切换
                "左耳双击设置为EQ切换"
        ));

        btnLeftDoubleNone.setOnClickListener(v -> setControlFunction(
                YoCommandApi.EAR_LEFT,
                YoCommandApi.TAP_TWICE,
                8, // 无
                "左耳双击设置为无"
        ));
    }

    private void setControlFunction(int earSide, int tapType, int functionType, String description) {
        if (!((GGECHSApplication) getApplication()).getBleApi().isDeviceConnected()) {
            Toast.makeText(this, "请先连接设备", Toast.LENGTH_SHORT).show();
            return;
        }

        commandApi.setControlFunction(earSide, tapType, functionType, (code, resultValue) -> {
            runOnUiThread(() -> {
                String message = description + ((code == YoCommandApi.CommandResultCode.SUCCESS) ? "成功" : "失败");
                //Toast.makeText(ControlTestActivity.this, message, Toast.LENGTH_SHORT).show();
            });
        });
    }
} 