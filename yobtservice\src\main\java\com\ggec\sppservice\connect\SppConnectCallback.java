package com.ggec.sppservice.connect;

import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;

/**                             
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * SPP连接回调接口，用于通知SPP设备连接事件
 */
public interface SppConnectCallback {
    /**
     * 开始连接回调
     * @param device 要连接的蓝牙设备
     */
    void onConnectStart(BluetoothDevice device);
    
    /**
     * 请求配对回调
     * @param device 需要配对的蓝牙设备
     */
    void onPairingRequest(BluetoothDevice device);
    
    /**
     * 配对成功回调
     * @param device 已配对的蓝牙设备
     */
    void onPaired(BluetoothDevice device);
    
    /**
     * 连接成功回调
     * @param device 已连接的蓝牙设备
     * @param socket 蓝牙连接Socket
     */
    void onConnected(BluetoothDevice device, BluetoothSocket socket);
    
    /**
     * 连接失败回调
     * @param device 蓝牙设备
     * @param errorMsg 错误信息
     */
    void onConnectFailed(BluetoothDevice device, String errorMsg);
    
    /**
     * 断开连接回调
     * @param device 断开连接的蓝牙设备
     */
    void onDisconnected(BluetoothDevice device);
} 