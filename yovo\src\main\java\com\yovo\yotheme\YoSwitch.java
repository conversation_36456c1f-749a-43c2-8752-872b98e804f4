package com.yovo.yotheme;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;

/**
 * YoSwitch - 自定义滑动开关控件
 * 风格：灰色背景，滑动部分为宇宙蓝，滑块为白色
 * 特点：滑块上下边界与滑轨平齐，无阴影，一体化设计便于移植
 */
public class YoSwitch extends View {
    // 默认颜色 - 硬编码以便于移植
    private int trackOffColor = 0xFFE5E5E5; // 灰色背景
    private int trackOnColor = 0xFF0A59F7;  // 宇宙蓝（品牌色）
    private int thumbColor = 0xFFFFFFFF;    // 白色滑块

    // 尺寸参数
    private int width;
    private int height;
    private float aspectRatio = 1.575f;  // 宽高比
    private float heightFactor = 0.85f;  // 高度缩放因子
    private float thumbPosition = 0;     // 滑块位置，0表示关闭，1表示打开
    private int trackPadding = 3;        // 滑轨与边缘的间距
    private int thumbPadding = 3;        // 滑块与滑轨的间距
    
    // 开关状态
    private boolean isChecked = false;
    
    // 绘制工具
    private Paint paint;
    private RectF trackRectF;
    private RectF thumbRectF;
    
    // 动画
    private ValueAnimator animator;
    private static final int ANIMATION_DURATION = 200; // 200ms
    
    // 监听器
    private OnCheckedChangeListener onCheckedChangeListener;
    
    // 构造方法
    public YoSwitch(Context context) {
        this(context, null);
    }
    
    public YoSwitch(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }
    
    public YoSwitch(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    
    private void init() {
        paint = new Paint();
        paint.setAntiAlias(true);  // 抗锯齿
        
        trackRectF = new RectF();
        thumbRectF = new RectF();
        
        // 初始化动画
        animator = new ValueAnimator();
        animator.setInterpolator(new AccelerateDecelerateInterpolator());
        animator.setDuration(ANIMATION_DURATION);
        animator.addUpdateListener(animation -> {
            thumbPosition = (float) animation.getAnimatedValue();
            calculateThumbRect();
            invalidate();
        });
    }
    
    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        
        // 获取测量模式和大小
        int measuredWidth = getMeasuredWidth();
        int measuredHeight = getMeasuredHeight();
        
        // 使用比例模式，保持指定的宽高比
        width = measuredWidth;
        height = measuredHeight;
        
        // 根据比例调整宽高
        if (width / (float)height > aspectRatio) {
            // 过宽，调整宽度
            width = (int)(height * aspectRatio);
        } else {
            // 过高，调整高度
            height = (int)(width / aspectRatio);
        }
        
        // 应用高度调整因子
        height = (int)(height * heightFactor);
        
        // 设置测量尺寸
        setMeasuredDimension(width, height);
        
        // 设置轨道区域
        trackRectF.set(0, 0, width, height);
        
        // 设置滑块位置
        calculateThumbRect();
    }
    
    private void calculateThumbRect() {
        // 考虑额外的 thumbPadding 使滑块更小
        int thumbWidth = height - 2 * (trackPadding + thumbPadding);
        
        // 修正 offset 计算，考虑 thumbPadding，确保滑块不会越界
        float maxOffset = width - thumbWidth - 2 * (trackPadding + thumbPadding);
        float offset = maxOffset * thumbPosition;
        
        thumbRectF.set(
                trackPadding + thumbPadding + offset,
                trackPadding + thumbPadding,
                trackPadding + thumbPadding + thumbWidth + offset,
                height - trackPadding - thumbPadding
        );
    }
    
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        
        // 绘制轨道背景
        if (isChecked) {
            paint.setColor(trackOnColor);
        } else {
            paint.setColor(trackOffColor);
        }
        canvas.drawRoundRect(trackRectF, height / 2, height / 2, paint);
        
        // 绘制滑块
        paint.setColor(thumbColor);
        float thumbRadius = (height - 2 * (trackPadding + thumbPadding)) / 2;
        canvas.drawRoundRect(thumbRectF, thumbRadius, thumbRadius, paint);
    }
    
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                return true;
            case MotionEvent.ACTION_UP:
                toggle();
                return true;
        }
        return super.onTouchEvent(event);
    }
    
    /**
     * 切换开关状态
     */
    public void toggle() {
        isChecked = !isChecked;
        
        // 启动动画
        if (animator.isRunning()) {
            animator.cancel();
        }
        
        float startValue = isChecked ? 0 : 1;
        float endValue = isChecked ? 1 : 0;
        animator.setFloatValues(startValue, endValue);
        animator.start();
        
        // 通知监听器
        if (onCheckedChangeListener != null) {
            onCheckedChangeListener.onCheckedChanged(this, isChecked);
        }
    }
    
    /**
     * 获取当前开关状态
     * @return 是否开启
     */
    public boolean isChecked() {
        return isChecked;
    }
    
    /**
     * 设置开关状态，不触发动画
     * @param checked 是否选中
     */
    public void setChecked(boolean checked) {
        if (this.isChecked == checked) {
            return;
        }
        
        this.isChecked = checked;
        thumbPosition = checked ? 1 : 0;
        calculateThumbRect();
        invalidate();
        
        // 通知监听器
        if (onCheckedChangeListener != null) {
            onCheckedChangeListener.onCheckedChanged(this, isChecked);
        }
    }
    
    /**
     * 设置开关状态，带动画效果
     * @param checked 是否选中
     */
    public void setCheckedWithAnimation(boolean checked) {
        if (this.isChecked == checked) {
            return;
        }
        
        this.isChecked = checked;
        
        // 启动动画
        if (animator.isRunning()) {
            animator.cancel();
        }
        
        float startValue = thumbPosition;
        float endValue = checked ? 1 : 0;
        animator.setFloatValues(startValue, endValue);
        animator.start();
        
        // 通知监听器
        if (onCheckedChangeListener != null) {
            onCheckedChangeListener.onCheckedChanged(this, isChecked);
        }
    }
    
    /**
     * 设置关闭状态的轨道颜色
     * @param color 颜色值
     */
    public void setTrackOffColor(int color) {
        this.trackOffColor = color;
        invalidate();
    }
    
    /**
     * 设置开启状态的轨道颜色
     * @param color 颜色值
     */
    public void setTrackOnColor(int color) {
        this.trackOnColor = color;
        invalidate();
    }
    
    /**
     * 设置滑块颜色
     * @param color 颜色值
     */
    public void setThumbColor(int color) {
        this.thumbColor = color;
        invalidate();
    }
    
    /**
     * 设置轨道内边距
     * @param padding 内边距
     */
    public void setTrackPaddingSize(int padding) {
        this.trackPadding = padding;
        calculateThumbRect();
        invalidate();
    }
    
    /**
     * 设置滑块内边距
     * @param padding 内边距
     */
    public void setThumbPaddingSize(int padding) {
        this.thumbPadding = padding;
        calculateThumbRect();
        invalidate();
    }
    
    /**
     * 设置动画持续时间
     * @param duration 持续时间，单位毫秒
     */
    public void setAnimationDuration(int duration) {
        if (duration < 0) {
            return;
        }
        animator.setDuration(duration);
    }
    
    /**
     * 设置宽高比例
     * @param ratio 宽高比例，默认为1.575
     */
    public void setRatio(float ratio) {
        if (ratio > 0 && this.aspectRatio != ratio) {
            this.aspectRatio = ratio;
            requestLayout();
        }
    }
    
    /**
     * 设置高度调整因子
     * @param factor 高度调整因子，小于1表示减小高度，大于1表示增加高度
     */
    public void setHeightFactor(float factor) {
        if (factor > 0 && this.heightFactor != factor) {
            this.heightFactor = factor;
            requestLayout();
        }
    }
    
    /**
     * 获取当前宽高比例
     * @return 宽高比例
     */
    public float getAspectRatio() {
        return aspectRatio;
    }
    
    /**
     * 获取当前高度调整因子
     * @return 高度调整因子
     */
    public float getHeightFactor() {
        return heightFactor;
    }
    
    /**
     * 恢复到默认尺寸设置
     */
    public void resetSize() {
        this.aspectRatio = 1.575f;
        this.heightFactor = 0.85f;
        this.trackPadding = 1;
        this.thumbPadding = 3;
        requestLayout();
    }
    
    /**
     * 设置状态改变监听器
     * @param listener 监听器
     */
    public void setOnCheckedChangeListener(OnCheckedChangeListener listener) {
        this.onCheckedChangeListener = listener;
    }
    
    /**
     * 状态改变监听接口
     */
    public interface OnCheckedChangeListener {
        /**
         * 当开关状态改变时调用
         * @param switchView 开关视图
         * @param isChecked 是否选中
         */
        void onCheckedChanged(YoSwitch switchView, boolean isChecked);
    }
} 