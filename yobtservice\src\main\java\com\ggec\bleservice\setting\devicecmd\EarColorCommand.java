package com.ggec.bleservice.setting.devicecmd;

import com.ggec.bleservice.setting.Command;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 获取耳机颜色命令
 * 负责从耳机获取硬件颜色信息（黑色）
 */
public class EarColorCommand extends Command {
    private static final String TAG = "EarColorCommand";
    
    // 命令前缀，用于确认是获取耳机颜色命令
    private static final String REQUEST_PREFIX = "99EC910001";
    
    // 响应前缀
    // 注意：根据协议文档，应该返回99EC8B0001，但实际上返回的是与请求相同的99EC910001
    private static final String RESPONSE_PREFIX = "99EC910001";
    
    // 发送命令数据
    private static final String COMMAND_DATA = "99EC91000100" + "1234";
    
    // 命令校验和后缀
    private static final String COMMAND_SUFFIX = "1234";
    
    // 耳机颜色常量
    public static final int COLOR_BLACK = 0; // 黑色
    
    // 耳机颜色结果
    private int earColor = COLOR_BLACK;
    
    /**
     * 构造方法
     */
    public EarColorCommand() {
        super();
        // 设置命令前缀
        setCommandPrefix("99EC91");
    }
    
    @Override
    public String getCommandData() {
        return COMMAND_DATA;
    }
    
    @Override
    public String parseResponse(String responseData) {
        YoBTSDKLog.d(TAG, "解析响应数据: " + responseData);
        
        if (responseData == null || responseData.length() < 12) {
            // 通知命令失败，yobackdata为null
            notifyCompletion(ResultCode.FAILED, null, "响应数据格式错误");
            return "响应数据格式错误";
        }
        
        // 获取颜色字节 - 在实际返回的数据中位于位置10-12
        String colorByte = responseData.substring(10, 12);
        
        // 解析颜色值
        try {
            earColor = Integer.parseInt(colorByte, 16);
            String result = String.format("%02x", earColor); // 格式化为两位十六进制
            
            // 通知命令完成，yobackdata为颜色代码
            notifyCompletion(ResultCode.SUCCESS, result, "获取颜色成功");
            
            return result;
        } catch (NumberFormatException e) {
            // 通知命令失败，yobackdata为null
            notifyCompletion(ResultCode.FAILED, null, "颜色数据解析错误");
            
            return "颜色数据解析错误";
        }
    }
    
    @Override
    public boolean isResponseMatch(String responseData) {
        YoBTSDKLog.d(TAG, "检查响应匹配: " + responseData);
        
        // 检查响应前缀是否匹配
        return responseData != null && responseData.startsWith(RESPONSE_PREFIX);
    }
    
    /**
     * 获取耳机颜色结果
     * @return 耳机颜色代码
     */
    public int getEarColor() {
        return earColor;
    }
} 