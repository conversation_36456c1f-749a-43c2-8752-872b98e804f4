package com.ggec.bleservice.ota;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;

import com.ggec.bleservice.YoBLEApi;
import com.ggec.bleservice.YoCommandApi;
import com.ggec.bleservice.core.BleDataService;
import com.ggec.bleservice.setting.Command;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * OTA状态检测器
 * 负责检查设备是否满足OTA升级条件
 */
public class OtaStatusChecker {
    
    private static final String TAG = "OtaStatusChecker";
    
    // 主线程Handler
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    // 上下文
    private final Context context;
    
    // 电量检查器
    private final OtaBatteryChecker batteryChecker;
    
    // SPP连接检查器
    private final OtaSppConnectionChecker sppConnectionChecker;
    
    // 单例实例
    private static OtaStatusChecker instance;
    
    /**
     * 获取单例实例
     * @param context 应用上下文
     * @return OtaStatusChecker实例
     */
    public static synchronized OtaStatusChecker getInstance(Context context) {
        if (instance == null) {
            instance = new OtaStatusChecker(context.getApplicationContext());
        }
        return instance;
    }
    
    /**
     * 私有构造方法
     * @param context 应用上下文
     */
    private OtaStatusChecker(Context context) {
        // 保存上下文
        this.context = context.getApplicationContext();
        
        // 获取电量检查器实例
        batteryChecker = OtaBatteryChecker.getInstance();
        
        // 获取SPP连接检查器实例
        sppConnectionChecker = OtaSppConnectionChecker.getInstance(context);
    }
    
    /**
     * 检查耳机电量是否满足OTA条件
     * 当左右耳机电量都大于30%时返回true
     * @return 是否满足OTA条件
     */
    public boolean checkEarbudsBatteryForOta() {
        // 委托给OtaBatteryChecker处理
        return batteryChecker.checkBatteryLevel();
    }
    
    /**
     * 检查设备是否满足所有OTA条件
     * @return 是否满足所有OTA条件
     */
    public boolean checkOtaStatus() {
        // 检查电量条件
        boolean batteryOk = checkEarbudsBatteryForOta();
        if (!batteryOk) {
            YoBTSDKLog.i(TAG, "电量条件不满足，无法进行OTA升级");
        }
        return batteryOk;
    }
    
    /**
     * OTA电池状态回调接口
     */
    public interface OtaBatteryStatusCallback {
        /**
         * 电池状态检查结果回调
         * @param ready 是否满足OTA条件
         * @param code 结果状态码 (Command.ResultCode)
         */
        void onOtaBatteryResult(boolean ready, int code);
    }
    
    /**
     * 异步检查OTA电池状态
     * @param callback 状态回调
     */
    public void checkOtaBatteryStatusAsync(final OtaBatteryStatusCallback callback) {
        if (callback == null) {
            return;
        }
        
        // 委托给OtaBatteryChecker处理，并通过适配器转换回调接口
        batteryChecker.checkBatteryLevelAsync(new OtaBatteryChecker.BatteryCheckCallback() {
            @Override
            public void onBatteryCheckResult(boolean ready, String leftBattery, String rightBattery) {
                // 调用原始回调，添加状态码参数
                int code = ready ? Command.ResultCode.SUCCESS : Command.ResultCode.FAILED;
                mainHandler.post(() -> callback.onOtaBatteryResult(ready, code));
            }
        });
    }
    
    /**
     * 获取OTA电量阈值
     * @return 电量阈值百分比
     */
    public int getOtaBatteryThreshold() {
        return batteryChecker.getBatteryThreshold();
    }
    
    /**
     * 释放资源
     */
    public void release() {
        // 释放电量检查器资源
        batteryChecker.release();
        
        // 释放SPP连接检查器资源
        sppConnectionChecker.release();
        
        instance = null;
    }
} 