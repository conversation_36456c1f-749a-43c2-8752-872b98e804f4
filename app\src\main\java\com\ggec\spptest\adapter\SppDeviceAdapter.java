package com.ggec.spptest.adapter;

import android.bluetooth.BluetoothDevice;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.ggec.hs01.R;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class SppDeviceAdapter extends RecyclerView.Adapter<SppDeviceAdapter.DeviceViewHolder> {

    // 内部类存储设备信息
    public static class DeviceInfo {
        private final BluetoothDevice device;
        private int rssi;

        public DeviceInfo(BluetoothDevice device, int rssi) {
            this.device = device;
            this.rssi = rssi;
        }

        public BluetoothDevice getDevice() {
            return device;
        }

        public int getRssi() {
            return rssi;
        }

        public void updateRssi(int rssi) {
            this.rssi = rssi;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            DeviceInfo that = (DeviceInfo) o;
            return Objects.equals(device.getAddress(), that.device.getAddress());
        }

        @Override
        public int hashCode() {
            return Objects.hash(device.getAddress());
        }
    }

    private final List<DeviceInfo> deviceList = new ArrayList<>();
    private OnDeviceClickListener onDeviceClickListener;

    // ViewHolder类
    public static class DeviceViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvDeviceName;
        private final TextView tvDeviceAddress;
        private final TextView tvDeviceRssi;
        private final Button btnConnect;

        public DeviceViewHolder(@NonNull View itemView) {
            super(itemView);
            tvDeviceName = itemView.findViewById(R.id.tv_spp_device_name);
            tvDeviceAddress = itemView.findViewById(R.id.tv_spp_device_address);
            tvDeviceRssi = itemView.findViewById(R.id.tv_spp_device_rssi);
            btnConnect = itemView.findViewById(R.id.btn_spp_connect);
        }

        public void bind(DeviceInfo deviceInfo) {
            BluetoothDevice device = deviceInfo.getDevice();
            String name = device.getName();
            tvDeviceName.setText(name != null ? name : "未知设备");
            tvDeviceAddress.setText(device.getAddress());
            tvDeviceRssi.setText("信号强度: " + deviceInfo.getRssi() + " dBm");
        }
    }

    // 设备点击监听接口
    public interface OnDeviceClickListener {
        void onDeviceClick(BluetoothDevice device);
    }

    public void setOnDeviceClickListener(OnDeviceClickListener listener) {
        this.onDeviceClickListener = listener;
    }

    @NonNull
    @Override
    public DeviceViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_spp_device, parent, false);
        return new DeviceViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull DeviceViewHolder holder, int position) {
        DeviceInfo deviceInfo = deviceList.get(position);
        holder.bind(deviceInfo);
        
        // 设置整个项的点击事件
        holder.itemView.setOnClickListener(v -> {
            if (onDeviceClickListener != null) {
                onDeviceClickListener.onDeviceClick(deviceInfo.getDevice());
            }
        });
        
        // 设置连接按钮的点击事件
        holder.btnConnect.setOnClickListener(v -> {
            if (onDeviceClickListener != null) {
                onDeviceClickListener.onDeviceClick(deviceInfo.getDevice());
            }
        });
    }

    @Override
    public int getItemCount() {
        return deviceList.size();
    }

    /**
     * 添加或更新设备
     * @param device 蓝牙设备
     * @param rssi 信号强度
     */
    public void addDevice(BluetoothDevice device, int rssi) {
        DeviceInfo newDeviceInfo = new DeviceInfo(device, rssi);
        int index = deviceList.indexOf(newDeviceInfo);
        
        if (index >= 0) {
            // 更新已存在的设备信息
            deviceList.get(index).updateRssi(rssi);
            notifyItemChanged(index);
        } else {
            // 添加新设备
            deviceList.add(newDeviceInfo);
            notifyItemInserted(deviceList.size() - 1);
        }
    }

    /**
     * 清空设备列表
     */
    public void clearDevices() {
        deviceList.clear();
        notifyDataSetChanged();
    }
} 