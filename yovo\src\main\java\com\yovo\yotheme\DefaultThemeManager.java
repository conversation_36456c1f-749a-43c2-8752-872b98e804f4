package com.yovo.yotheme;

import android.content.Context;

/**
 * 主题管理接口的默认实现
 */
public class DefaultThemeManager implements ThemeManager {
    private static volatile DefaultThemeManager instance;
    
    // 默认主题颜色
    private int primaryColor = 0xFF0A59F7; // 宇宙蓝（默认）
    private int secondaryColor = 0xFF0048D4; // 深蓝（按下）
    
    private DefaultThemeManager() {
        // 私有构造器防止外部实例化
    }
    
    /**
     * 获取单例实例
     */
    public static DefaultThemeManager getInstance() {
        if (instance == null) {
            synchronized (DefaultThemeManager.class) {
                if (instance == null) {
                    instance = new DefaultThemeManager();
                }
            }
        }
        return instance;
    }
    
    @Override
    public YoSwitch getSwitch(Context context) {
        YoSwitch yoSwitch = new YoSwitch(context);
        yoSwitch.setTrackOnColor(primaryColor);
        // 设置统一的开关尺寸为50dp x 25dp
        yoSwitch.setMinimumWidth((int)(50 * context.getResources().getDisplayMetrics().density));
        yoSwitch.setMinimumHeight((int)(25 * context.getResources().getDisplayMetrics().density));
        return yoSwitch;
    }
    
    @Override
    public YoButton getButton(Context context) {
        YoButton yoButton = new YoButton(context);
        yoButton.setButtonColor(primaryColor, secondaryColor);
        return yoButton;
    }
    
    @Override
    public void setThemeColors(int primaryColor, int secondaryColor) {
        this.primaryColor = primaryColor;
        this.secondaryColor = secondaryColor;
    }
} 