<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.8.0" type="baseline" client="gradle" dependencies="false" name="AGP (8.8.0)" variant="all" version="8.8.0">

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="        this.name = device.getName() != null ? device.getName() : &quot;未知设备&quot;;"
        errorLine2="                    ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/hs01/model/BluetoothDeviceModel.java"
            line="25"
            column="21"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="        this.name = device.getName() != null ? device.getName() : &quot;未知设备&quot;;"
        errorLine2="                                               ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/hs01/model/BluetoothDeviceModel.java"
            line="25"
            column="48"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        message="A newer version of com.android.application than 8.8.0 is available: 8.10.1. (There is also a newer version of 8.8.𝑥 available, if upgrading to 8.10.1 is difficult: 8.8.2)"
        errorLine1="agp = &quot;8.8.0&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        message="A newer version of com.android.application than 8.8.0 is available: 8.10.1. (There is also a newer version of 8.8.𝑥 available, if upgrading to 8.10.1 is difficult: 8.8.2)"
        errorLine1="agp = &quot;8.8.0&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        message="A newer version of com.android.application than 8.8.0 is available: 8.10.1. (There is also a newer version of 8.8.𝑥 available, if upgrading to 8.10.1 is difficult: 8.8.2)"
        errorLine1="agp = &quot;8.8.0&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        message="A newer version of com.android.library than 8.8.0 is available: 8.10.1. (There is also a newer version of 8.8.𝑥 available, if upgrading to 8.10.1 is difficult: 8.8.2)"
        errorLine1="agp = &quot;8.8.0&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        message="A newer version of com.android.library than 8.8.0 is available: 8.10.1. (There is also a newer version of 8.8.𝑥 available, if upgrading to 8.10.1 is difficult: 8.8.2)"
        errorLine1="agp = &quot;8.8.0&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        message="A newer version of com.android.library than 8.8.0 is available: 8.10.1. (There is also a newer version of 8.8.𝑥 available, if upgrading to 8.10.1 is difficult: 8.8.2)"
        errorLine1="agp = &quot;8.8.0&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1"
        errorLine1="appcompat = &quot;1.7.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="../gradle/libs.versions.toml"
            line="6"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1"
        errorLine1="appcompat = &quot;1.7.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="../gradle/libs.versions.toml"
            line="6"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1"
        errorLine1="appcompat = &quot;1.7.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="../gradle/libs.versions.toml"
            line="6"
            column="13"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="        notifyDataSetChanged();"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/hs01/utils/BluetoothDeviceAdapter.java"
            line="51"
            column="9"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="        notifyDataSetChanged();"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/hs01/utils/BluetoothDeviceAdapter.java"
            line="84"
            column="9"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/comp_background_gray` with a theme that also paints a background (inferred theme is `@style/Theme.HS01`)"
        errorLine1="    android:background=&quot;@color/comp_background_gray&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_bluetooth_connect.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/comp_background_gray` with a theme that also paints a background (inferred theme is `@style/Theme.HS01`)"
        errorLine1="    android:background=&quot;@color/comp_background_gray&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `?attr/selectableItemBackground` with a theme that also paints a background (inferred theme is `@style/Theme.HS01`)"
        errorLine1="    android:background=&quot;?attr/selectableItemBackground&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_bluetooth_device.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.black` appears to be unused"
        errorLine1="    &lt;color name=&quot;black&quot;>#FF000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.white` appears to be unused"
        errorLine1="    &lt;color name=&quot;white&quot;>#FFFFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.warning` appears to be unused"
        errorLine1="    &lt;color name=&quot;warning&quot;>#ffe84026&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="10"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.alert` appears to be unused"
        errorLine1="    &lt;color name=&quot;alert&quot;>#ffed6f21&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="11"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.confirm` appears to be unused"
        errorLine1="    &lt;color name=&quot;confirm&quot;>#ff64bb5c&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="12"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.font_tertiary` appears to be unused"
        errorLine1="    &lt;color name=&quot;font_tertiary&quot;>#66000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="17"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.font_fourth` appears to be unused"
        errorLine1="    &lt;color name=&quot;font_fourth&quot;>#33000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="18"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.font_emphasize` appears to be unused"
        errorLine1="    &lt;color name=&quot;font_emphasize&quot;>#ff0a59f7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="19"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.font_on_primary` appears to be unused"
        errorLine1="    &lt;color name=&quot;font_on_primary&quot;>#ffffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="20"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.font_on_secondary` appears to be unused"
        errorLine1="    &lt;color name=&quot;font_on_secondary&quot;>#99ffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="21"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.font_on_tertiary` appears to be unused"
        errorLine1="    &lt;color name=&quot;font_on_tertiary&quot;>#66ffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="22"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.font_on_fourth` appears to be unused"
        errorLine1="    &lt;color name=&quot;font_on_fourth&quot;>#33ffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="23"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.icon_primary` appears to be unused"
        errorLine1="    &lt;color name=&quot;icon_primary&quot;>#e5000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="26"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.icon_secondary` appears to be unused"
        errorLine1="    &lt;color name=&quot;icon_secondary&quot;>#99000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="27"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.icon_tertiary` appears to be unused"
        errorLine1="    &lt;color name=&quot;icon_tertiary&quot;>#66000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="28"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.icon_fourth` appears to be unused"
        errorLine1="    &lt;color name=&quot;icon_fourth&quot;>#33000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="29"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.icon_emphasize` appears to be unused"
        errorLine1="    &lt;color name=&quot;icon_emphasize&quot;>#ff0a59f7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="30"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.icon_sub_emphasize` appears to be unused"
        errorLine1="    &lt;color name=&quot;icon_sub_emphasize&quot;>#660a59f7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="31"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.icon_on_primary` appears to be unused"
        errorLine1="    &lt;color name=&quot;icon_on_primary&quot;>#ffffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="32"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.icon_on_secondary` appears to be unused"
        errorLine1="    &lt;color name=&quot;icon_on_secondary&quot;>#99ffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="33"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.icon_on_tertiary` appears to be unused"
        errorLine1="    &lt;color name=&quot;icon_on_tertiary&quot;>#66ffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="34"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.icon_on_fourth` appears to be unused"
        errorLine1="    &lt;color name=&quot;icon_on_fourth&quot;>#33ffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="35"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.background_primary` appears to be unused"
        errorLine1="    &lt;color name=&quot;background_primary&quot;>#ffffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="38"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.background_secondary` appears to be unused"
        errorLine1="    &lt;color name=&quot;background_secondary&quot;>#fff1f3f5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="39"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.background_tertiary` appears to be unused"
        errorLine1="    &lt;color name=&quot;background_tertiary&quot;>#ffe5e5ea&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="40"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.background_fourth` appears to be unused"
        errorLine1="    &lt;color name=&quot;background_fourth&quot;>#ffd1d1d6&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="41"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.background_emphasize` appears to be unused"
        errorLine1="    &lt;color name=&quot;background_emphasize&quot;>#ff0a59f7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="42"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_foreground_primary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_foreground_primary&quot;>#ff000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="45"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_background_primary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_background_primary&quot;>#ffffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="46"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_background_primary_trans` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_background_primary_trans&quot;>#ffffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="47"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_background_primary_contrary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_background_primary_contrary&quot;>#ffffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="48"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_background_secondary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_background_secondary&quot;>#19000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="50"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_background_tertiary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_background_tertiary&quot;>#0c000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="51"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_background_emphasize` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_background_emphasize&quot;>#ff0a59f7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="52"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_background_neutral` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_background_neutral&quot;>#ff000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="53"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_emphasize_secondary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_emphasize_secondary&quot;>#330a59f7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="54"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_emphasize_tertiary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_emphasize_tertiary&quot;>#190a59f7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="55"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_divider` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_divider&quot;>#33000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="56"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_common_contrary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_common_contrary&quot;>#ffffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="57"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_background_focus` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_background_focus&quot;>#fff1f3f5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="58"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_focused_primary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_focused_primary&quot;>#e5000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="59"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_focused_secondary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_focused_secondary&quot;>#99000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="60"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_focused_tertiary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_focused_tertiary&quot;>#66000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="61"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.interactive_hover` appears to be unused"
        errorLine1="    &lt;color name=&quot;interactive_hover&quot;>#0c000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="64"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.interactive_pressed` appears to be unused"
        errorLine1="    &lt;color name=&quot;interactive_pressed&quot;>#19000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="65"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.interactive_focus` appears to be unused"
        errorLine1="    &lt;color name=&quot;interactive_focus&quot;>#ff0a59f7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="66"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.interactive_active` appears to be unused"
        errorLine1="    &lt;color name=&quot;interactive_active&quot;>#ff0a59f7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="67"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.interactive_select` appears to be unused"
        errorLine1="    &lt;color name=&quot;interactive_select&quot;>#33000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="68"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.interactive_click` appears to be unused"
        errorLine1="    &lt;color name=&quot;interactive_click&quot;>#19000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="69"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.ImmersiveSystemBars` appears to be unused"
        errorLine1="    &lt;style name=&quot;ImmersiveSystemBars&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="        &lt;ImageView"
        errorLine2="         ~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_bluetooth_connect.xml"
            line="22"
            column="10"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="        &lt;ImageView"
        errorLine2="         ~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_bluetooth_connect.xml"
            line="50"
            column="10"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="src/main/res/layout/item_bluetooth_device.xml"
            line="10"
            column="6"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        holder.tvDeviceRssi.setText(device.getRssi() + &quot; dBm&quot;);"
        errorLine2="                                    ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ggec/hs01/utils/BluetoothDeviceAdapter.java"
            line="118"
            column="37"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        holder.tvDeviceRssi.setText(device.getRssi() + &quot; dBm&quot;);"
        errorLine2="                                                       ~~~~~~">
        <location
            file="src/main/java/com/ggec/hs01/utils/BluetoothDeviceAdapter.java"
            line="118"
            column="56"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;蓝牙连接&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;蓝牙连接&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_bluetooth_connect.xml"
            line="40"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;正在搜索蓝牙设备...&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;正在搜索蓝牙设备...&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_bluetooth_connect.xml"
            line="73"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;未发现蓝牙设备&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;未发现蓝牙设备&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_bluetooth_connect.xml"
            line="99"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;蓝牙连接&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;蓝牙连接&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="17"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;佩戴检测&quot;, should use `@string` resource"
        errorLine1="                    android:text=&quot;佩戴检测&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="53"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;游戏模式&quot;, should use `@string` resource"
        errorLine1="                    android:text=&quot;游戏模式&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="75"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;自动音量调节&quot;, should use `@string` resource"
        errorLine1="                    android:text=&quot;自动音量调节&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="97"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;VAD唤醒&quot;, should use `@string` resource"
        errorLine1="                    android:text=&quot;VAD唤醒&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="119"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;听力保护&quot;, should use `@string` resource"
        errorLine1="                    android:text=&quot;听力保护&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="141"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;音质模式&quot;, should use `@string` resource"
        errorLine1="                    android:text=&quot;音质模式&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="163"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;掉落提醒（TBD）&quot;, should use `@string` resource"
        errorLine1="                    android:text=&quot;掉落提醒（TBD）&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="186"
            column="21"/>
    </issue>

</issues>
