package com.ggec.sppservice.utils;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import android.content.Context;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import com.ggec.sppservice.manager.SppScanManager;
import com.ggec.sppservice.scanner.SppScanCallback;
import com.ggec.sppservice.scanner.SppScanConfig;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * SPP工具类
 * 提供SPP连接和通信的辅助方法
 */
public class SppUtils {
    private static final String TAG = "SppUtils";
    
    /**
     * 检查蓝牙是否可用并已启用
     * @return 蓝牙是否已启用
     */
    public static boolean isBluetoothEnabled() {
        BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
        return adapter != null && adapter.isEnabled();
    }
    
    /**
     * 创建SPP连接
     * @param device 蓝牙设备
     * @return 蓝牙Socket，连接失败则返回null
     */
    public static BluetoothSocket createSppConnection(BluetoothDevice device) {
        try {
            return device.createRfcommSocketToServiceRecord(SppScanConfig.SPP_UUID);
        } catch (IOException e) {
            YoBTSDKLog.e(TAG, "创建SPP连接失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 连接到SPP设备
     * @param socket 蓝牙Socket
     * @return 连接是否成功
     */
    public static boolean connect(BluetoothSocket socket) {
        if (socket == null) {
            return false;
        }
        
        try {
            socket.connect();
            return true;
        } catch (IOException e) {
            YoBTSDKLog.e(TAG, "连接失败: " + e.getMessage());
            try {
                socket.close();
            } catch (IOException closeException) {
                YoBTSDKLog.e(TAG, "关闭Socket失败: " + closeException.getMessage());
            }
            return false;
        }
    }
    
    /**
     * 关闭SPP连接
     * @param socket 蓝牙Socket
     */
    public static void closeConnection(BluetoothSocket socket) {
        if (socket != null) {
            try {
                socket.close();
            } catch (IOException e) {
                YoBTSDKLog.e(TAG, "关闭Socket失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 获取输入流
     * @param socket 蓝牙Socket
     * @return 输入流，失败则返回null
     */
    public static InputStream getInputStream(BluetoothSocket socket) {
        if (socket == null) {
            return null;
        }
        
        try {
            return socket.getInputStream();
        } catch (IOException e) {
            YoBTSDKLog.e(TAG, "获取输入流失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取输出流
     * @param socket 蓝牙Socket
     * @return 输出流，失败则返回null
     */
    public static OutputStream getOutputStream(BluetoothSocket socket) {
        if (socket == null) {
            return null;
        }
        
        try {
            return socket.getOutputStream();
        } catch (IOException e) {
            YoBTSDKLog.e(TAG, "获取输出流失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 发送数据
     * @param socket 蓝牙Socket
     * @param data 要发送的数据
     * @return 是否发送成功
     */
    public static boolean sendData(BluetoothSocket socket, byte[] data) {
        OutputStream out = getOutputStream(socket);
        if (out == null || data == null) {
            return false;
        }
        
        try {
            out.write(data);
            out.flush();
            return true;
        } catch (IOException e) {
            YoBTSDKLog.e(TAG, "发送数据失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 接口：SPP连接回调
     */
    public interface SppConnectionCallback {
        void onScanStarted();
        void onScanFinished();
        void onConnected(BluetoothDevice device, BluetoothSocket socket);
        void onError(String message);
    }
} 