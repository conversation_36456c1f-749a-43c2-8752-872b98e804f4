package com.ggec.bleservice.setting.batterycmd;

import android.os.Handler;
import android.os.Looper;

import com.ggec.bleservice.setting.CommandManager;
import com.ggec.bleservice.setting.CommandQueueCenter;
import com.ggec.bleservice.setting.cmdmanager.BatterySettingsManager;
import com.ggec.bleservice.setting.aggregatorcmd.HomePageStatusAggregator;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-29
 * Description: 
 * 电池信息监听器
 * 负责监听设备主动上报的电量及充电状态变化，并通知UI层
 * 支持防抖处理，避免短时间内频繁启停
 */
public class BatteryInfoListener implements
        CommandManager.BatteryInfoCallback,
        CommandManager.CaseChargingCallback,
        CommandQueueCenter.QueueStatusListener {

    private static final String TAG = "BatteryInfoListener";
    
    // 防抖间隔时间（毫秒）- 确保两次启动/停止操作间隔至少1200ms
    private static final long DEBOUNCE_INTERVAL_MS = 1200;

    private static volatile BatteryInfoListener instance;

    // 命令管理器实例
    private final CommandManager commandManager;
    
    // BatterySettingsManager 实例
    private final BatterySettingsManager batterySettingsManager;
    
    // 命令队列中心实例
    private final CommandQueueCenter commandQueueCenter;
    
    // 主线程Handler
    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    // 当前电量和充电状态 - 修改为String类型支持"--"显示
    private String leftBatteryLevel = "--";
    private String rightBatteryLevel = "--";
    private String caseBatteryLevel = "--";
    private boolean isCaseCharging = false;

    // UI更新的回调
    private BatteryStateCallback uiCallback;
    
    // 标志位：是否正在等待首次电量获取完成
    private boolean waitingForInitialBattery = false;
    
    // 标志位：是否已经接收到首次电量数据
    private boolean hasReceivedInitialBattery = false;
    
    // 标志位：是否已经接收到首次充电状态数据  
    private boolean hasReceivedInitialCharging = false;
    
    // 标志位：是否正在等待队列空闲
    private boolean waitingForQueueIdle = false;
    
    // 待启动的回调，用于队列空闲后启动监听
    private BatteryStateCallback pendingCallback = null;
    
    // 电量监听启动完成的回调列表
    private final java.util.List<Runnable> startupCompletionCallbacks = new java.util.ArrayList<>();
    
    // 防抖相关：记录上次操作时间
    private long lastStartTime = 0;
    private long lastStopTime = 0;

    public interface BatteryStateCallback {
        void onBatteryStateChanged();
    }

    private BatteryInfoListener() {
        this.commandManager = CommandManager.getInstance();
        this.batterySettingsManager = BatterySettingsManager.getInstance();
        this.commandQueueCenter = CommandQueueCenter.getInstance();
    }

    public static BatteryInfoListener getInstance() {
        if (instance == null) {
            synchronized (BatteryInfoListener.class) {
                if (instance == null) {
                    instance = new BatteryInfoListener();
                }
            }
        }
        return instance;
    }

    /**
     * 开始监听电池状态变化
     */
    public void startListening(BatteryStateCallback callback) {
        // 防抖检查：如果距离上次启动时间不足500ms，则拒绝执行
        long currentTime = System.currentTimeMillis();
        long timeSinceLastStart = currentTime - lastStartTime;
        
        if (timeSinceLastStart < DEBOUNCE_INTERVAL_MS && lastStartTime > 0) {
            long remainingTime = DEBOUNCE_INTERVAL_MS - timeSinceLastStart;
            YoBTSDKLog.w(TAG, "启动请求过于频繁，距离上次启动仅" + timeSinceLastStart + "ms，需等待" + remainingTime + "ms后再试");
            return;
        }
        
        // 如果监听器已经在运行，且回调相同，则忽略重复启动请求
        if (isActivelyListening() && uiCallback == callback) {
            YoBTSDKLog.d(TAG, "电量监听已在运行中，忽略重复启动请求");
            return;
        }
        
        // 更新最后启动时间
        lastStartTime = currentTime;
        
        YoBTSDKLog.d(TAG, "开始监听电池状态");
        
        // 检查首页聚合器是否正在运行，如果是则拒绝启动监听，确保互斥
        if (isHomePageAggregatorRunning()) {
            YoBTSDKLog.w(TAG, "首页聚合器正在运行中，暂不启动电量监听以避免冲突");
            return;
        }
        
        // 检查命令队列状态
        if (commandQueueCenter.isQueueEmptyAndIdle()) {
            // 队列为空且没有正在执行的命令，立即启动
            //YoBTSDKLog.d(TAG, "命令队列空闲，立即启动电量监听");
            startListeningImmediately(callback);
        } else {
            // 有命令正在执行，等待队列空闲
            YoBTSDKLog.d(TAG, "命令队列忙碌，等待队列空闲后启动电量监听");
            waitingForQueueIdle = true;
            pendingCallback = callback;
            
            // 添加队列状态监听器
            commandQueueCenter.addQueueStatusListener(this);
        }
    }
    
    /**
     * 检查首页聚合器是否正在运行
     * @return true if aggregator is running, false otherwise
     */
    private boolean isHomePageAggregatorRunning() {
        try {
            // 简化检查：直接使用CommandQueueCenter检查是否有GlobalStatusCommand
            return commandQueueCenter.hasGlobalStatusCommand();
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "检查聚合器状态时发生异常", e);
            // 如果检查状态时发生异常，为了安全起见，假设没有冲突
            return false;
        }
    }
    
    /**
     * 立即启动电量监听（内部方法）
     */
    private void startListeningImmediately(BatteryStateCallback callback) {
        YoBTSDKLog.d(TAG, "立即启动电量监听");
        this.uiCallback = callback;
        
        // 重置首次获取标志
        waitingForInitialBattery = true;
        hasReceivedInitialBattery = false;
        hasReceivedInitialCharging = false;
        
        commandManager.setBatteryInfoCallback(this);
        commandManager.setCaseChargingCallback(this);
        batterySettingsManager.startMonitoring();
        
        // 主动获取一次电池信息
        batterySettingsManager.getFullBatteryInfo();
    }

    /**
     * 停止监听
     */
    public void stopListening() {
        // 防抖检查：如果距离上次停止时间不足500ms，则拒绝执行
        long currentTime = System.currentTimeMillis();
        long timeSinceLastStop = currentTime - lastStopTime;
        
        if (timeSinceLastStop < DEBOUNCE_INTERVAL_MS && lastStopTime > 0) {
            long remainingTime = DEBOUNCE_INTERVAL_MS - timeSinceLastStop;
            YoBTSDKLog.w(TAG, "停止请求过于频繁，距离上次停止仅" + timeSinceLastStop + "ms，需等待" + remainingTime + "ms后再试");
            return;
        }
        
        // 如果监听器已经停止，则忽略重复停止请求
        if (!isActivelyListening() && !waitingForQueueIdle && !waitingForInitialBattery) {
            YoBTSDKLog.d(TAG, "电量监听已停止，忽略重复停止请求");
            return;
        }
        
        // 更新最后停止时间
        lastStopTime = currentTime;
        
        YoBTSDKLog.d(TAG, "停止监听电池状态");
        this.uiCallback = null;
        
        // 重置标志位
        waitingForInitialBattery = false;
        hasReceivedInitialBattery = false;
        hasReceivedInitialCharging = false;
        waitingForQueueIdle = false;
        pendingCallback = null;
        
        // 如果有等待的启动完成回调，在停止监听时立即执行它们，避免无效等待
        synchronized (startupCompletionCallbacks) {
            if (!startupCompletionCallbacks.isEmpty()) {
                YoBTSDKLog.w(TAG, "电量监听被停止，立即执行 " + startupCompletionCallbacks.size() + " 个等待的启动完成回调");
                
                for (Runnable callback : startupCompletionCallbacks) {
                    try {
                        callback.run();
                    } catch (Exception e) {
                        YoBTSDKLog.e(TAG, "执行启动完成回调时发生异常", e);
                    }
                }
                startupCompletionCallbacks.clear();
            }
        }
        
        // 移除队列状态监听器
        commandQueueCenter.removeQueueStatusListener(this);
        
        // 移除回调，避免内存泄漏
        commandManager.setBatteryInfoCallback(null);
        commandManager.setCaseChargingCallback(null);
        batterySettingsManager.stopMonitoring();
        
        // 清除所有pending的handler任务
        mainHandler.removeCallbacksAndMessages(null);
    }

    @Override
    public void onBatteryInfoUpdated(int code, String leftLevel, String rightLevel, String caseLevel) {
        YoBTSDKLog.d(TAG, "接收到电量更新: L=" + leftLevel + ", R=" + rightLevel + ", Case=" + caseLevel);
        
        // 验证电量范围并转换为字符串
        this.leftBatteryLevel = (!leftLevel.equals("--") && leftLevel.matches("\\d+")) ? leftLevel : "--";
        this.rightBatteryLevel = (!rightLevel.equals("--") && rightLevel.matches("\\d+")) ? rightLevel : "--";
        this.caseBatteryLevel = (!caseLevel.equals("--") && caseLevel.matches("\\d+")) ? caseLevel : "--";

        // 标记已接收到首次电量数据
        if (waitingForInitialBattery && !hasReceivedInitialBattery) {
            hasReceivedInitialBattery = true;
            YoBTSDKLog.d(TAG, "首次电量数据获取完成");
            checkAndPauseQueueIfReady();
        }

        if (uiCallback != null) {
            uiCallback.onBatteryStateChanged();
        }
    }

    @Override
    public void onCaseChargingUpdated(int code, boolean isCharging) {
        YoBTSDKLog.d(TAG, "接收到充电状态更新: " + isCharging);
        this.isCaseCharging = isCharging;
        
        // 标记已接收到首次充电状态数据
        if (waitingForInitialBattery && !hasReceivedInitialCharging) {
            hasReceivedInitialCharging = true;
            YoBTSDKLog.d(TAG, "首次充电状态数据获取完成");
            checkAndPauseQueueIfReady();
        }
        
        if (uiCallback != null) {
            uiCallback.onBatteryStateChanged();
        }
    }
    
    /**
     * 检查是否首次数据获取完成，如果完成则暂停队列
     */
    private void checkAndPauseQueueIfReady() {
        if (waitingForInitialBattery && hasReceivedInitialBattery && hasReceivedInitialCharging) {
            YoBTSDKLog.i(TAG, "电量监听启动完成（电量和充电状态数据都已获取），暂停命令队列500ms以确保系统稳定");
            waitingForInitialBattery = false;
            
            // 暂停命令队列500ms
            CommandQueueCenter.getInstance().pauseTemporarily(500);
            
            // 延迟通知启动完成回调，确保队列暂停生效
            mainHandler.postDelayed(() -> {
                YoBTSDKLog.d(TAG, "电量监听启动完全完成，通知等待的回调");
                notifyStartupCompletionCallbacks();
            }, 100); // 延迟100ms确保暂停操作已生效
        }
    }

    /**
     * 获取左耳电量
     * @return 电量字符串（百分比数字或"--"）
     */
    public String getLeftBatteryLevel() {
        return leftBatteryLevel;
    }

    /**
     * 获取右耳电量
     * @return 电量字符串（百分比数字或"--"）
     */
    public String getRightBatteryLevel() {
        return rightBatteryLevel;
    }

    /**
     * 获取充电盒电量
     * @return 电量字符串（百分比数字或"--"）
     */
    public String getCaseBatteryLevel() {
        return caseBatteryLevel;
    }

    /**
     * 获取充电盒是否在充电
     * @return 是否在充电
     */
    public boolean isCaseCharging() {
        return isCaseCharging;
    }
    
    /**
     * 检查是否正在等待命令队列空闲
     * @return 是否正在等待队列空闲
     */
    public boolean isWaitingForQueueIdle() {
        return waitingForQueueIdle;
    }
    
    /**
     * 检查是否正在等待首次电量数据获取
     * @return 是否正在等待首次电量获取完成
     */
    public boolean isWaitingForInitialBattery() {
        return waitingForInitialBattery;
    }
    
    /**
     * 检查电量监听器是否正在活跃监听中
     * @return 是否正在监听
     */
    public boolean isActivelyListening() {
        return uiCallback != null;
    }
    
    /**
     * 添加电量监听启动完成回调
     * 如果监听已经启动完成，回调会立即执行
     * @param callback 完成回调
     */
    public void addStartupCompletionCallback(Runnable callback) {
        if (callback == null) return;
        
        synchronized (startupCompletionCallbacks) {
            // 如果电量监听已经完成启动（不在等待状态且有活跃监听），立即执行回调
            if (!waitingForInitialBattery && !waitingForQueueIdle && isActivelyListening()) {
                YoBTSDKLog.d(TAG, "电量监听已启动完成，立即执行完成回调");
                try {
                    callback.run();
                } catch (Exception e) {
                    YoBTSDKLog.e(TAG, "执行启动完成回调时发生异常", e);
                }
            } else {
                // 否则添加到待执行列表
                startupCompletionCallbacks.add(callback);
                YoBTSDKLog.d(TAG, "添加电量监听启动完成回调到等待列表");
            }
        }
    }
    
    /**
     * 通知所有等待的启动完成回调
     */
    private void notifyStartupCompletionCallbacks() {
        synchronized (startupCompletionCallbacks) {
            if (!startupCompletionCallbacks.isEmpty()) {
                YoBTSDKLog.d(TAG, "通知 " + startupCompletionCallbacks.size() + " 个启动完成回调");
                
                for (Runnable callback : startupCompletionCallbacks) {
                    try {
                        callback.run();
                    } catch (Exception e) {
                        YoBTSDKLog.e(TAG, "执行启动完成回调时发生异常", e);
                    }
                }
                startupCompletionCallbacks.clear();
            }
        }
    }
    
    // ======================== CommandQueueCenter.QueueStatusListener 实现 ========================
    
    @Override
    public void onQueueBecameIdle() {
        if (waitingForQueueIdle && pendingCallback != null) {
            YoBTSDKLog.d(TAG, "命令队列变为空闲，100ms后启动电量监听");
            
            // 等待100ms后启动，确保系统稳定
            mainHandler.postDelayed(() -> {
                if (waitingForQueueIdle && pendingCallback != null) {
                    YoBTSDKLog.d(TAG, "延迟后启动电量监听");
                    
                    // 移除队列监听器
                    commandQueueCenter.removeQueueStatusListener(this);
                    
                    // 重置等待标志
                    waitingForQueueIdle = false;
                    BatteryStateCallback callback = pendingCallback;
                    pendingCallback = null;
                    
                    // 启动电量监听
                    startListeningImmediately(callback);
                }
            }, 100); // 等待100ms
        }
    }
    
    @Override
    public void onQueueBecameBusy() {
        // 队列变为忙碌状态，这里不需要特殊处理
        // 但是记录日志以便调试
        if (waitingForQueueIdle) {
            YoBTSDKLog.d(TAG, "队列变为忙碌状态（等待期间）");
        }
    }
} 