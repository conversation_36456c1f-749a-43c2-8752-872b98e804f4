package com.ggec.sppservice.connect;

import android.bluetooth.BluetoothA2dp;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.Looper;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import com.ggec.sppservice.utils.SppUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * SPP连接管理器
 * 负责管理SPP设备的连接和配对
 */
public class SppConnectManager {
    private static final String TAG = "SppConnectManager";
    
    private static volatile SppConnectManager INSTANCE;
    
    private final Context context;
    private final ExecutorService executor;
    private final Handler mainHandler;

    // 持有监听器的列表
    private final List<ClassicBTConnectionStateListener> connectionStateListeners = new ArrayList<>();
    
    // 用于监听断开事件的广播接收器
    private final BroadcastReceiver disconnectionReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
            if (device == null) {
                return;
            }

            if (BluetoothDevice.ACTION_ACL_CONNECTED.equals(action)) {
                YoBTSDKLog.d(TAG, "设备已连接: " + device.getName() + " [" + device.getAddress() + "]");
                notifyHeadsetIsConnected(device.getAddress());
            } else if (BluetoothDevice.ACTION_ACL_DISCONNECTED.equals(action)) {
                YoBTSDKLog.d(TAG, "设备已断开: " + device.getName() + " [" + device.getAddress() + "]");
                notifyHeadsetIsDisconnected(device.getAddress());
            } else if (BluetoothA2dp.ACTION_CONNECTION_STATE_CHANGED.equals(action)) {
                int state = intent.getIntExtra(BluetoothA2dp.EXTRA_STATE, BluetoothA2dp.STATE_DISCONNECTED);
                if (state == BluetoothA2dp.STATE_CONNECTED) {
                    notifyHeadsetIsConnected(device.getAddress());
                } else if (state == BluetoothA2dp.STATE_DISCONNECTED) {
                    notifyHeadsetIsDisconnected(device.getAddress());
                }
            }
        }
    };
    
    // 标记广播接收器是否已注册
    private boolean isDisconnectionReceiverRegistered = false;
    
    private void notifyHeadsetIsConnected(String macAddress) {
        synchronized (connectionStateListeners) {
            for (ClassicBTConnectionStateListener listener : connectionStateListeners) {
                listener.headsetIsConnected(macAddress);
            }
        }
    }

    // 通知所有监听器的方法
    private void notifyHeadsetIsDisconnected(String macAddress) {
        synchronized (connectionStateListeners) {
            for (ClassicBTConnectionStateListener listener : connectionStateListeners) {
                listener.headsetIsDisconnected(macAddress);
            }
        }
    }
    
    /**
     * 为SPP连接状态更改添加监听器
     * @param listener 要添加的监听器
     */
    public void addClassicBTConnectionStateListener(ClassicBTConnectionStateListener listener) {
        synchronized (connectionStateListeners) {
            if (!connectionStateListeners.contains(listener)) {
                connectionStateListeners.add(listener);
                if (!isDisconnectionReceiverRegistered) {
                    IntentFilter filter = new IntentFilter();
//                    filter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED);
//                    filter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED);
                    filter.addAction(BluetoothA2dp.ACTION_CONNECTION_STATE_CHANGED);
                    context.registerReceiver(disconnectionReceiver, filter);
                    isDisconnectionReceiverRegistered = true;
                    YoBTSDKLog.d(TAG, "连接状态接收器已注册");
                }
            }
        }
    }

    /**
     * 为SPP连接状态更改移除监听器
     * @param listener 要移除的监听器
     */
    public void removeClassicBTConnectionStateListener(ClassicBTConnectionStateListener listener) {
        synchronized (connectionStateListeners) {
            connectionStateListeners.remove(listener);
            if (connectionStateListeners.isEmpty() && isDisconnectionReceiverRegistered) {
                context.unregisterReceiver(disconnectionReceiver);
                isDisconnectionReceiverRegistered = false;
                YoBTSDKLog.d(TAG, "连接状态接收器已注销");
            }
        }
    }
    
    // 用于处理断开事件并通知监听器的回调
    private final SppConnectCallback internalConnectCallback = new SppConnectAdapter() {
        @Override
        public void onDisconnected(BluetoothDevice device) {
            YoBTSDKLog.d(TAG, "设备已断开: " + device.getName());
            notifyHeadsetIsDisconnected(device.getAddress());
        }
    };
    
    // 用于管理连接状态的处理器
    private final Handler connectionHandler = new Handler(Looper.getMainLooper());
    
    // 配置
    private SppConnectConfig connectConfig;
    
    // 连接相关
    private BluetoothDevice currentDevice;
    private BluetoothSocket currentSocket;
    private SppConnectCallback userConnectCallback;
    
    // 重试相关
    private int retryAttempts = 0;
    
    // 广播接收器
    private final BroadcastReceiver pairingReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (BluetoothDevice.ACTION_BOND_STATE_CHANGED.equals(action)) {
                BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                if (device == null || !device.equals(currentDevice)) {
                    return;
                }
                
                int bondState = intent.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, BluetoothDevice.ERROR);
                
                switch (bondState) {
                    case BluetoothDevice.BOND_BONDED:
                        YoBTSDKLog.d(TAG, "设备已配对: " + device.getName());
                        notifyPaired(device);
                        // 配对成功后继续连接
                        connectToDevice(device);
                        break;
                    case BluetoothDevice.BOND_NONE:
                        YoBTSDKLog.d(TAG, "设备未配对: " + device.getName());
                        break;
                }
            } else if (BluetoothDevice.ACTION_PAIRING_REQUEST.equals(action)) {
                BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                if (device != null && device.equals(currentDevice)) {
                    YoBTSDKLog.d(TAG, "收到配对请求: " + device.getName());
                    notifyPairingRequest(device);
                }
            }
        }
    };
    
    /**
     * 获取SppConnectManager实例
     * @param context 应用上下文
     * @return SppConnectManager实例
     */
    public static SppConnectManager getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (SppConnectManager.class) {
                if (INSTANCE == null) {
                    INSTANCE = new SppConnectManager(context.getApplicationContext());
                }
            }
        }
        return INSTANCE;
    }
    
    /**
     * 私有构造方法
     * @param context 应用上下文
     */
    private SppConnectManager(Context context) {
        this.context = context;
        this.executor = Executors.newSingleThreadExecutor();
        this.mainHandler = new Handler(Looper.getMainLooper());
        
        // 默认连接配置
        this.connectConfig = SppConnectConfig.newBuilder().build();
        
        // 注册广播接收器
        IntentFilter filter = new IntentFilter();
        filter.addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED);
        filter.addAction(BluetoothDevice.ACTION_PAIRING_REQUEST);
        context.registerReceiver(pairingReceiver, filter);
        
        YoBTSDKLog.d(TAG, "SppConnectManager已初始化");
    }
    
    /**
     * 设置连接配置
     * @param config 连接配置
     */
    public void setConnectConfig(SppConnectConfig config) {
        this.connectConfig = config != null ? config : SppConnectConfig.newBuilder().build();
    }
    
    /**
     * 连接SPP设备
     * @param device 蓝牙设备
     * @param callback 连接回调
     */
    public void connect(BluetoothDevice device, SppConnectCallback callback) {
        if (device == null) {
            if (callback != null) {
                callback.onConnectFailed(null, "设备为空");
            }
            return;
        }
        
        if (!SppUtils.isBluetoothEnabled()) {
            if (callback != null) {
                callback.onConnectFailed(device, "蓝牙未启用");
            }
            return;
        }
        
        // 保存当前连接设备和回调
        this.currentDevice = device;
        this.userConnectCallback = callback;
        this.retryAttempts = 0;
        
        notifyConnectStart(device);
        
        // 检查配对状态并进行连接
        checkBondStateAndConnect(device);
    }
    
    /**
     * 检查配对状态并连接
     * @param device 蓝牙设备
     */
    private void checkBondStateAndConnect(final BluetoothDevice device) {
        executor.execute(() -> {
            // 检查设备配对状态
            int bondState = device.getBondState();
            
            if (bondState == BluetoothDevice.BOND_BONDED) {
                // 已配对，直接连接
                YoBTSDKLog.d(TAG, "设备已配对，开始连接: " + device.getName());
                connectToDevice(device);
            } else if (bondState == BluetoothDevice.BOND_NONE && connectConfig.isAutoPair()) {
                // 未配对且启用自动配对，先进行配对
                YoBTSDKLog.d(TAG, "设备未配对，开始配对: " + device.getName());
                boolean pairingStarted = device.createBond();
                if (!pairingStarted) {
                    notifyConnectFailed(device, "启动配对失败");
                }
            } else {
                // 未配对且未启用自动配对，直接连接
                YoBTSDKLog.d(TAG, "设备未配对，但未启用自动配对，直接连接: " + device.getName());
                connectToDevice(device);
            }
        });
    }
    
    /**
     * 连接到设备
     * @param device 蓝牙设备
     */
    private void connectToDevice(final BluetoothDevice device) {
        executor.execute(() -> {
            try {
                BluetoothSocket socket = device.createRfcommSocketToServiceRecord(SppConnectConfig.SPP_UUID);
                
                // 使用超时
                final boolean[] connected = {false};
                final boolean[] timeout = {false};
                
                // 连接线程
                Thread connectThread = new Thread(() -> {
                    try {
                        socket.connect();
                        connected[0] = true;
                    } catch (IOException e) {
                        YoBTSDKLog.e(TAG, "连接失败: " + e.getMessage());
                    }
                });
                connectThread.start();
                
                // 等待连接完成或超时
                connectThread.join(connectConfig.getTimeoutMillis());
                
                if (!connected[0]) {
                    timeout[0] = true;
                    connectThread.interrupt();
                    try {
                        socket.close();
                    } catch (IOException e) {
                        // 忽略关闭异常
                    }
                    
                    // 判断是否需要重试
                    if (connectConfig.isRetryConnect() && retryAttempts < connectConfig.getRetryCount()) {
                        retryAttempts++;
                        YoBTSDKLog.d(TAG, "连接超时，重试第 " + retryAttempts + " 次");
                        // 延迟1秒重试
                        mainHandler.postDelayed(() -> connectToDevice(device), 1000);
                        return;
                    } else {
                        notifyConnectFailed(device, "连接超时");
                        return;
                    }
                }
                
                // 连接成功
                currentSocket = socket;
                notifyConnected(device, socket);
                
            } catch (Exception e) {
                YoBTSDKLog.e(TAG, "连接过程异常: " + e.getMessage());
                
                // 判断是否需要重试
                if (connectConfig.isRetryConnect() && retryAttempts < connectConfig.getRetryCount()) {
                    retryAttempts++;
                    YoBTSDKLog.d(TAG, "连接异常，重试第 " + retryAttempts + " 次");
                    // 延迟1秒重试
                    mainHandler.postDelayed(() -> connectToDevice(device), 1000);
                } else {
                    notifyConnectFailed(device, "连接异常: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 断开当前连接
     */
    public void disconnect() {
        if (currentDevice == null && currentSocket == null) {
            return;
        }

        final BluetoothDevice deviceToDisconnect = currentDevice;
        final BluetoothSocket socketToClose = currentSocket;

        currentDevice = null;
        currentSocket = null;

        executor.execute(() -> {
            if (socketToClose != null) {
                try {
                    socketToClose.close();
                    YoBTSDKLog.d(TAG, "断开连接成功" + (deviceToDisconnect != null ? deviceToDisconnect.getName() : "unknown device"));
                    if (deviceToDisconnect != null) {
                        notifyDisconnected(deviceToDisconnect);
                    }
                } catch (IOException e) {
                    YoBTSDKLog.e(TAG, "断开连接失败: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 获取当前已连接的Socket
     * @return 蓝牙Socket，未连接则返回null
     */
    public BluetoothSocket getConnectedSocket() {
        return currentSocket != null && currentSocket.isConnected() ? currentSocket : null;
    }
    
    /**
     * 获取当前连接的设备
     * @return 蓝牙设备，未连接则返回null
     */
    public BluetoothDevice getConnectedDevice() {
        return currentDevice;
    }
    
    /**
     * 是否已连接
     * @return 是否已连接
     */
    public boolean isConnected() {
        return currentSocket != null && currentSocket.isConnected();
    }
    
    /**
     * 释放资源
     */
    public void release() {
        // 断开连接
        disconnect();

        if (isDisconnectionReceiverRegistered) {
            context.unregisterReceiver(disconnectionReceiver);
            isDisconnectionReceiverRegistered = false;
        }
        
        // 注销广播接收器
        try {
            context.unregisterReceiver(pairingReceiver);
        } catch (Exception e) {
            // 忽略可能的未注册异常
            YoBTSDKLog.e(TAG, "unregisterReceiver failed", e);
        }
        
        // 关闭线程池
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
        
        // 清理回调和监听器
        userConnectCallback = null;
        connectionStateListeners.clear();
        
        // 清理单例实例
        synchronized (SppConnectManager.class) {
            INSTANCE = null;
        }
        
        YoBTSDKLog.d(TAG, "SppConnectManager资源已释放");
    }
    
    // 回调通知方法
    
    private void notifyConnectStart(final BluetoothDevice device) {
        if (userConnectCallback != null) {
            mainHandler.post(() -> userConnectCallback.onConnectStart(device));
        }
    }
    
    private void notifyPairingRequest(final BluetoothDevice device) {
        if (userConnectCallback != null) {
            mainHandler.post(() -> userConnectCallback.onPairingRequest(device));
        }
    }
    
    private void notifyPaired(final BluetoothDevice device) {
        if (userConnectCallback != null) {
            mainHandler.post(() -> userConnectCallback.onPaired(device));
        }
    }
    
    private void notifyConnected(final BluetoothDevice device, final BluetoothSocket socket) {
        if (userConnectCallback != null) {
            mainHandler.post(() -> userConnectCallback.onConnected(device, socket));
        }
    }
    
    private void notifyConnectFailed(final BluetoothDevice device, final String errorMsg) {
        if (userConnectCallback != null) {
            mainHandler.post(() -> userConnectCallback.onConnectFailed(device, errorMsg));
        }
    }
    
    private void notifyDisconnected(final BluetoothDevice device) {
        if (userConnectCallback != null && device != null) {
            mainHandler.post(() -> userConnectCallback.onDisconnected(device));
        }
    }
} 