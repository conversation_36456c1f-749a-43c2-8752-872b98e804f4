package com.ggec.bleservice.scanner.impl;

import android.Manifest;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothManager;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.Looper;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import androidx.annotation.RequiresPermission;

import com.ggec.bleservice.scanner.IScanner;
import com.ggec.bleservice.scanner.ScanCallback;
import com.ggec.bleservice.scanner.ScanConfig;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 蓝牙扫描器抽象基类，提供通用功能
 */
public abstract class BaseScanner implements IScanner {
    private static final String TAG = "BaseScanner";
    
    protected Context mContext;
    protected BluetoothAdapter mBluetoothAdapter;
    protected ScanCallback mScanCallback;
    protected boolean mScanning = false;
    protected Handler mHandler = new Handler(Looper.getMainLooper());
    protected ScanConfig mScanConfig = ScanConfig.createDefault();
    
    /**
     * 构造函数
     * @param context 上下文
     */
    public BaseScanner(Context context) {
        this.mContext = context;
        
        // 获取蓝牙适配器
        final BluetoothManager bluetoothManager =
                (BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE);
        if (bluetoothManager != null) {
            mBluetoothAdapter = bluetoothManager.getAdapter();
        }
        
        // 检查设备是否支持BLE
        if (!context.getPackageManager().hasSystemFeature(PackageManager.FEATURE_BLUETOOTH_LE)) {
            YoBTSDKLog.e(TAG, "设备不支持BLE");
        }
    }
    
    /**
     * 设置扫描配置
     * @param config 扫描配置
     */
    public void setScanConfig(ScanConfig config) {
        if (config != null) {
            this.mScanConfig = config;
        }
    }
    
    /**
     * 检查蓝牙是否可用
     * @return true如果可用
     */
    protected boolean isBluetoothAvailable() {
        return mBluetoothAdapter != null && mBluetoothAdapter.isEnabled();
    }
    
    /**
     * 扫描超时设置
     * @param timeoutMillis 超时时间（毫秒）
     */
    protected void setScanTimeout(long timeoutMillis) {
        mHandler.postDelayed(this::stopScan, timeoutMillis);
    }
    
    @Override
    @RequiresPermission(allOf = {Manifest.permission.BLUETOOTH, Manifest.permission.BLUETOOTH_ADMIN})
    public void startScan(ScanCallback callback) {
        if (!isBluetoothAvailable()) {
            YoBTSDKLog.e(TAG, "蓝牙不可用");
            return;
        }
        
        if (mScanning) {
            YoBTSDKLog.w(TAG, "扫描已经在进行中");
            return;
        }
        
        this.mScanCallback = callback;
        mScanning = true;
        
        if (callback != null) {
            mHandler.post(callback::onScanStart);
        }
    }
    
    @Override
    public void stopScan() {
        if (!mScanning) {
            return;
        }
        
        mScanning = false;
        
        if (mScanCallback != null) {
            mHandler.post(mScanCallback::onScanFinish);
        }
    }
    
    @Override
    public void close() {
        stopScan();
        mHandler.removeCallbacksAndMessages(null);
    }
} 