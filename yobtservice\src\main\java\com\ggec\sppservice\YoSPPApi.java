package com.ggec.sppservice;

import android.content.Context;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import com.ggec.sppservice.check.YoSPPMacDeviceChecker;
import com.ggec.sppservice.connect.ClassicBTConnectionStateListener;
import com.ggec.sppservice.connect.SppConnectCallback;
import com.ggec.sppservice.connect.SppConnectConfig;
import com.ggec.sppservice.connect.SppConnectManager;
import com.ggec.sppservice.manager.SppScanManager;
import com.ggec.sppservice.scanner.SppScanCallback;
import com.ggec.sppservice.scanner.SppScanConfig;
import com.ggec.sppservice.utils.SppUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * SPP服务API
 * 提供SPP服务的统一对外接口
 */
public class YoSPPApi {
    private static final String TAG = "YoSPPApi";
    
    private static volatile YoSPPApi INSTANCE;
    
    private final Context context;
    private final SppScanManager scanManager;
    private final SppConnectManager connectManager;
    private final YoSPPMacDeviceChecker deviceChecker;
    
    private SppScanCallback userScanCallback;
    private SppConnectCallback userConnectCallback;
    private final Map<ClassicBTDisconnectedListener, ClassicBTConnectionStateListener> disconnectListenerMap = new ConcurrentHashMap<>();
    private final Map<ClassicBTConnectedListener, ClassicBTConnectionStateListener> connectListenerMap = new ConcurrentHashMap<>();

    /**
     * 耳机断开连接事件的监听器。
     * 此公共接口提供给应用层，用于在经典蓝牙设备断开时接收通知。
     */
    public interface ClassicBTDisconnectedListener {
        /**
         * 当耳机/经典蓝牙设备断开时调用。
         * @param macAddress 已断开设备的MAC地址。
         */
        void headsetIsDisconnected(String macAddress);
    }

    /**
     * 耳机连接事件的监听器。
     * 此公共接口提供给应用层，用于在经典蓝牙设备连接时接收通知。
     */
    public interface ClassicBTConnectedListener {
        /**
         * 当耳机/经典蓝牙设备连接时调用。
         * @param macAddress 已连接设备的MAC地址。
         */
        void headsetIsConnected(String macAddress);
    }
    
    /**
     * 获取YoSPPApi实例
     * @param context 应用上下文
     * @return YoSPPApi实例
     */
    public static YoSPPApi getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (YoSPPApi.class) {
                if (INSTANCE == null) {
                    INSTANCE = new YoSPPApi(context.getApplicationContext());
                }
            }
        }
        return INSTANCE;
    }
    
    /**
     * 私有构造方法
     * @param context 应用上下文
     */
    private YoSPPApi(Context context) {
        this.context = context;
        this.scanManager = SppScanManager.getInstance(context);
        this.connectManager = SppConnectManager.getInstance(context);
        // 提前初始化设备检查器，确保Profile有足够时间初始化
        this.deviceChecker = YoSPPMacDeviceChecker.getInstance(context);
        this.deviceChecker.init();
        YoBTSDKLog.d(TAG, "YoSPPApi已初始化");
    }
    
    /**
     * 检查蓝牙是否可用
     * @return 蓝牙是否可用
     */
    public boolean isBluetoothEnabled() {
        return SppUtils.isBluetoothEnabled();
    }
    
    /**
     * 扫描SPP设备
     * 扫描结果会包含所有扫描到的设备，不区分是否已配对
     * 默认只显示经典蓝牙设备，过滤掉BLE设备
     * @param deviceNameFilter 设备名称过滤（可为null）
     * @param timeoutMillis 超时时间（毫秒，默认为12000毫秒）
     * @param callback 扫描回调
     */
    public void scanSppDevices(String deviceNameFilter, long timeoutMillis, SppScanCallback callback) {
        scanSppDevices(deviceNameFilter,null, timeoutMillis, true, callback);
    }

    /**
     * 扫描SPP设备
     * 扫描结果会包含所有扫描到的设备，不区分是否已配对
     * 默认只显示经典蓝牙设备，过滤掉BLE设备
     * @param deviceNameFilter 设备名称过滤（可为null）
     * @param deviceMacFilter 设备mac地址过滤（可为null）
     * @param timeoutMillis 超时时间（毫秒，默认为12000毫秒）
     * @param callback 扫描回调
     */
    public void scanSppDevices(String deviceNameFilter, String deviceMacFilter, long timeoutMillis, SppScanCallback callback) {
        scanSppDevices(deviceNameFilter, deviceMacFilter,timeoutMillis, true, callback);
    }
    
    /**
     * 扫描SPP设备
     * 扫描结果会包含所有扫描到的设备，不区分是否已配对
     * @param deviceNameFilter 设备名称过滤（可为null）
     * @param timeoutMillis 超时时间（毫秒，默认为12000毫秒）
     * @param classicBluetoothOnly 是否仅显示经典蓝牙设备
     * @param callback 扫描回调
     */
    public void scanSppDevices(String deviceNameFilter, String deviceMacFilter, long timeoutMillis,
                              boolean classicBluetoothOnly, SppScanCallback callback) {
        if (!isBluetoothEnabled()) {
            YoBTSDKLog.e(TAG, "无法扫描，蓝牙未启用");
            if (callback != null) {
                callback.onScanFinish();
            }
            return;
        }
        
        this.userScanCallback = callback;
        
        // 创建扫描配置
        SppScanConfig.Builder configBuilder = SppScanConfig.newBuilder();
        if (deviceNameFilter != null && !deviceNameFilter.isEmpty()) {
            configBuilder.nameFilter(deviceNameFilter);
        }
        if(deviceMacFilter != null && !deviceMacFilter.isEmpty()) {
            configBuilder.macFilter(deviceMacFilter);
        }
        if (timeoutMillis > 0) {
            configBuilder.timeout(timeoutMillis);
        }
        configBuilder.classicBluetoothOnly(classicBluetoothOnly);
        
        scanManager.setScanConfig(configBuilder.build());
        scanManager.startScan(callback);
    }
    
    /**
     * 停止扫描
     */
    public void stopScan() {
        scanManager.stopScan();
        userScanCallback = null;
    }
    
    /**
     * 连接SPP设备
     * @param device 要连接的蓝牙设备
     * @param callback 连接回调
     */
    public void connectDevice(BluetoothDevice device, SppConnectCallback callback) {
        if (!isBluetoothEnabled()) {
            YoBTSDKLog.e(TAG, "无法连接，蓝牙未启用");
            if (callback != null) {
                callback.onConnectFailed(device, "蓝牙未启用");
            }
            return;
        }
        
        this.userConnectCallback = callback;
        
        // 使用默认配置连接
        connectManager.connect(device, callback);
    }
    
    /**
     * 连接SPP设备（使用自定义配置）
     * @param device 要连接的蓝牙设备
     * @param config 连接配置
     * @param callback 连接回调
     */
    public void connectDevice(BluetoothDevice device, SppConnectConfig config, SppConnectCallback callback) {
        if (!isBluetoothEnabled()) {
            YoBTSDKLog.e(TAG, "无法连接，蓝牙未启用");
            if (callback != null) {
                callback.onConnectFailed(device, "蓝牙未启用");
            }
            return;
        }
        
        this.userConnectCallback = callback;
        
        // 设置连接配置
        connectManager.setConnectConfig(config);
        
        // 连接设备
        connectManager.connect(device, callback);
    }
    
    /**
     * 扫描并连接第一个匹配的SPP设备
     * @param deviceNameFilter 设备名称过滤（可为null）
     * @param scanTimeoutMillis 扫描超时时间（毫秒）
     * @param connectConfig 连接配置（可为null，使用默认配置）
     * @param callback 连接回调
     */
    public void scanAndConnect(String deviceNameFilter, long scanTimeoutMillis,
                              SppConnectConfig connectConfig, SppConnectCallback callback) {
        scanAndConnect(deviceNameFilter,null, scanTimeoutMillis, true, connectConfig, callback);
    }
    
    /**
     * 扫描并连接第一个匹配的SPP设备
     * @param deviceNameFilter 设备名称过滤（可为null）
     * @param scanTimeoutMillis 扫描超时时间（毫秒）
     * @param classicBluetoothOnly 是否仅显示经典蓝牙设备
     * @param deviceMacFilter 设备mac地址过滤（可为null）
     * @param connectConfig 连接配置（可为null，使用默认配置）
     * @param callback 连接回调
     */
    public void scanAndConnect(String deviceNameFilter, String deviceMacFilter, long scanTimeoutMillis,
                              boolean classicBluetoothOnly,
                              SppConnectConfig connectConfig, SppConnectCallback callback) {
        if (!isBluetoothEnabled()) {
            YoBTSDKLog.e(TAG, "无法扫描连接，蓝牙未启用");
            if (callback != null) {
                callback.onConnectFailed(null, "蓝牙未启用");
            }
            return;
        }
        
        this.userConnectCallback = callback;
        
        // 设置连接配置
        if (connectConfig != null) {
            connectManager.setConnectConfig(connectConfig);
        }
        
        // 扫描并连接第一个设备
        SppScanCallback scanCallback = new SppScanCallback() {
            private boolean deviceFound = false;
            
            @Override
            public void onDeviceFound(BluetoothDevice device, int rssi) {
                if (!deviceFound) {
                    deviceFound = true;
                    // 停止扫描
                    stopScan();
                    // 连接设备
                    connectManager.connect(device, callback);
                }
            }
            
            @Override
            public void onScanStart() {
                // 不需处理
            }
            
            @Override
            public void onScanFinish() {
                if (!deviceFound && callback != null) {
                    callback.onConnectFailed(null, "未找到匹配的设备");
                }
            }
        };
        
        // 开始扫描
        scanSppDevices(deviceNameFilter, deviceMacFilter, scanTimeoutMillis, classicBluetoothOnly, scanCallback);
    }
    
    /**
     * 断开SPP连接
     */
    public void disconnect() {
        connectManager.disconnect();
    }
    
    /**
     * 发送数据
     * @param data 要发送的数据
     * @return 是否发送成功
     */
    public boolean sendData(byte[] data) {
        BluetoothSocket socket = getConnectedSocket();
        return socket != null && SppUtils.sendData(socket, data);
    }
    
    /**
     * 获取已连接的Socket
     * @return 蓝牙Socket，未连接则返回null
     */
    public BluetoothSocket getConnectedSocket() {
        return connectManager.getConnectedSocket();
    }
    
    /**
     * 获取当前连接的设备
     * @return 蓝牙设备，未连接则返回null
     */
    public BluetoothDevice getConnectedDevice() {
        return connectManager.getConnectedDevice();
    }
    
    /**
     * 是否已连接
     * @return 是否已连接
     */
    public boolean isConnected() {
        return connectManager.isConnected();
    }
    
    /**
     * 检查当前经典蓝牙连接状态
     * 改进后的方法会自动处理Profile未就绪的情况
     * @return 连接结果，包含连接状态和MAC地址
     */
    public YoSPPMacDeviceChecker.DeviceConnectionResult checkSppConnectionStatus() {
        return deviceChecker.checkConnectedDevice();
    }
    
    /**
     * 获取当前连接的设备MAC地址
     * @return 当前连接设备的MAC地址，如果未连接则返回null
     */
    public String getConnectedDeviceMac() {
        YoSPPMacDeviceChecker.DeviceConnectionResult result = deviceChecker.checkConnectedDevice();
        return result.isConnected() ? result.getMacAddress() : null;
    }
    
    /**
     * 检查指定MAC地址的设备是否已通过经典蓝牙连接
     * @param macAddress 要检查的设备MAC地址
     * @return 如果设备已连接则返回true，否则返回false
     */
    public boolean isSppDeviceConnected(String macAddress) {
        if (macAddress == null || macAddress.isEmpty()) {
            return false;
        }
        return deviceChecker.isDeviceConnected(macAddress);
    }

    /**
     * 为SPP连接状态更改添加监听器
     * @param listener 要添加的监听器
     */
    public void addClassicBTConnectionStateListener(ClassicBTConnectionStateListener listener) {
        connectManager.addClassicBTConnectionStateListener(listener);
    }

    /**
     * 为SPP连接状态更改移除监听器
     * @param listener 要移除的监听器
     */
    public void removeClassicBTConnectionStateListener(ClassicBTConnectionStateListener listener) {
        connectManager.removeClassicBTConnectionStateListener(listener);
    }
    
    /**
     * 添加一个监听器以接收耳机断开连接事件的通知。
     * 此方法是线程安全的。
     *
     * @param listener 要添加的监听器。如果监听器为null，则此方法不执行任何操作。
     */
    public void addClassicBTDisconnectedListener(ClassicBTDisconnectedListener listener) {
        if (listener == null) {
            YoBTSDKLog.w(TAG, "尝试添加一个空的ClassicBTDisconnectedListener。");
            return;
        }

        // 创建一个包装器，将公共监听器适配到内部的ClassicBTConnectionStateListener
        ClassicBTConnectionStateListener wrapper = new ClassicBTConnectionStateListener() {
            @Override
            public void headsetIsConnected(String macAddress) {
                // 此监听器仅用于断开事件，因此此处不作处理。
            }

            @Override
            public void headsetIsDisconnected(String macAddress) {
                listener.headsetIsDisconnected(macAddress);
            }
        };
        
        // 仅当监听器是新的时，才存储映射并注册包装器
        if (disconnectListenerMap.putIfAbsent(listener, wrapper) == null) {
            connectManager.addClassicBTConnectionStateListener(wrapper);
            YoBTSDKLog.d(TAG, "ClassicBTDisconnectedListener 已添加。");
        }
    }

    /**
     * 移除一个先前添加的耳机断开连接监听器。
     * 此方法是线程安全的。
     *
     * @param listener 要移除的监听器。如果监听器为null，则此方法不执行任何操作。
     */
    public void removeClassicBTDisconnectedListener(ClassicBTDisconnectedListener listener) {
        if (listener == null) {
            YoBTSDKLog.w(TAG, "尝试移除一个空的ClassicBTDisconnectedListener。");
            return;
        }

        // 从映射中移除监听器及其包装器
        ClassicBTConnectionStateListener wrapper = disconnectListenerMap.remove(listener);
        
        // 如果监听器存在，则从连接管理器中注销其包装器
        if (wrapper != null) {
            connectManager.removeClassicBTConnectionStateListener(wrapper);
            YoBTSDKLog.d(TAG, "ClassicBTDisconnectedListener 已移除。");
        }
    }

    /**
     * 添加一个监听器以接收耳机连接事件的通知。
     * 此方法是线程安全的。
     *
     * @param listener 要添加的监听器。如果监听器为null，则此方法不执行任何操作。
     */
    public void addClassicBTConnectedListener(ClassicBTConnectedListener listener) {
        if (listener == null) {
            YoBTSDKLog.w(TAG, "尝试添加一个空的ClassicBTConnectedListener。");
            return;
        }

        ClassicBTConnectionStateListener wrapper = new ClassicBTConnectionStateListener() {
            @Override
            public void headsetIsConnected(String macAddress) {
                listener.headsetIsConnected(macAddress);
            }

            @Override
            public void headsetIsDisconnected(String macAddress) {
                // 此监听器仅用于连接事件，因此此处不作处理。
            }
        };

        if (connectListenerMap.putIfAbsent(listener, wrapper) == null) {
            connectManager.addClassicBTConnectionStateListener(wrapper);
            YoBTSDKLog.d(TAG, "ClassicBTConnectedListener 已添加。");
        }
    }

    /**
     * 移除一个先前添加的耳机连接监听器。
     * 此方法是线程安全的。
     *
     * @param listener 要移除的监听器。如果监听器为null，则此方法不执行任何操作。
     */
    public void removeClassicBTConnectedListener(ClassicBTConnectedListener listener) {
        if (listener == null) {
            YoBTSDKLog.w(TAG, "尝试移除一个空的ClassicBTConnectedListener。");
            return;
        }

        ClassicBTConnectionStateListener wrapper = connectListenerMap.remove(listener);
        if (wrapper != null) {
            connectManager.removeClassicBTConnectionStateListener(wrapper);
            YoBTSDKLog.d(TAG, "ClassicBTConnectedListener 已移除。");
        }
    }
    
    /**
     * 释放资源
     */
    public void release() {
        // 停止扫描
        stopScan();
        
        // 断开连接
        disconnect();
        
        // 释放扫描管理器
        scanManager.release();
        
        // 释放连接管理器
        connectManager.release();
        
        // 释放连接检测器
        deviceChecker.release();

        // 清空所有监听器
        disconnectListenerMap.clear();
        connectListenerMap.clear();
        
        // 清空回调
        userScanCallback = null;
        userConnectCallback = null;
        
        // 清除单例实例
        synchronized (YoSPPApi.class) {
            INSTANCE = null;
        }
        
        YoBTSDKLog.d(TAG, "YoSPPApi资源已释放");
    }
} 