package com.ggec.bleservice.setting.controlcmd;

import com.ggec.bleservice.setting.Command;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 耳机触控设置命令
 * 负责设置耳机的不同触控操作（左右耳、双击三击）对应的功能
 */
public class ControlCommand extends Command {
    private static final String TAG = "ControlCommand";
    
    // 命令前缀，用于确认是控制设置命令
    private static final String COMMAND_PREFIX = "99EC850002";
    
    // 命令基础格式
    private static final String COMMAND_FORMAT = "99EC850002%02d%02d";
    
    // 命令校验和后缀
    private static final String COMMAND_SUFFIX = "1234";
    
    // 耳机侧常量
    public static final int EAR_LEFT = 1;
    public static final int EAR_RIGHT = 2;
    
    // 触击类型常量
    public static final int TAP_TWICE = 1;
    public static final int TAP_TRIPLE = 2;
    
    // 功能类型常量
    public static final int FUNCTION_PLAY_PAUSE = 0;
    public static final int FUNCTION_VOLUME_UP = 1;
    public static final int FUNCTION_VOLUME_DOWN = 2;
    public static final int FUNCTION_PREV_TRACK = 3;
    public static final int FUNCTION_NEXT_TRACK = 4;
    public static final int FUNCTION_VOICE_ASSISTANT = 5;
    public static final int FUNCTION_EQ_SWITCH = 6;
    public static final int FUNCTION_GAME_MODE = 7;
    public static final int FUNCTION_NONE = 8;
    
    // 功能名称
    private static final String[] FUNCTION_NAMES = {
        "播放/暂停",
        "音量加",
        "音量减",
        "上一曲",
        "下一曲",
        "语音助手",
        "EQ切换",
        "游戏模式",
        "无"
    };
    
    // 耳机侧
    private final int earSide;
    
    // 触击类型
    private final int tapType;
    
    // 功能类型
    private final int functionType;
    
    /**
     * 构造方法
     * @param earSide 耳机侧（左耳、右耳）
     * @param tapType 触击类型（双击、三击）
     * @param functionType 功能类型
     */
    public ControlCommand(int earSide, int tapType, int functionType) {
        super();
        this.earSide = earSide;
        this.tapType = tapType;
        this.functionType = functionType;
        // 设置命令前缀
        setCommandPrefix("99EC85");
    }
    
    @Override
    public String getCommandData() {
        // 生成命令数据:
        // 左耳敲击2次：99 EC 85 00 02 00 0x 12 34
        // 右耳敲击2次：99 EC 85 00 02 01 0x 12 34
        // 左耳敲击3次：99 EC 85 00 02 02 0x 12 34
        // 右耳敲击3次：99 EC 85 00 02 03 0x 12 34
        int position = calculatePosition();
        return String.format(COMMAND_FORMAT, position, functionType) + COMMAND_SUFFIX;
    }
    
    /**
     * 计算设置位置编码
     * @return 位置编码
     */
    private int calculatePosition() {
        // 根据耳机侧和触击类型计算位置编码
        if (earSide == EAR_LEFT && tapType == TAP_TWICE) {
            return 0; // 左耳双击
        } else if (earSide == EAR_RIGHT && tapType == TAP_TWICE) {
            return 1; // 右耳双击
        } else if (earSide == EAR_LEFT && tapType == TAP_TRIPLE) {
            return 2; // 左耳三击
        } else if (earSide == EAR_RIGHT && tapType == TAP_TRIPLE) {
            return 3; // 右耳三击
        }
        return 0; // 默认为左耳双击
    }
    
    @Override
    public String parseResponse(String responseData) {
        YoBTSDKLog.d(TAG, "解析响应数据: " + responseData);
        
        if (responseData == null || responseData.length() < 14) {
            return "响应数据格式错误";
        }
        
        // 构建期望的响应前缀
        int position = calculatePosition();
        String expectedPrefix = String.format(COMMAND_FORMAT, position, functionType).substring(0, 13);
        
        // 获取实际响应前缀
        String responsePrefix = responseData.substring(0, Math.min(13, responseData.length()));
        
        // 判断前缀是否匹配
        boolean isSuccess = responsePrefix.equals(expectedPrefix);
                
        // 获取功能名称
        String functionName = (functionType >= 0 && functionType < FUNCTION_NAMES.length) 
                ? FUNCTION_NAMES[functionType] : "未知功能";
        
        String earName = (earSide == EAR_LEFT) ? "左耳" : "右耳";
        String tapName = (tapType == TAP_TWICE) ? "双击" : "三击";
        
        String result = isSuccess ?
                earName + tapName + "设置为\"" + functionName + "\"成功" :
                earName + tapName + "设置为\"" + functionName + "\"失败";
        
        // 计算 resultValue，格式为 status(两位) + function(两位)
        // 例如：0103 表示成功设置为上一曲功能
        int resultValue;
        if (isSuccess) {
            // 将状态(1)和功能合并为一个数字，例如：成功(01) + 上一曲(03) = 103
            resultValue = (1 * 100) + functionType;
        } else {
            resultValue = 0; // 失败状态
        }
        
        // 通知命令完成（使用带结果值的回调）
        notifyCompletion(isSuccess ? ResultCode.SUCCESS : ResultCode.FAILED, resultValue, result);
        
        return result;
    }
    
    @Override
    public boolean isResponseMatch(String responseData) {
        // 检查响应前缀是否匹配
        if (responseData != null && responseData.length() >= 10) {
            String prefix = responseData.substring(0, 10);
            return COMMAND_PREFIX.equals(prefix);
        }
        return false;
    }
} 