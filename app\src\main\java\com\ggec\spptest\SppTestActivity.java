//package com.ggec.spptest;
//
//import android.Manifest;
//import android.bluetooth.BluetoothDevice;
//import android.bluetooth.BluetoothSocket;
//import android.content.pm.PackageManager;
//import android.os.Build;
//import android.os.Bundle;
//import android.os.Handler;
//import android.os.Looper;
//import android.util.Log;
//import android.view.View;
//import android.widget.Button;
//import android.widget.LinearLayout;
//import android.widget.ProgressBar;
//import android.widget.TextView;
//import android.widget.Toast;
//
//import androidx.annotation.NonNull;
//import androidx.appcompat.app.AppCompatActivity;
//import androidx.core.app.ActivityCompat;
//import androidx.core.content.ContextCompat;
//import androidx.recyclerview.widget.LinearLayoutManager;
//import androidx.recyclerview.widget.RecyclerView;
//
//import com.ggec.hs01.R;
//import com.ggec.sppservice.YoSPPApi;
//import com.ggec.sppservice.connect.SppConnectAdapter;
//import com.ggec.sppservice.connect.SppConnectCallback;
//import com.ggec.sppservice.connect.SppConnectConfig;
//import com.ggec.sppservice.scanner.SppScanCallback;
//import com.ggec.spptest.adapter.SppDeviceAdapter;
//
//import java.util.ArrayList;
//import java.util.List;
//
//public class SppTestActivity extends AppCompatActivity {
//    private static final String TAG = "SppTestActivity";
//    private static final int REQUEST_BLUETOOTH_PERMISSIONS = 1001;
//    private static final int SCAN_TIMEOUT = 12000; // 扫描超时时间，修改为12秒
//
//    // UI组件
//    private TextView tvScanStatus;
//    private TextView tvNoDevices;
//    private ProgressBar progressScanning;
//    private RecyclerView rvSppDevices;
//
//    // 连接相关UI组件
//    private LinearLayout layoutConnectionStatus;
//    private TextView tvConnectionStatus;
//    private TextView tvConnectedDevice;
//    private ProgressBar progressConnecting;
//    private Button btnDisconnect;
//    private Button btnSendTest;
//
//    // SPP服务
//    private YoSPPApi sppApi;
//
//    // 设备适配器
//    private SppDeviceAdapter deviceAdapter;
//
//    // 是否正在扫描
//    private boolean isScanning = false;
//
//    // 当前连接的设备
//    private BluetoothDevice connectedDevice = null;
//    private boolean isConnected = false;
//    private boolean isConnecting = false;
//
//    // Handler for UI updates
//    private final Handler uiHandler = new Handler(Looper.getMainLooper());
//
//    // SPP扫描回调
//    private final SppScanCallback sppScanCallback = new SppScanCallback() {
//        @Override
//        public void onDeviceFound(BluetoothDevice device, int rssi) {
//            runOnUiThread(() -> {
//                Log.d(TAG, "发现设备: " + device.getName() + " - " + device.getAddress() + " RSSI: " + rssi);
//                deviceAdapter.addDevice(device, rssi);
//
//                if (tvNoDevices.getVisibility() == View.VISIBLE) {
//                    tvNoDevices.setVisibility(View.GONE);
//                }
//            });
//        }
//
//        @Override
//        public void onScanStart() {
//            runOnUiThread(() -> {
//                updateScanStatus("开始扫描...");
//                progressScanning.setVisibility(View.VISIBLE);
//                isScanning = true;
//            });
//        }
//
//        @Override
//        public void onScanFinish() {
//            runOnUiThread(() -> {
//                updateScanStatus("扫描完成");
//                progressScanning.setVisibility(View.GONE);
//                isScanning = false;
//
//                if (deviceAdapter.getItemCount() == 0) {
//                    tvNoDevices.setVisibility(View.VISIBLE);
//                }
//            });
//        }
//    };
//
//    // SPP连接回调
//    private final SppConnectCallback sppConnectCallback = new SppConnectCallback() {
//        @Override
//        public void onConnectStart(BluetoothDevice device) {
//            runOnUiThread(() -> {
//                isConnecting = true;
//                updateConnectionStatus("正在连接...");
//                showConnectionUI(device, false);
//                progressConnecting.setVisibility(View.VISIBLE);
//            });
//        }
//
//        @Override
//        public void onPairingRequest(BluetoothDevice device) {
//            runOnUiThread(() -> {
//                Toast.makeText(SppTestActivity.this, "请确认配对请求", Toast.LENGTH_LONG).show();
//                updateConnectionStatus("请确认配对请求...");
//            });
//        }
//
//        @Override
//        public void onPaired(BluetoothDevice device) {
//            runOnUiThread(() -> {
//                Toast.makeText(SppTestActivity.this, "设备已配对", Toast.LENGTH_SHORT).show();
//                updateConnectionStatus("设备已配对，正在连接...");
//            });
//        }
//
//        @Override
//        public void onConnected(BluetoothDevice device, BluetoothSocket socket) {
//            runOnUiThread(() -> {
//                isConnected = true;
//                isConnecting = false;
//                connectedDevice = device;
//                updateConnectionStatus("已连接");
//                showConnectionUI(device, true);
//                progressConnecting.setVisibility(View.GONE);
//
//                // 启用相关按钮
//                btnDisconnect.setEnabled(true);
//                btnSendTest.setEnabled(true);
//
//                Toast.makeText(SppTestActivity.this, "连接成功: " + device.getName(), Toast.LENGTH_SHORT).show();
//                Log.d(TAG, "已连接到设备: " + device.getName());
//            });
//        }
//
//        @Override
//        public void onConnectFailed(BluetoothDevice device, String errorMsg) {
//            runOnUiThread(() -> {
//                isConnecting = false;
//                updateConnectionStatus("连接失败: " + errorMsg);
//                progressConnecting.setVisibility(View.GONE);
//
//                // 设备信息仍然显示，但按钮禁用
//                btnDisconnect.setEnabled(false);
//                btnSendTest.setEnabled(false);
//
//                Toast.makeText(SppTestActivity.this, "连接失败: " + errorMsg, Toast.LENGTH_SHORT).show();
//                Log.d(TAG, "连接失败: " + errorMsg);
//            });
//        }
//
//        @Override
//        public void onDisconnected(BluetoothDevice device) {
//            runOnUiThread(() -> {
//                isConnected = false;
//                isConnecting = false;
//                connectedDevice = null;
//
//                if (layoutConnectionStatus.getVisibility() == View.VISIBLE) {
//                    updateConnectionStatus("已断开连接");
//                    btnDisconnect.setEnabled(false);
//                    btnSendTest.setEnabled(false);
//
//                    // 2秒后隐藏连接状态UI
//                    uiHandler.postDelayed(() -> {
//                        if (!isConnected && !isConnecting) {
//                            layoutConnectionStatus.setVisibility(View.GONE);
//                        }
//                    }, 2000);
//                }
//
//                Toast.makeText(SppTestActivity.this, "已断开连接", Toast.LENGTH_SHORT).show();
//                Log.d(TAG, "已断开连接");
//            });
//        }
//    };
//
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.activity_spp_test);
//
//        // 初始化SPP API
//        sppApi = YoSPPApi.getInstance(this);
//
//        // 初始化视图
//        initViews();
//
//        // 检查权限
//        checkAndRequestPermissions();
//    }
//
//    private void initViews() {
//        // 获取UI组件
//        tvScanStatus = findViewById(R.id.tv_scan_status);
//        tvNoDevices = findViewById(R.id.tv_no_devices);
//        progressScanning = findViewById(R.id.progress_scanning);
//        rvSppDevices = findViewById(R.id.rv_spp_devices);
//
//        // 连接相关UI组件
//        layoutConnectionStatus = findViewById(R.id.layout_connection_status);
//        tvConnectionStatus = findViewById(R.id.tv_connection_status);
//        tvConnectedDevice = findViewById(R.id.tv_connected_device);
//        progressConnecting = findViewById(R.id.progress_connecting);
//        btnDisconnect = findViewById(R.id.btn_disconnect);
//        btnSendTest = findViewById(R.id.btn_send_test);
//
//        // 初始化RecyclerView
//        rvSppDevices.setLayoutManager(new LinearLayoutManager(this));
//        deviceAdapter = new SppDeviceAdapter();
//        rvSppDevices.setAdapter(deviceAdapter);
//
//        // 设置设备点击事件
//        deviceAdapter.setOnDeviceClickListener(device -> {
//            Toast.makeText(this, "正在连接: " + device.getName(), Toast.LENGTH_SHORT).show();
//            connectToDevice(device);
//        });
//
//        // 设置扫描按钮点击事件
//        findViewById(R.id.btn_scan_devices).setOnClickListener(v -> startScanDevices());
//
//        // 设置刷新按钮点击事件
//        findViewById(R.id.btn_refresh).setOnClickListener(v -> {
//            if (isScanning) {
//                stopScan();
//            }
//            startScanDevices();
//        });
//
//        // 设置断开连接按钮点击事件
//        btnDisconnect.setOnClickListener(v -> disconnectDevice());
//
//        // 设置发送测试数据按钮点击事件
//        btnSendTest.setOnClickListener(v -> sendTestData());
//
//        // 设置返回按钮点击事件
//        findViewById(R.id.btn_back).setOnClickListener(v -> finish());
//    }
//
//    private void updateScanStatus(String status) {
//        if (tvScanStatus != null) {
//            tvScanStatus.setText(status);
//        }
//    }
//
//    private void updateConnectionStatus(String status) {
//        if (tvConnectionStatus != null) {
//            tvConnectionStatus.setText("连接状态: " + status);
//        }
//    }
//
//    private void showConnectionUI(BluetoothDevice device, boolean connected) {
//        if (layoutConnectionStatus != null && tvConnectedDevice != null) {
//            layoutConnectionStatus.setVisibility(View.VISIBLE);
//            String deviceInfo = device.getName() != null ? device.getName() : "未知设备";
//            deviceInfo += " (" + device.getAddress() + ")";
//            tvConnectedDevice.setText("设备: " + deviceInfo);
//
//            // 设置背景色
//            layoutConnectionStatus.setBackgroundColor(
//                    connected ?
//                    ContextCompat.getColor(this, android.R.color.holo_green_light) :
//                    ContextCompat.getColor(this, android.R.color.holo_blue_light)
//            );
//        }
//    }
//
//    private void checkAndRequestPermissions() {
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
//            String[] permissions = {
//                    Manifest.permission.BLUETOOTH_SCAN,
//                    Manifest.permission.BLUETOOTH_CONNECT,
//                    Manifest.permission.ACCESS_FINE_LOCATION
//            };
//
//            List<String> permissionsToRequest = new ArrayList<>();
//            for (String permission : permissions) {
//                if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
//                    permissionsToRequest.add(permission);
//                }
//            }
//
//            if (!permissionsToRequest.isEmpty()) {
//                ActivityCompat.requestPermissions(this,
//                        permissionsToRequest.toArray(new String[0]),
//                        REQUEST_BLUETOOTH_PERMISSIONS);
//            } else {
//                // 已有权限，开始扫描
//                startScanDevices();
//            }
//        } else {
//            String[] permissions = {
//                    Manifest.permission.ACCESS_FINE_LOCATION,
//                    Manifest.permission.BLUETOOTH,
//                    Manifest.permission.BLUETOOTH_ADMIN
//            };
//
//            List<String> permissionsToRequest = new ArrayList<>();
//            for (String permission : permissions) {
//                if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
//                    permissionsToRequest.add(permission);
//                }
//            }
//
//            if (!permissionsToRequest.isEmpty()) {
//                ActivityCompat.requestPermissions(this,
//                        permissionsToRequest.toArray(new String[0]),
//                        REQUEST_BLUETOOTH_PERMISSIONS);
//            } else {
//                // 已有权限，开始扫描
//                startScanDevices();
//            }
//        }
//    }
//
//    @Override
//    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        if (requestCode == REQUEST_BLUETOOTH_PERMISSIONS) {
//            boolean allGranted = true;
//            for (int result : grantResults) {
//                if (result != PackageManager.PERMISSION_GRANTED) {
//                    allGranted = false;
//                    break;
//                }
//            }
//
//            if (allGranted) {
//                // 权限已授予，开始扫描
//                startScanDevices();
//            } else {
//                Toast.makeText(this, "需要蓝牙和位置权限才能正常使用SPP功能", Toast.LENGTH_LONG).show();
//                updateScanStatus("蓝牙权限被拒绝");
//            }
//        }
//    }
//
//    private void startScanDevices() {
//        if (!sppApi.isBluetoothEnabled()) {
//            updateScanStatus("蓝牙未启用，请先开启蓝牙");
//            tvNoDevices.setText("请开启蓝牙后重试");
//            tvNoDevices.setVisibility(View.VISIBLE);
//            return;
//        }
//
//        // 清空设备列表
//        deviceAdapter.clearDevices();
//        tvNoDevices.setVisibility(View.GONE);
//
//        // 停止之前的扫描
//        if (isScanning) {
//            stopScan();
//        }
//
//        // 更新UI状态
//        updateScanStatus("正在扫描设备...");
//        progressScanning.setVisibility(View.VISIBLE);
//
//        // 开始扫描，只显示经典蓝牙设备（过滤BLE设备）
//        sppApi.scanSppDevices(null, SCAN_TIMEOUT, true, sppScanCallback);
//    }
//
//    private void stopScan() {
//        if (isScanning) {
//            sppApi.stopScan();
//            isScanning = false;
//            updateScanStatus("已停止扫描");
//            progressScanning.setVisibility(View.GONE);
//        }
//    }
//
//    private void connectToDevice(BluetoothDevice device) {
//        // 停止扫描
//        stopScan();
//
//        // 如果已经连接了设备，先断开
//        if (isConnected || isConnecting) {
//            sppApi.disconnect();
//        }
//
//        // 创建连接配置
//        SppConnectConfig connectConfig = SppConnectConfig.newBuilder()
//                .autoPair(true)  // 自动配对
//                .retryConnect(true)  // 自动重试
//                .retryCount(2)  // 重试2次
//                .build();
//
//        // 连接设备
//        sppApi.connectDevice(device, connectConfig, sppConnectCallback);
//    }
//
//    private void disconnectDevice() {
//        if (isConnected || isConnecting) {
//            sppApi.disconnect();
//        }
//    }
//
//    private void sendTestData() {
//        if (isConnected) {
//            // 发送测试数据
//            byte[] testData = "Hello SPP Device!".getBytes();
//            boolean success = sppApi.sendData(testData);
//
//            if (success) {
//                Toast.makeText(this, "发送测试数据成功", Toast.LENGTH_SHORT).show();
//                Log.d(TAG, "发送测试数据成功");
//            } else {
//                Toast.makeText(this, "发送测试数据失败", Toast.LENGTH_SHORT).show();
//                Log.d(TAG, "发送测试数据失败");
//            }
//        }
//    }
//
//    @Override
//    protected void onDestroy() {
//        super.onDestroy();
//        // 停止扫描
//        stopScan();
//        // 断开连接
//        disconnectDevice();
//        // 释放资源
//        if (sppApi != null) {
//            sppApi.release();
//        }
//    }
//}