<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/control_test_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/comp_background_gray"
    android:fitsSystemWindows="false"
    tools:context=".activity.ControlTestActivity">

    <!-- 顶部导航栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/nav_bar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/comp_background_gray"
        android:elevation="0dp"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="4dp"
            android:padding="12dp"
            android:src="@drawable/ic_public_back"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="控制测试"
            android:textColor="@color/font_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 空白内容区域 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@+id/nav_bar"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 页面内容可以添加在这里 -->
            <com.ggec.hs01.view.HMButton
                android:id="@+id/btn_left_triple_eq"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设置左耳三击为EQ切换"
                android:layout_marginBottom="16dp"/>

            <com.ggec.hs01.view.HMButton
                android:id="@+id/btn_left_triple_prev"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设置左耳三击为上一曲"
                android:layout_marginBottom="16dp"/>

            <com.ggec.hs01.view.HMButton
                android:id="@+id/btn_right_triple_assist"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设置右耳三击为语音助手"
                android:layout_marginBottom="16dp"/>

            <com.ggec.hs01.view.HMButton
                android:id="@+id/btn_right_triple_play"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设置右耳三击为播放+暂停"
                android:layout_marginBottom="16dp"/>

            <!-- 右耳双击 -->
            <com.ggec.hs01.view.HMButton
                android:id="@+id/btn_right_double_next"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设置右耳双击为下一曲"
                android:layout_marginBottom="16dp"/>

            <com.ggec.hs01.view.HMButton
                android:id="@+id/btn_right_double_assist"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设置右耳双击为语音助手"
                android:layout_marginBottom="16dp"/>

            <com.ggec.hs01.view.HMButton
                android:id="@+id/btn_right_double_play"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设置右耳双击为播放+暂停"
                android:layout_marginBottom="16dp"/>

            <!-- 左耳双击 -->
            <com.ggec.hs01.view.HMButton
                android:id="@+id/btn_left_double_prev"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设置左耳双击为上一曲"
                android:layout_marginBottom="16dp"/>

            <com.ggec.hs01.view.HMButton
                android:id="@+id/btn_left_double_assist"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设置左耳双击为语音助手"
                android:layout_marginBottom="16dp"/>

            <com.ggec.hs01.view.HMButton
                android:id="@+id/btn_left_double_eq"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设置左耳双击为EQ切换"
                android:layout_marginBottom="16dp"/>

            <com.ggec.hs01.view.HMButton
                android:id="@+id/btn_left_double_none"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设置左耳双击为无"
                android:layout_marginBottom="16dp"/>

        </LinearLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout> 