package com.ggec.hs01.utils;

import android.content.Context;

import com.yovo.yotheme.ThemeManager;
import com.yovo.yotheme.YoButton;
import com.yovo.yotheme.YoSwitch;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 主题帮助类 - 用于高内聚低耦合地访问yovo模块中的主题功能
 * 作为app模块与yovo模块的桥接器
 */
public class ThemeHelper {
    private static volatile ThemeHelper instance;
    private final ThemeManager themeManager;
    
    private ThemeHelper() {
        // 获取yovo模块中的ThemeManager实例
        themeManager = ThemeManager.getDefault();
    }
    
    /**
     * 获取ThemeHelper单例实例
     * @return ThemeHelper实例
     */
    public static ThemeHelper getInstance() {
        if (instance == null) {
            synchronized (ThemeHelper.class) {
                if (instance == null) {
                    instance = new ThemeHelper();
                }
            }
        }
        return instance;
    }
    
    /**
     * 获取主题化的开关组件
     * @param context 上下文
     * @return YoSwitch实例
     */
    public YoSwitch getSwitch(Context context) {
        YoSwitch yoSwitch = themeManager.getSwitch(context);
        // 设置统一的开关尺寸为50dp x 25dp (与MusicSettingsActivity中保持一致)
        yoSwitch.setMinimumWidth((int)(50 * context.getResources().getDisplayMetrics().density));
        yoSwitch.setMinimumHeight((int)(25 * context.getResources().getDisplayMetrics().density));
        return yoSwitch;
    }
    
    /**
     * 获取主题化的按钮组件
     * @param context 上下文
     * @return YoButton实例
     */
    public YoButton getButton(Context context) {
        return themeManager.getButton(context);
    }
    
    /**
     * 设置全局主题色
     * @param primaryColor 主色调
     * @param secondaryColor 次级色调
     */
    public void setThemeColors(int primaryColor, int secondaryColor) {
        themeManager.setThemeColors(primaryColor, secondaryColor);
    }
} 