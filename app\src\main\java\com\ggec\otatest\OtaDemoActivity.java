// package com.ggec.hs01.otatest;

// import android.Manifest;
// import android.content.pm.PackageManager;
// import android.os.Build;
// import android.os.Bundle;
// import android.util.Log;
// import android.widget.Button;
// import android.widget.ProgressBar;
// import android.widget.TextView;
// import android.widget.Toast;

// import androidx.annotation.NonNull;
// import androidx.annotation.Nullable;
// import androidx.appcompat.app.AppCompatActivity;
// import androidx.core.app.ActivityCompat;
// import androidx.core.content.ContextCompat;

// import com.bes.sdk.device.HmDevice;
// import com.bes.sdk.utils.OTAStatus;
// import com.ggec.hs01.R;
// import com.ggec.yotasdk.YOTAApi;
// import com.ggec.yotasdk.YOTAManager;

// import java.util.ArrayList;
// import java.util.List;

// /**
//  * OTA升级演示Activity
//  */
// public class OtaDemoActivity extends AppCompatActivity implements YOTAApi.ProgressListener, YOTAApi.StatusListener {

//     private static final String TAG = "OtaDemoActivity";
//     private static final int REQUEST_PERMISSIONS_CODE = 101;
    
//     private TextView tvOtaProgress;
//     private ProgressBar progressBar;
//     private Button btnStartOta;
    
//     private YOTAApi yotaApi;
//     private HmDevice currentDevice;
    
//     @Override
//     protected void onCreate(@Nullable Bundle savedInstanceState) {
//         super.onCreate(savedInstanceState);
//         setContentView(R.layout.activity_ota_demo);
        
//         Log.i(TAG, "OtaDemoActivity创建");
        
//         // 初始化视图
//         initViews();
        
//         // 初始化YOTAApi
//         yotaApi = YOTAManager.getInstance(this);
//         yotaApi.setProgressListener(this);
//         yotaApi.setStatusListener(this);
        
//         // 获取可用设备
//         getCurrentDevice();
//     }
    
//     private void initViews() {
//         tvOtaProgress = findViewById(R.id.tv_ota_progress);
//         progressBar = findViewById(R.id.progress_bar);
//         btnStartOta = findViewById(R.id.btn_start_ota);
        
//         btnStartOta.setEnabled(true);
//         btnStartOta.setOnClickListener(v -> checkAndRequestPermissions());
//     }
    
//     /**
//      * 检查并请求权限
//      */
//     private void checkAndRequestPermissions() {
//         if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
//             List<String> permissionsToRequest = new ArrayList<>();
//             String[] requiredPermissions = new String[]{
//                     Manifest.permission.READ_EXTERNAL_STORAGE,
//                     Manifest.permission.READ_PHONE_STATE
//             };

//             for (String permission : requiredPermissions) {
//                 if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
//                     permissionsToRequest.add(permission);
//                 }
//             }

//             if (!permissionsToRequest.isEmpty()) {
//                 ActivityCompat.requestPermissions(this,
//                         permissionsToRequest.toArray(new String[0]),
//                         REQUEST_PERMISSIONS_CODE);
//             } else {
//                 // 所有权限都已被授予
//                 startOtaUpgrade();
//             }
//         } else {
//             // Android 6.0 以下系统，权限在安装时已授予
//             startOtaUpgrade();
//         }
//     }


//     @Override
//     public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
//         super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//         if (requestCode == REQUEST_PERMISSIONS_CODE) {
//             boolean allPermissionsGranted = true;
//             for (int grantResult : grantResults) {
//                 if (grantResult != PackageManager.PERMISSION_GRANTED) {
//                     allPermissionsGranted = false;
//                     break;
//                 }
//             }

//             if (allPermissionsGranted) {
//                 // 所有权限都被授予
//                 Toast.makeText(this, "所有必要权限已获取", Toast.LENGTH_SHORT).show();
//                 startOtaUpgrade();
//             } else {
//                 // 至少有一个权限被拒绝
//                 Toast.makeText(this, "需要所有权限才能进行OTA升级", Toast.LENGTH_SHORT).show();
//             }
//         }
//     }
    
//     /**
//      * 获取当前连接的设备
//      */
//     private void getCurrentDevice() {
//         Log.i(TAG, "获取可用设备");
        
//         // 使用YOTAManager获取已连接的SPP设备
//         ArrayList<HmDevice> connectedDevices = ((YOTAManager) yotaApi).getConnectedSppDevices();
        
//         if (!connectedDevices.isEmpty()) {
//             currentDevice = connectedDevices.get(0);
//             String deviceName = currentDevice.getDeviceName();
//             String deviceMac = currentDevice.getDeviceMAC();
//             Log.i(TAG, "发现已连接设备: " + deviceName + " (" + deviceMac + ")");
//         } else {
//             Log.i(TAG, "没有检测到已连接的SPP设备");
//             currentDevice = null;
//         }
//     }
    
//     /**
//      * 开始OTA升级
//      */
//     private void startOtaUpgrade() {
//         Log.i(TAG, "尝试开始OTA升级");
        
//         getCurrentDevice();
//         resetProgressDisplay();
        
//         if (currentDevice != null) {
//             boolean result = yotaApi.startUpgrade(currentDevice, null);
//             if (result) {
//                 btnStartOta.setEnabled(false);
//                 Toast.makeText(this, "开始OTA升级", Toast.LENGTH_SHORT).show();
//                 Log.i(TAG, "OTA升级已开始");
//             } else {
//                 Toast.makeText(this, "启动OTA升级失败，请检查设备状态", Toast.LENGTH_SHORT).show();
//                 Log.e(TAG, "OTA升级启动失败");
//             }
//         } else {
//             Toast.makeText(this, "未连接设备，无法开始OTA升级", Toast.LENGTH_SHORT).show();
//             Log.e(TAG, "未连接设备，无法开始OTA升级");
//         }
//     }
    
//     private void resetProgressDisplay() {
//         progressBar.setProgress(0);
//         tvOtaProgress.setText("0%");
//     }
    
//     @Override
//     protected void onDestroy() {
//         super.onDestroy();
//         Log.i(TAG, "OtaDemoActivity销毁");
//         if (yotaApi != null) {
//             yotaApi.cancelUpgrade();
//         }
//     }
    
//     // YOTAApi.ProgressListener 实现
//     @Override
//     public void onProgressChanged(float progress) {
//         // 不再乘以100，假设progress已经是百分比值
//         int progressInt = (int) progress;
//         Log.i(TAG, "OTA升级进度: " + progressInt + "%");
        
//         runOnUiThread(() -> {
//             progressBar.setProgress(progressInt);
//             tvOtaProgress.setText(progressInt + "%");
//         });
//     }
    
//     // YOTAApi.StatusListener 实现
//     @Override
//     public void onStatusChanged(OTAStatus status) {
//         Log.i(TAG, "OTA状态变更: " + status.getName());
        
//         runOnUiThread(() -> {
//             switch (status) {
//                 case STATUS_STARTED:
//                     Toast.makeText(OtaDemoActivity.this, "OTA升级已开始", Toast.LENGTH_SHORT).show();
//                     break;
//                 case STATUS_UPDATING:
//                     Toast.makeText(OtaDemoActivity.this, "OTA升级中...", Toast.LENGTH_SHORT).show();
//                     break;
//                 case STATUS_SUCCEED:
//                     Toast.makeText(OtaDemoActivity.this, "OTA升级成功", Toast.LENGTH_SHORT).show();
//                     btnStartOta.setEnabled(true);
//                     break;
//                 case STATUS_FAILED:
//                     Toast.makeText(OtaDemoActivity.this, "OTA升级失败", Toast.LENGTH_SHORT).show();
//                     btnStartOta.setEnabled(true);
//                     break;
//                 case STATUS_CANCELED:
//                     Toast.makeText(OtaDemoActivity.this, "OTA升级取消", Toast.LENGTH_SHORT).show();
//                     btnStartOta.setEnabled(true);
//                     break;
//                 default:
//                     Log.i(TAG, "其他OTA状态: " + status.getName());
//                     break;
//             }
//         });
//     }
    
//     @Override
//     public void onError(int errorCode, String message) {
//         Log.e(TAG, "OTA错误: " + errorCode + ", " + message);
        
//         runOnUiThread(() -> {
//             Toast.makeText(OtaDemoActivity.this, "OTA错误: " + message, Toast.LENGTH_SHORT).show();
//             btnStartOta.setEnabled(true);
//         });
//     }
    
//     @Override
//     public void onSuccess() {
//         Log.i(TAG, "OTA成功完成");
        
//         runOnUiThread(() -> {
//             Toast.makeText(OtaDemoActivity.this, "OTA升级完成", Toast.LENGTH_SHORT).show();
//             btnStartOta.setEnabled(true);
//         });
//     }
// } 