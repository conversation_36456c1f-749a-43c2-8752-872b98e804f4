package com.ggec.bleservice.setting.cmdmanager;

import com.ggec.bleservice.YoCommandApi;
import com.ggec.bleservice.setting.Command;
import com.ggec.bleservice.setting.CommandQueueCenter;
import com.ggec.bleservice.setting.batterycmd.CaseChargingStatusCommand;
import com.ggec.bleservice.setting.batterycmd.EarInCaseStatusCommand;
import com.ggec.bleservice.setting.musiccmd.EQModeCommand;
import com.ggec.bleservice.setting.musiccmd.SoundQualityModeCommand;
import com.ggec.bleservice.setting.musiccmd.VolumeAdaptiveCommand;
import com.ggec.bleservice.setting.musiccmd.VolumeRemindCommand;
import com.ggec.bleservice.setting.musiccmd.UsageRemindCommand;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-16
 * Description: 
 * 音乐设置管理器
 * 负责管理所有音乐和音效相关的设置，包括EQ模式、音质模式、音量提醒等
 */
public class MusicSettingsManager extends BaseCommandManager {
    private static final String TAG = "MusicSettingsManager";
    
    // 单例实例
    private static MusicSettingsManager instance;
    
    /**
     * 获取单例实例
     */
    public static synchronized MusicSettingsManager getInstance() {
        if (instance == null) {
            instance = new MusicSettingsManager();
        }
        return instance;
    }
    
    /**
     * 私有构造方法
     */
    private MusicSettingsManager() {
        init(); // 调用父类初始化方法
    }
    
    /**
     * 释放资源
     */
    @Override
    public void release() {
        super.release();
        instance = null;
    }
    
    /**
     * 设置音量自适应开关
     * @param enabled 是否启用
     */
    public void setVolumeAdaptive(boolean enabled) {
        YoBTSDKLog.i(TAG, "设置音量自适应: " + (enabled ? "开启" : "关闭"));
        
        // 创建音量自适应命令
        VolumeAdaptiveCommand command = new VolumeAdaptiveCommand(enabled);
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 设置音质模式
     * @param isHighQuality 是否为高音质模式，true表示高音质，false表示高续航
     */
    public void setQualityMode(boolean isHighQuality) {
        YoBTSDKLog.i(TAG, "设置音质模式: " + (isHighQuality ? "高音质" : "高续航"));
        
        // 创建音质模式命令
        SoundQualityModeCommand command = new SoundQualityModeCommand(isHighQuality);
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 设置EQ模式
     * @param mode EQ模式，可使用EQModeCommand中定义的常量
     */
    public void setEQMode(int mode) {
        String modeName;
        switch (mode) {
            case EQModeCommand.MUSIC_MODE:
                modeName = "音乐模式";
                break;
            case EQModeCommand.MOVIE_MODE:
                modeName = "电影模式";
                break;
            case EQModeCommand.NEWS_MODE:
                modeName = "新闻模式";
                break;
            case EQModeCommand.ELDERLY_MODE:
                modeName = "老年人模式";
                break;
            default:
                modeName = "未知模式";
                break;
        }
        
        YoBTSDKLog.i(TAG, "设置EQ模式: " + modeName);
        
        // 创建EQ模式命令
        EQModeCommand command = new EQModeCommand(mode);
        
        // 执行命令
        createAndEnqueueCommand(command);
    }

    /**
     * 设置音量提醒开关
     * @param enabled 是否启用
     */
    public void setVolumeRemind(boolean enabled) {
        YoBTSDKLog.i(TAG, "设置音量提醒: " + (enabled ? "开启" : "关闭"));
        
        // 创建音量提醒命令
        VolumeRemindCommand command = new VolumeRemindCommand(enabled);
        
        // 执行命令
        createAndEnqueueCommand(command);
    }
    
    /**
     * 设置使用时长提醒开关
     * @param enabled 是否启用
     */
    public void setUsageRemind(boolean enabled) {
        YoBTSDKLog.i(TAG, "设置使用时长提醒: " + (enabled ? "开启" : "关闭"));
        
        // 创建使用时长提醒命令
        UsageRemindCommand command = new UsageRemindCommand(enabled);
        
        // 执行命令
        createAndEnqueueCommand(command);
    }

    private YoCommandApi.EQModeCallback eqModeCallback;

    /**
     * 监听EQ模式变化
     *  @param callback EQ模式变化回调
     **/
    public void listenEQMode(YoCommandApi.EQModeCallback callback) {
        this.eqModeCallback = callback;
    }

    @Override
    protected String getManagerTag() {
        return TAG;
    }

    @Override
    protected void handleUnsolicitedResponse(String data) {
        // 检查是否有命令正在执行，如果有则让命令优先处理数据
        if (CommandQueueCenter.getInstance().isExecuting()) {
            YoBTSDKLog.d(TAG, "有命令正在执行，暂不处理主动上报数据: " + data);
            return;
        }

        YoBTSDKLog.d(TAG, "处理主动上报数据: " + data);
        if (data == null || data.length() < 12) {
            return;
        }
        String prefix = data.substring(0, 6);

        try {
            if (prefix.equals("99EC83")) { // EQ模式
                // 获取命令状态字节
                String statusByteHex = data.substring(10, 12);
                try {
                    // 将十六进制状态转换为整数
                    int mode = Integer.parseInt(statusByteHex, 16);
                    // 通知命令完成
                    if(eqModeCallback != null) {
                        eqModeCallback.listenEQMode(mode);
                    }
                } catch (NumberFormatException e) {
                    YoBTSDKLog.e(TAG, "解析EQ模式状态失败: " + statusByteHex, e);
                }
            }
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "解析电池主动上报数据失败: " + data, e);
        }
    }

    @Override
    protected boolean isDataRelevant(String data) {
        // 检查数据前缀是否与音乐设置相关
        if (data != null && data.length() >= 6) {
            String prefix = data.substring(0, 6); // 取前6个字符 (99ECxx)
            
            // 高音质模式 99EC81
            // 音量自适应 99EC82
            // EQ模式 99EC83
            // 音量提醒/使用时长提醒 99EC84
            return "99EC81".equals(prefix) || "99EC82".equals(prefix) || 
                   "99EC83".equals(prefix) || "99EC84".equals(prefix);
        }
        return false;
    }
} 