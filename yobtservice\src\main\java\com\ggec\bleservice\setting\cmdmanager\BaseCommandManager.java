package com.ggec.bleservice.setting.cmdmanager;

import com.ggec.bleservice.setting.Command;
import com.ggec.bleservice.setting.CommandQueueCenter;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-12
 * Description: 
 * 命令管理器基类
 * 负责特定类型命令的创建和响应处理，不再负责命令队列管理
 */
public abstract class BaseCommandManager {
    private static final String TAG = "BaseCommandManager";
    
    // 命令回调
    private CommandResultListener commandResultListener;
    
    /**
     * 命令结果监听器
     */
    public interface CommandResultListener {
        /**
         * 命令执行完成回调
         * @param command 命令对象
         * @param code 结果状态码
         * @param result 执行结果
         */
        void onCommandResult(Command command, int code, String result);
        
        /**
         * 命令执行完成回调（带数据）
         * @param command 命令对象
         * @param code 结果状态码
         * @param yobackdata 返回的原始数据，失败时为null
         * @param result 执行结果
         */
        void onCommandResult(Command command, int code, Object yobackdata, String result);

        /**
         * 命令执行完成回调（带结果值）
         * @param command 命令对象
         * @param code 结果状态码
         * @param resultValue 结果值，表示具体的命令完成状态
         * @param result 执行结果
         */
        void onCommandResult(Command command, int code, int resultValue, String result);
    }
    
    /**
     * 构造函数
     */
    protected BaseCommandManager() {
        init();
    }
    
    /**
     * 初始化管理器
     * 在子类构造函数中调用
     */
    protected void init() {
        // 子类可以覆盖此方法进行初始化
    }
    
    /**
     * 设置命令结果监听器
     * @param listener 监听器
     */
    public void setCommandResultListener(CommandResultListener listener) {
        this.commandResultListener = listener;
    }
    
    /**
     * 释放资源
     */
    public void release() {
        commandResultListener = null;
    }
    
    /**
     * 获取管理器类型标识，用于日志输出
     */
    protected abstract String getManagerTag();
    
    /**
     * 创建命令并加入队列
     * @param command 要执行的命令
     */
    protected void createAndEnqueueCommand(Command command) {
        if (command == null) {
            return;
        }
        
        // 为命令注册完成回调
        command.registerOneTimeCallback(new Command.CommandCallback() {
            @Override
            public void onCommandCompleted(Command command, int code, String result) {
                // 命令已完成，调用内部处理方法
                onCommandCompletedInternal(command, code, result);
                
                // 通知监听器
                if (commandResultListener != null) {
                    commandResultListener.onCommandResult(command, code, result);
                }
            }
            
            @Override
            public void onCommandCompleted(Command command, int code, Object yobackdata, String result) {
                // 命令已完成，调用带数据的新版内部处理方法
                onCommandCompletedInternal(command, code, yobackdata, result);
                
                // 通知监听器
                if (commandResultListener != null) {
                    commandResultListener.onCommandResult(command, code, yobackdata, result);
                }
            }

            @Override
            public void onCommandCompleted(Command command, int code, int resultValue, String result) {
                // 命令已完成，调用带结果值的内部处理方法
                onCommandCompletedInternal(command, code, resultValue, result);
                
                // 通知监听器
                if (commandResultListener != null) {
                    commandResultListener.onCommandResult(command, code, resultValue, result);
                }
            }
        });
        
        // 加入统一队列
        CommandQueueCenter.getInstance().enqueueCommand(command);
    }
    
    /**
     * 命令完成的内部处理方法，子类可以覆盖此方法
     * @param command 命令对象
     * @param code 结果状态码
     * @param result 执行结果
     */
    protected void onCommandCompletedInternal(Command command, int code, String result) {
        // 默认实现为空，子类可以覆盖
    }

    /**
     * 命令完成的内部处理方法（带数据），子类可以覆盖此方法
     * @param command 命令对象
     * @param code 结果状态码
     * @param yobackdata 返回的原始数据
     * @param result 执行结果
     */
    protected void onCommandCompletedInternal(Command command, int code, Object yobackdata, String result) {
        // 默认调用无数据的版本，以保持向后兼容
        onCommandCompletedInternal(command, code, result);
    }

    /**
     * 命令完成的内部处理方法（带结果值），子类可以覆盖此方法
     * @param command 命令对象
     * @param code 结果状态码
     * @param resultValue 结果值
     * @param result 执行结果
     */
    protected void onCommandCompletedInternal(Command command, int code, int resultValue, String result) {
        // 默认调用无数据的版本，以保持向后兼容
        onCommandCompletedInternal(command, code, result);
    }
    
    /**
     * 处理响应数据
     * 当收到蓝牙数据时，如果与当前管理器相关，则会调用此方法
     * @param data 响应数据
     * @return 是否已处理
     */
    public boolean handleResponse(String data) {
        // 检查数据是否与当前管理器相关
        if (!isDataRelevant(data)) {
            return false;
        }
        
        YoBTSDKLog.d(getManagerTag(), "处理相关数据: " + data);
        handleUnsolicitedResponse(data);
        return true;
    }

    /**
     * 处理主动上报的数据，子类应覆盖此方法来解析特定于管理器的数据
     * @param data 上报的数据
     */
    protected void handleUnsolicitedResponse(String data) {
        // 默认实现为空，子类可以覆盖
        YoBTSDKLog.d(getManagerTag(), "收到主动上报数据，但未处理: " + data);
    }
    
    /**
     * 判断数据是否与当前管理器相关
     * @param data 接收到的数据
     * @return 是否相关
     */
    protected abstract boolean isDataRelevant(String data);
} 