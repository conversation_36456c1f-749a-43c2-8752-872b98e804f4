package com.ggec.bleservice.scanner;

import android.Manifest;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothManager;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import androidx.core.app.ActivityCompat;

import com.ggec.bleservice.core.BleEventDispatcher;
//import com.ggec.bleservice.scanner.impl.EnhancedBleScanner;
import com.ggec.bleservice.scanner.impl.ModernBleScanner;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-17
 * Description: 
 * 蓝牙扫描管理器
 * 负责获取适合当前Android版本的扫描器，并提供统一的扫描接口
 */
public class ScanManager {
    private static final String TAG = "ScanManager";
    
    private static ScanManager instance;
    
    private Context context;
    private IScanner scanner;
    private boolean scanning = false;
    private final Handler handler = new Handler(Looper.getMainLooper());
    private ScanConfig scanConfig = ScanConfig.createDefault();
    private final CopyOnWriteArrayList<ScanCallback> callbacks = new CopyOnWriteArrayList<>();
    private final Map<String, ParsedAdvertisingData> parsedDataCache = new ConcurrentHashMap<>();

    // 扫描器类型
    private static final int SCANNER_TYPE_MODERN = 0;
    private static final int SCANNER_TYPE_ENHANCED = 1;
    private int currentScannerType = SCANNER_TYPE_ENHANCED; // 默认使用增强型扫描器

    // 自定义设备名称过滤器开关
    private boolean customDeviceNameFilterEnabled = false;
    
    // 默认扫描超时时间
    private static final long DEFAULT_SCAN_TIMEOUT = 10000; // 10秒
    
    // 封装统一的回调
    private final com.ggec.bleservice.scanner.ScanCallback innerCallback = new com.ggec.bleservice.scanner.ScanCallback() {
        @Override
        public void onFound(BluetoothDevice device, int rssi, byte[] scanRecord, List<android.os.ParcelUuid> serviceUuids) {
            if(device == null) {
                return;
            }
            Log.d(TAG, "onFound: device = " + device.getAddress() + ", rssi = " + rssi + ",type=" + device.getType());
            if(device.getType() <= BluetoothDevice.DEVICE_TYPE_CLASSIC) {
                return;
            }

            // 过滤设备名称（如果配置了）
            try {
                // Feature Code 过滤
                String featureCodeFilter = scanConfig.getFeatureCodeFilter();
                if (featureCodeFilter != null && !featureCodeFilter.isEmpty()) {
                    ParsedAdvertisingData parsedData = ScanDataParser.parseFromServiceUuid(serviceUuids);
                    if (parsedData == null || !featureCodeFilter.equals(parsedData.getFeatureCode())) {
                        return; // Feature code不匹配，过滤掉
                    }
                    // 缓存解析的数据
                    parsedDataCache.put(device.getAddress(), parsedData);
                }

                String deviceName = null;
                try {
                    deviceName = device.getName();
                } catch (SecurityException e) {
                    YoBTSDKLog.w(TAG, "获取设备名称权限被拒绝: " + e.getMessage());
                    // 如果无法获取名称，但通过了feature code过滤，则仍然通知
                    notifyDeviceFound(device, rssi, scanRecord, serviceUuids);
                    return;
                }

                // 自定义设备名称过滤逻辑
                if (customDeviceNameFilterEnabled) {
                    if (deviceName == null || deviceName.isEmpty()) {
                        return; // 过滤掉没有名称的设备
                    }
                    String lowerCaseName = deviceName.toLowerCase();
                    if (!lowerCaseName.startsWith("hs") && !lowerCaseName.startsWith("vidda")) {
                        return; // 过滤掉名称不符合规则的设备
                    }
                }
                
                // 设备名称为空的过滤逻辑
                if (scanConfig.getDeviceNameFilter() != null) {
                    // 特殊情况：空字符串作为过滤器，表示要过滤掉没有名称的设备
                    if (scanConfig.getDeviceNameFilter().isEmpty()) {
                        // 如果设备名称为空，则不通知发现此设备
                        if (deviceName == null || deviceName.isEmpty()) {
                            return;
                        }
                    } 
                    // 常规过滤：设备名称需要包含过滤字符串
                    else if (deviceName != null && !deviceName.contains(scanConfig.getDeviceNameFilter())) {
                        return;
                    }
                }
                
                // 通知找到设备
                notifyDeviceFound(device, rssi, scanRecord, serviceUuids);
            } catch (Exception e) {
                YoBTSDKLog.e(TAG, "处理扫描结果时出错: " + e.getMessage());
                // 即使出错也通知回调
                notifyDeviceFound(device, rssi, scanRecord, serviceUuids);
            }
        }
        
        // 辅助方法，通知所有回调找到设备
        private void notifyDeviceFound(BluetoothDevice device, int rssi, byte[] scanRecord, List<android.os.ParcelUuid> serviceUuids) {
            // 通知所有回调
            for (ScanCallback callback : callbacks) {
                handler.post(() -> callback.onFound(device, rssi, scanRecord, serviceUuids));
            }
            
            // 通过事件总线分发设备发现事件
            BleEventDispatcher.getInstance().dispatchDeviceFound(device, rssi, scanRecord);
        }
        
        @Override
        public void onScanStart() {
            scanning = true;
            parsedDataCache.clear();
            for (ScanCallback callback : callbacks) {
                handler.post(callback::onScanStart);
            }
            
            // 通过事件总线分发扫描开始事件
            BleEventDispatcher.getInstance().dispatchScanStarted();
        }
        
        @Override
        public void onScanFinish() {
            scanning = false;
            for (ScanCallback callback : callbacks) {
                handler.post(callback::onScanFinish);
            }
            // 扫描结束时清除缓存
//            parsedDataCache.clear();
            
            // 通过事件总线分发扫描结束事件
            BleEventDispatcher.getInstance().dispatchScanFinished();
        }
    };
    
    /**
     * 获取单例
     * @return ScanManager实例
     */
    public static ScanManager getInstance() {
        if (instance == null) {
            synchronized (ScanManager.class) {
                if (instance == null) {
                    instance = new ScanManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化
     * @param context 上下文
     */
    public void init(Context context) {
        this.context = context.getApplicationContext();
        createScanner();
    }
    
    /**
     * 检查蓝牙权限
     * @return 是否有必要的蓝牙权限
     */
    private boolean checkBluetoothPermissions() {
        if (context == null) return false;
        
        boolean hasPermission = true;
        
        if (Build.VERSION.SDK_INT >= 31) { // Android 12+
            hasPermission = ActivityCompat.checkSelfPermission(context, 
                    "android.permission.BLUETOOTH_SCAN") == PackageManager.PERMISSION_GRANTED;
        } else {
            hasPermission = ActivityCompat.checkSelfPermission(context, 
                    Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED;
        }
        
        if (!hasPermission) {
            YoBTSDKLog.e(TAG, "缺少必要的蓝牙权限");
        }
        
        return hasPermission;
    }
    
    /**
     * 创建适合当前设备的扫描器
     */
    private void createScanner() {
        if (scanner != null || context == null) return;
        
        // 检查蓝牙是否可用
        BluetoothManager bluetoothManager = (BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE);
        if (bluetoothManager == null || bluetoothManager.getAdapter() == null) {
            YoBTSDKLog.e(TAG, "蓝牙不可用");
            return;
        }
        
        // 检查权限
        if (!checkBluetoothPermissions()) {
            YoBTSDKLog.w(TAG, "缺少蓝牙权限，扫描器可能无法正常工作");
            // 继续尝试创建扫描器，在使用时再检查权限
        }
        
        // 根据配置的扫描器类型创建扫描器
        try {
            if (currentScannerType == SCANNER_TYPE_ENHANCED) {
                scanner = new ModernBleScanner(context);
                YoBTSDKLog.d(TAG, "创建增强型蓝牙扫描器");
            } else {
                scanner = new ModernBleScanner(context);
                YoBTSDKLog.d(TAG, "创建标准蓝牙扫描器");
            }
            
            // 应用当前的扫描配置
            if (scanner != null) {
                scanner.setScanConfig(scanConfig);
            }
        } catch (Exception e) {
            YoBTSDKLog.e(TAG, "创建扫描器失败: " + e.getMessage());
            // 如果创建增强扫描器失败，尝试创建标准扫描器
            if (currentScannerType == SCANNER_TYPE_ENHANCED) {
                try {
                    scanner = new ModernBleScanner(context);
                    YoBTSDKLog.d(TAG, "增强型扫描器创建失败，回退到标准扫描器");
                    if (scanner != null) {
                        scanner.setScanConfig(scanConfig);
                    }
                } catch (Exception ex) {
                    YoBTSDKLog.e(TAG, "创建标准扫描器也失败: " + ex.getMessage());
                }
            }
        }
    }
    
    /**
     * 设置使用的扫描器类型
     * @param scannerType 扫描器类型，0-标准扫描器，1-增强扫描器
     */
    public void setScannerType(int scannerType) {
        if (scannerType != currentScannerType) {
            currentScannerType = scannerType;
            
            // 如果当前正在扫描，先停止
            boolean wasScanning = scanning;
            if (wasScanning) {
                stopScan();
            }
            
            // 释放旧扫描器
            if (scanner != null) {
                scanner.close();
                scanner = null;
            }
            
            // 创建新扫描器
            createScanner();
            
            // 如果之前在扫描，重新开始
            if (wasScanning) {
                startScan();
            }
        }
    }
    
    /**
     * 获取当前使用的扫描器类型
     * @return 扫描器类型，0-标准扫描器，1-增强扫描器
     */
    public int getScannerType() {
        return currentScannerType;
    }
    
    /**
     * 检查扫描管理器是否已初始化
     * @return 是否已初始化
     */
    public boolean isInitialized() {
        return context != null && scanner != null;
    }
    
    /**
     * 添加扫描回调
     * @param callback 回调接口
     */
    public void addScanCallback(ScanCallback callback) {
        if (callback != null && !callbacks.contains(callback)) {
            callbacks.add(callback);
        }
    }
    
    /**
     * 移除扫描回调
     * @param callback 回调接口
     */
    public void removeScanCallback(ScanCallback callback) {
        if (callback != null) {
            callbacks.remove(callback);
        }
    }
    
    /**
     * 设置扫描配置
     * @param config 扫描配置
     */
    public void setScanConfig(ScanConfig config) {
        if (config != null) {
            this.scanConfig = config;
            
            // 如果扫描器已创建，更新其配置
            if (scanner != null) {
                scanner.setScanConfig(config);
            }
        }
    }
    
    /**
     * 开始扫描
     * @return 是否成功开始扫描
     */
    public boolean startScan() {
        return startScan(scanConfig.getScanTimeout() > 0 ? scanConfig.getScanTimeout() : DEFAULT_SCAN_TIMEOUT);
    }
    
    /**
     * 开始扫描
     * @param timeoutMs 超时时间（毫秒）
     * @return 是否成功开始扫描
     */
    public boolean startScan(long timeoutMs) {
        if (scanner == null) {
            createScanner();
            
            if (scanner == null) {
                YoBTSDKLog.e(TAG, "创建扫描器失败");
                return false;
            }
        }
        
        // 检查权限
        if (!checkBluetoothPermissions()) {
            YoBTSDKLog.e(TAG, "缺少蓝牙权限，无法开始扫描");
            return false;
        }

        // 每次开始扫描前清除旧的缓存数据
        parsedDataCache.clear();
        
        if (scanning) {
            stopScan();
        }
        
        // 更新超时时间
        scanConfig.setScanTimeout(timeoutMs);
        scanner.setScanConfig(scanConfig);
        
        // 启动扫描
        scanner.startScan(innerCallback);
        return true;
    }
    
    /**
     * 停止扫描
     */
    public void stopScan() {
        if (scanner != null) {
            scanner.stopScan();
            scanning = false;
        }
        handler.removeCallbacksAndMessages(null);
    }
    
    /**
     * 是否正在扫描
     */
    public boolean isScanning() {
        return scanning;
    }
    
    /**
     * 设置是否启用自定义设备名称过滤
     * @param enable 是否启用
     */
    public void setCustomDeviceNameFilter(boolean enable) {
        this.customDeviceNameFilterEnabled = enable;
    }

    /**
     * 检查是否已启用自定义设备名称过滤
     * @return 是否已启用
     */
    public boolean isCustomDeviceNameFilterEnabled() {
        return customDeviceNameFilterEnabled;
    }
    
    /**
     * 获取当前扫描配置
     * @return 当前扫描配置
     */
    public ScanConfig getScanConfig() {
        return scanConfig;
    }

    /**
     * 获取指定地址设备的解析数据
     * @param deviceAddress 设备MAC地址
     * @return 解析后的数据，如果不存在则返回null
     */
    public ParsedAdvertisingData getParsedData(String deviceAddress) {
        return parsedDataCache.get(deviceAddress);
    }
    
    /**
     * 获取当前使用的扫描器实例
     * @return 扫描器实例
     */
    public IScanner getScanner() {
        return scanner;
    }
    
//    /**
//     * 获取增强型扫描器（如果当前使用的是增强型扫描器）
//     * @return 增强型扫描器，如果当前不是增强型扫描器则返回null
//     */
//    public EnhancedBleScanner getEnhancedScanner() {
//        if (scanner instanceof EnhancedBleScanner) {
//            return (EnhancedBleScanner) scanner;
//        }
//        return null;
//    }
    
    /**
     * 释放资源
     */
    public void release() {
        stopScan();
        if (scanner != null) {
            scanner.close();
            scanner = null;
        }
        callbacks.clear();
        handler.removeCallbacksAndMessages(null);
        context = null;
        instance = null;
    }
} 