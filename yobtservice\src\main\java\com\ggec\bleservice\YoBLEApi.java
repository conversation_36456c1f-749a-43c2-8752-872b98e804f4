package com.ggec.bleservice;

import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;

import com.ggec.bleservice.core.BleConstants;
import com.ggec.bleservice.core.BleDataCallback;
import com.ggec.bleservice.ota.OtaStatusChecker;
import com.ggec.bleservice.core.BleDataService;
import com.ggec.bleservice.core.BleEventDispatcher;
import com.ggec.bleservice.core.BleManager;
import com.ggec.bleservice.scanner.ParsedAdvertisingData;
import com.ggec.yobtsdkserver.sdkmanager.BTPermissionManager;
import com.ggec.yobtsdkserver.sdkmanager.YoBTSDKManager;
import com.ggec.bleservice.scanner.ScanConfig;
import com.ggec.bleservice.scanner.ScanManager;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 蓝牙功能统一API
 * 对外提供简洁统一的蓝牙接口，封装内部实现细节
 * 
 * 注意：此类是SDK的公开API之一，不会被混淆。
 * SDK内部实现类会被混淆以保护代码。
 */
public class YoBLEApi {
    private static final String TAG = "YoBLEApi";
    
    private static YoBLEApi instance;
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    private final List<BleListener> listeners = new ArrayList<>();
    
    /**
     * 蓝牙事件监听接口
     */
    public interface BleListener {
        /**
         * 蓝牙扫描发现设备
         * @param device 蓝牙设备
         * @param rssi 信号强度
         */
        default void onDeviceFound(BluetoothDevice device, int rssi) {}
        
        /**
         * 扫描开始
         */
        default void onScanStarted() {}
        
        /**
         * 扫描结束
         */
        default void onScanFinished() {}
        
        /**
         * 设备连接状态变化
         * 只有在设备完全就绪后才会以connected=true回调
         * @param device 蓝牙设备
         * @param connected 如果为true，表示设备已连接且已就绪
         */
        default void onConnectionStateChanged(BluetoothDevice device, boolean connected) {}

        /**
         * 连接失败
         * @param device 蓝牙设备
         * @param failed 如果为 true 表示连接失败
         */
        default void onConnectFailed(BluetoothDevice device, boolean failed) {}

        /**
         * 设备断开连接
         * @param device 蓝牙设备
         * @param disconnected 如果为 true 表示已断开连接
         */
        default void onDeviceDisconnected(BluetoothDevice device, boolean disconnected) {}
        
        /**
         * 发生错误
         * @param errorMessage 错误信息
         */
        default void onError(String errorMessage) {}
        
        /**
         * OTA升级进度更新
         * @param progress 升级进度(0-100)
         */
        default void onOtaProgressUpdated(int progress) {}
        
        /**
         * OTA升级成功
         */
        default void onOtaSuccess() {}
        
        /**
         * OTA升级失败
         * @param errorCode 错误码
         */
        default void onOtaFailed(int errorCode) {}
        
        /**
         * OTA升级取消
         */
        default void onOtaCanceled() {}
        
        /**
         * 设备就绪状态变化
         * 当设备连接、服务发现和通知启用完成后会触发
         * @param ready 设备是否就绪
         */
        default void onDeviceReadyStateChanged(boolean ready) {}

        /**
         * 系统蓝牙关闭
         */
        default void systemBluetoothClose() {}

        /**
         * 系统蓝牙开启
         */
        default void systemBluetoothOpen() {}
    }
    
    // 统一的事件监听接口，连接BleEventDispatcher
    private final BleEventDispatcher.BleEventListener bleEventListener = new BleEventDispatcher.BleEventListener() {
        @Override
        public void onDeviceFound(BluetoothDevice device, int rssi, byte[] scanRecord) {
            for (BleListener listener : listeners) {
                mainHandler.post(() -> listener.onDeviceFound(device, rssi));
            }
        }
        
        @Override
        public void onScanStarted() {
            for (BleListener listener : listeners) {
                mainHandler.post(listener::onScanStarted);
            }
        }
        
        @Override
        public void onScanFinished() {
            for (BleListener listener : listeners) {
                mainHandler.post(listener::onScanFinished);
            }
        }
        
        @Override
        public void onConnectionStateChanged(BluetoothDevice device, int status, int newState) {
            if (newState == BleConstants.STATE_CONNECTED && status == 0) {
                // 连接成功，但不触发onConnectionStateChanged回调
                // 改为在onDeviceReady中触发连接成功回调
            } else if (newState == BleConstants.STATE_DISCONNECTED) {
                // 区分连接失败和断开连接的情况
                if (BleManager.getInstance().wasEverConnected(device)) {
                    // 曾经连接成功过，现在断开 - 触发断开连接回调
                    for (BleListener listener : listeners) {
                        mainHandler.post(() -> listener.onDeviceDisconnected(device, true));
                    }
                } else {
                    // 从未连接成功过 - 触发连接失败回调
                    for (BleListener listener : listeners) {
                        mainHandler.post(() -> listener.onConnectionStateChanged(device, false));
                    }
                }
            }
        }
        
        @Override
        public void onDeviceReady(boolean ready) {
            // 只触发设备就绪状态变化回调
            for (BleListener listener : listeners) {
                mainHandler.post(() -> listener.onDeviceReadyStateChanged(ready));
            }
            
            // 在设备首次就绪时，单独触发onConnectionStateChanged回调
            // 这样分开处理，保证两个回调相互独立
            if (ready) {
                BluetoothDevice connectedDevice = BleManager.getInstance().getConnectedDevice();
                if (connectedDevice != null) {
                    for (BleListener listener : listeners) {
                        mainHandler.post(() -> listener.onConnectionStateChanged(connectedDevice, true));
                    }
                }
            }
            // 注意：设备不就绪时不触发onConnectionStateChanged(false)
            // 因为物理连接可能仍然存在
        }

        @Override
        public void systemBluetoothClose() {
            for (BleListener listener : listeners) {
                mainHandler.post(listener::systemBluetoothClose);
            }
        }

        @Override
        public void systemBluetoothOpen() {
            for (BleListener listener : listeners) {
                mainHandler.post(listener::systemBluetoothOpen);
            }
        }
    };
    
    /**
     * 获取YoBLEApi单例
     */
    public static YoBLEApi getInstance() {
        if (instance == null) {
            synchronized (YoBLEApi.class) {
                if (instance == null) {
                    instance = new YoBLEApi();
                }
            }
        }
        return instance;
    }
    
    private YoBLEApi() {
        // 私有构造函数
    }
    
    // 保存上下文的成员变量
    private Context context;
    
    /**
     * 初始化蓝牙服务
     * @param context 应用程序上下文
     */
    public void init(Context context) {
        // 保存上下文
        this.context = context.getApplicationContext();
        
        // 初始化核心管理器
        BleManager.getInstance().init(context);
        
        // 注册统一事件监听器
        BleEventDispatcher.getInstance().registerEventListener(bleEventListener);
        
        // 初始化数据服务
        BleDataService dataService = BleDataService.getInstance();
        dataService.start();
        
        // 初始化扫描管理器
        ScanManager.getInstance().init(context);
    }
    
    /**
     * 释放蓝牙服务资源
     * 应用退出时调用
     */
    public void release() {
        // 注销所有回调
        BleEventDispatcher.getInstance().unregisterEventListener(bleEventListener);
        
        BleDataService dataService = BleDataService.getInstance();
        dataService.stop();
        
        // 清理资源
        listeners.clear();
        BleManager.getInstance().release();
        instance = null;
    }
    
    /**
     * 注册蓝牙事件监听器
     * @param listener 监听器
     */
    public void registerListener(BleListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
            
            // 检查设备是否已就绪和连接状态
            boolean isDeviceReady = isDeviceReady();
            BluetoothDevice connectedDevice = BleManager.getInstance().getConnectedDevice();
            
            // 分别处理设备就绪状态回调
            if (isDeviceReady) {
                mainHandler.post(() -> listener.onDeviceReadyStateChanged(true));
            }
            
            // 单独处理连接状态回调
            // 只有当设备就绪时才通知连接成功
            if (isDeviceReady && connectedDevice != null) {
                mainHandler.post(() -> listener.onConnectionStateChanged(connectedDevice, true));
            } else {
                // 设备未就绪或未连接时通知连接失败
                mainHandler.post(() -> listener.onConnectionStateChanged(null, false));
            }
        }
    }
    
    /**
     * 注销蓝牙事件监听器
     * @param listener 监听器
     */
    public void unregisterListener(BleListener listener) {
        if (listener != null) {
            listeners.remove(listener);
        }
    }
    
    /**
     * 检查蓝牙权限
     * @param activity Activity实例
     * @return 如果权限已全部授予返回true
     */
    public boolean checkBluetoothPermissions(Activity activity) {
        return BTPermissionManager.checkAndRequestPermissions(activity);
    }
    
    /**
     * 开始扫描蓝牙设备
     * @return 是否成功开始扫描
     */
    public boolean startScan() {
        return ScanManager.getInstance().startScan();
    }
    
    /**
     * 开始扫描蓝牙设备（带超时）
     * @param timeoutMs 超时时间（毫秒）
     * @return 是否成功开始扫描
     */
    public boolean startScan(int timeoutMs) {
        return ScanManager.getInstance().startScan(timeoutMs);
    }
    
    /**
     * 使用配置开始扫描
     * @param config 扫描配置
     * @return 是否成功开始扫描
     */
    public boolean startScanWithConfig(ScanConfig config) {
        ScanManager.getInstance().setScanConfig(config);
        return ScanManager.getInstance().startScan();
    }
    
    /**
     * 停止扫描
     */
    public void stopScan() {
        ScanManager.getInstance().stopScan();
    }
    
    /**
     * 设置是否过滤没有名称的蓝牙设备
     * @param enable 是否启用过滤
     * @return 是否成功设置过滤
     */
    public boolean setFilterNoNameDevices(boolean enable) {
        try {
            ScanManager scanManager = ScanManager.getInstance();
            ScanConfig scanConfig = scanManager.getScanConfig();
            
            if (scanConfig == null) {
                scanConfig = ScanConfig.createDefault();
            }
            
            if (enable) {
                // 设置空字符串作为特殊标记，表示过滤没有名称的设备
                scanConfig.setDeviceNameFilter("");
            } else {
                // 清除过滤器
                scanConfig.setDeviceNameFilter(null);
            }
            
            scanManager.setScanConfig(scanConfig);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 检查是否已启用过滤没有名称的蓝牙设备
     * @return 是否已启用过滤
     */
    public boolean isFilterNoNameDevicesEnabled() {
        try {
            ScanManager scanManager = ScanManager.getInstance();
            ScanConfig scanConfig = scanManager.getScanConfig();
            
            // 如果设置了空字符串作为过滤器，表示启用了过滤没有名称的设备
            return scanConfig != null && 
                   scanConfig.getDeviceNameFilter() != null && 
                   scanConfig.getDeviceNameFilter().isEmpty();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 设置是否启用自定义设备名称过滤
     * 启用后，将只扫描名称以 "HS" 或 "vidda" 开头的设备
     * @param enable 是否启用过滤
     * @return 是否成功设置
     */
    public boolean setCustomDeviceNameFilter(boolean enable) {
        try {
            ScanManager.getInstance().setCustomDeviceNameFilter(enable);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查是否已启用自定义设备名称过滤
     * @return 是否已启用过滤
     */
    public boolean isCustomDeviceNameFilterEnabled() {
        try {
            return ScanManager.getInstance().isCustomDeviceNameFilterEnabled();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 设置是否通过特征码过滤设备
     * 启用后，将只扫描匹配预定义特征码的设备
     * @param enable 是否启用过滤
     * @return 是否成功设置
     */
    public boolean setFilterByFeatureCode(boolean enable) {
        try {
            ScanManager scanManager = ScanManager.getInstance();
            ScanConfig scanConfig = scanManager.getScanConfig();
            if (scanConfig == null) {
                scanConfig = ScanConfig.createDefault();
            }
            // 使用ScanConfig内部的特征码过滤
            scanConfig.useOurFeatureCodeFilter(enable);
            scanManager.setScanConfig(scanConfig);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查是否已启用特征码过滤
     * @return 是否已启用过滤
     */
    public boolean isFilteringByFeatureCode() {
        try {
            ScanManager scanManager = ScanManager.getInstance();
            ScanConfig scanConfig = scanManager.getScanConfig();
            return scanConfig != null &&
                    scanConfig.getFeatureCodeFilter() != null &&
                    !scanConfig.getFeatureCodeFilter().isEmpty();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 连接设备
     * @param device 蓝牙设备
     * @return 是否成功发起连接
     */
    public boolean connect(BluetoothDevice device) {
        return BleManager.getInstance().connect(device);
    }
    
    /**
     * 断开当前连接
     */
    public void disconnect() {
        BleManager.getInstance().disconnect();
    }
    
    /**
     * 获取已连接的设备
     * @return 已连接的蓝牙设备，未连接时返回null
     */
    public BluetoothDevice getConnectedDevice() {
        return BleManager.getInstance().getConnectedDevice();
    }
    
    /**
     * 检查是否已连接设备
     * @return 是否已连接设备
     */
    public boolean isDeviceConnected() {
        return BleManager.getInstance().isDeviceConnected();
    }
    
    /**
     * 检查设备是否已就绪
     * 设备就绪指设备已连接、服务已发现且通知已启用
     * 只有设备就绪后才能发送命令
     * @return 如果设备已就绪返回true
     */
    public boolean isDeviceReady() {
        return BleDataService.getInstance().isDeviceReady();
    }
    
    /**
     * 注册设备就绪状态监听器
     * 此方法为便捷方法，可直接监听设备就绪状态而不需要注册完整的BleListener
     * @param callback 就绪状态回调
     */
    public void registerDeviceReadyCallback(DeviceReadyCallback callback) {
        if (callback != null) {
            BleListener listener = new BleListener() {
                @Override
                public void onDeviceReadyStateChanged(boolean ready) {
                    callback.onDeviceReady(ready);
                }
            };
            registerListener(listener);
            
            // 如果设备已就绪，立即通知
            if (isDeviceReady()) {
                mainHandler.post(() -> callback.onDeviceReady(true));
            }
        }
    }
    
    /**
     * 设备就绪状态回调接口
     */
    public interface DeviceReadyCallback {
        /**
         * 设备就绪状态变化时调用
         * @param ready 设备是否就绪
         */
        void onDeviceReady(boolean ready);
    }
    
    /**
     * 是否正在扫描
     * @return 是否正在扫描
     */
    public boolean isScanning() {
        return ScanManager.getInstance().isScanning();
    }
    
    /**
     * 检查设备是否支持蓝牙
     * @return 如果设备支持蓝牙返回true
     */
    public boolean isBluetoothSupported() {
        BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
        return adapter != null;
    }
    
    /**
     * 检查蓝牙是否已开启
     * @return 如果蓝牙已开启返回true
     */
    public boolean isBluetoothEnabled() {
        BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
        return adapter != null && adapter.isEnabled();
    }
    
    /**
     * 设置文件日志是否启用
     * @param enabled 是否启用文件日志
     */
    public void setFileLogEnabled(boolean enabled) {
        YoBTSDKLog.setFileLogEnabled(enabled);
    }

    /**
     * 获取文件日志是否启用
     * @return 文件日志是否启用
     */
    public boolean isFileLogEnabled() {
        return YoBTSDKLog.isFileLogEnabled();
    }
    
    /**
     * 获取SDK版本
     * @return SDK版本字符串
     */
    public String getSDKVersion() {
        return YoBTSDKManager.getSDKVersion();
    }
    
    /**
     * 获取SDK版本号
     * @return SDK版本号
     */
    public int getSDKVersionCode() {
        return YoBTSDKManager.getSDKVersionCode();
    }
    
    /**
     * 获取SDK构建日期
     * @return SDK构建日期
     */
    public String getSDKBuildDate() {
        return YoBTSDKManager.getSDKBuildDate();
    }
    

    
    /**
     * 检查蓝牙核心管理器是否已初始化
     * @return 是否已初始化
     */
    public boolean isBleManagerInitialized() {
        return BleManager.getInstance().isInitialized();
    }
    
    /**
     * 检查蓝牙数据服务是否已启动
     * @return 是否已启动
     */
    public boolean isDataServiceRunning() {
        return BleDataService.getInstance().isRunning();
    }
    
    // ====================  OTA 相关接口 ====================
    
    /**
     * 查询OTA状态结果回调
     */
    public interface IsitCanOtaCallback {
        /**
         * 查询结果回调
         * @param canOta 是否可以进行OTA
         */
        void onResult(boolean canOta);
    }
    
    /**
     * 查询是否可以进行OTA升级
     * @param callback OTA状态回调
     */
    public void IsitCanOTA(IsitCanOtaCallback callback) {
        if (callback == null) {
            return;
        }

        OtaStatusChecker.getInstance(getContext()).checkOtaBatteryStatusAsync(
            (ready, code) -> callback.onResult(ready)
        );
    }
    
    /**
     * 获取应用程序上下文
     * @return 应用程序上下文
     */
    private Context getContext() {
        // BleManager没有getContext方法，直接返回初始化时保存的上下文
        return context;
    }
    
    // ====================  BLE广播数据解析相关接口 ====================

    /**
     * 获取指定蓝牙设备的解析后广播数据。
     * 此信息仅在扫描期间，且设备通过筛选后可用。
     * @param device 蓝牙设备
     * @return 解析后的数据对象 (macAddress, color, featureCode)，如果不存在则返回null
     */
    public ParsedAdvertisingData getParsedAdvertisingData(BluetoothDevice device) {
        if (device == null) {
            return null;
        }
        return ScanManager.getInstance().getParsedData(device.getAddress());
    }

    /**
     * 获取指定蓝牙设备的解析后广播数据，并以字符串形式返回。
     * @param device 蓝牙设备
     * @return 解析后的数据字符串，如果不存在则返回null
     */
    public String getParsedAdvertisingDataAsString(BluetoothDevice device) {
        ParsedAdvertisingData data = getParsedAdvertisingData(device);
        return data != null ? data.toString() : null;
    }
}