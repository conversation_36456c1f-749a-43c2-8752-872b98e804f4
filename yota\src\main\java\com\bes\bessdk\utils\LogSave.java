package com.bes.bessdk.utils;

import android.annotation.SuppressLint;
import android.text.format.DateFormat;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class LogSave {

    @SuppressLint("StaticFieldLeak")
    private static volatile LogSave mLogUtils;

    private String filePath = "";
    private List<String> contentList = new ArrayList<>();
    private boolean isSaving = true;

    public static LogSave getLogUtils() {
        synchronized (LogSave.class) {
            if (mLogUtils == null) {
                mLogUtils = new LogSave();
            }
        }
        return mLogUtils;
    }

    public void setPath(String path) {
        filePath = path;
    }

    public void writeLog(String content) {
        contentList.add(content);
        if (!isSaving) {
            isSaving = true;
            startSaveLogThread();
        }
    }

    private void startSaveLogThread() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                while (contentList.size() > 0) {
                    String content = contentList.get(0);
                    addLog(filePath, content);
                    if (contentList.size() > 0) {
                        contentList.remove(0);
                    }
                }
                isSaving = false;
            }
        }).start();
    }

    public void addLog(String path, String content) {
        File file = new File(path);
        try {
            if (!file.exists()) {
                file.createNewFile();
            }
            FileInputStream fis = new FileInputStream(file);
            long size = fis.available();
            fis.close();
            /**
             * 当文件大小大于100MByte时，主动删除
             */
            if (size >= 160000000) {
                file.delete();
                return;
            }

            FileOutputStream stream = new FileOutputStream(file, true);
            long curTimeMillis = System.currentTimeMillis();
            String temp = DateFormat.format("yyyy-MM-dd HH:mm:ss:", curTimeMillis) + (curTimeMillis + "").substring(10, 13) +
                    " " + content + "\n";
            byte[] buf = temp.getBytes();
            stream.write(buf);
            stream.close();

        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }


}
