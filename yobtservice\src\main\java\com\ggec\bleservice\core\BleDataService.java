package com.ggec.bleservice.core;

import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGattService;
import android.os.Handler;
import android.os.Looper;

import com.ggec.bleservice.setting.CommandQueueCenter;
import com.ggec.yobtsdkserver.utils.YoBTSDKLog;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 蓝牙数据通信服务
 * 专门处理蓝牙数据的发送和接收
 */
public class BleDataService {
    
    private static final String TAG = "BleDataService";
    
    // 最近接收的数据缓存，用于防止重复处理
    private final ConcurrentHashMap<String, Long> recentDataCache = new ConcurrentHashMap<>();
    
    // 通知启用状态
    private boolean isNotificationEnabled = false;
    
    // 设备就绪状态
    private boolean isDeviceReady = false;
    
    // 通知启用后到设备就绪的延迟（毫秒）
    private static final long DEVICE_READY_DELAY = 50;
    
    // 设备断开连接后到设置未就绪状态的延迟（毫秒）- 用于处理短暂的连接中断
    private static final long DEVICE_DISCONNECT_DELAY = 0;
    
    // 主线程Handler
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    // 设备就绪Runnable
    private final Runnable deviceReadyRunnable = () -> {
        isDeviceReady = true;
        CommandQueueCenter.setDeviceReady(true);
        // 分发设备就绪状态事件
        BleEventDispatcher.getInstance().dispatchDeviceReady(true);
        YoBTSDKLog.i(TAG, "通知启用成功，设备已就绪。");
    };
    
    // 设备断开连接处理Runnable
    private final Runnable deviceDisconnectRunnable = () -> {
        isNotificationEnabled = false;
        isDeviceReady = false;
        CommandQueueCenter.setDeviceReady(false);
        // 分发设备就绪状态变化事件
        BleEventDispatcher.getInstance().dispatchDeviceReady(false);
        YoBTSDKLog.w(TAG, "设备断开连接延迟处理完成，设备状态设为未就绪");
    };
    
    // 数据回调列表
    private final List<BleDataCallback> callbacks = new ArrayList<>();
    
    // 统一的蓝牙事件监听器
    private final BleEventDispatcher.BleEventListener bleEventListener;
    
    // 单例实例
    private static BleDataService instance;
    
    /**
     * 获取单例实例
     * @return BleDataService实例
     */
    public static synchronized BleDataService getInstance() {
        if (instance == null) {
            instance = new BleDataService();
        }
        return instance;
    }
    
    /**
     * 私有构造方法
     */
    private BleDataService() {
        // 创建统一的蓝牙事件监听器
        bleEventListener = new BleEventDispatcher.BleEventListener() {
            @Override
            public void onConnectionStateChanged(BluetoothDevice device, int status, int newState) {
                if (newState == BleConstants.STATE_CONNECTED) {
                    // 设备重新连接时，取消之前的断开连接延迟处理
                    mainHandler.removeCallbacks(deviceDisconnectRunnable);
                    YoBTSDKLog.i(TAG, "设备连接，取消断开连接延迟处理");
                } else {
                    // 连接断开时，不立即设置为未就绪，而是延迟处理，避免短暂连接中断影响命令执行
                    mainHandler.removeCallbacks(deviceDisconnectRunnable);
                    mainHandler.postDelayed(deviceDisconnectRunnable, DEVICE_DISCONNECT_DELAY);
                    YoBTSDKLog.w(TAG, "设备断开连接，将在" + DEVICE_DISCONNECT_DELAY + "ms后设置为未就绪状态");
                }
            }
            
            @Override
            public void onServicesDiscovered(BluetoothDevice device, List<BluetoothGattService> services, int status) {
                // 服务发现成功后，直接启用通知
//                if (status == 0) {
//                    enableNotifications();
//                }
            }

            @Override
            public void onMtuChanged(BluetoothDevice device, int mtu, int status) {
                YoBTSDKLog.i(TAG, "onMtuChanged: " + mtu);
                if(status == 0){
                    enableNotifications();
                }
            }

            @Override
            public void onNotificationEnabled(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, int status) {
                if (status == 0) {
                    mainHandler.removeCallbacks(deviceReadyRunnable);
                    mainHandler.post(deviceReadyRunnable);
                }
            }

            @Override
            public void onCharacteristicRead(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, 
                                          byte[] value, String hexValue, int status) {
                if (status == 0) {
                    if (hexValue != null) {
                        YoBTSDKLog.i(TAG, "特征值读取成功: " + characteristicUUID + ", 数据: " + hexValue);
                        notifyDataReceived(hexValue);
                    } else {
                        YoBTSDKLog.i(TAG, "特征值读取成功: " + characteristicUUID + ", 但数据为null");
                        notifyError("读取到空数据");
                    }
                } else {
                    YoBTSDKLog.e(TAG, "特征值读取失败: " + characteristicUUID);
                    notifyError("读取特征值失败");
                }
            }
            
            @Override
            public void onCharacteristicWrite(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, int status) {
                // 不打印日志，避免重复
                if (status != 0) {
                    notifyError("写入特征值失败");
                }
            }
            
            @Override
            public void onCharacteristicChanged(BluetoothDevice device, UUID serviceUUID, UUID characteristicUUID, 
                                           byte[] value, String hexValue) {
                String uuidStr = (characteristicUUID != null) ? characteristicUUID.toString() : "unknown";

                // 检查数据是否为空
                if (hexValue == null) {
                    YoBTSDKLog.w(TAG, "收到数据通知: " + uuidStr + ", 但数据为null");
                    return;
                }
                
                // 添加长度检查，防止空指针异常
                if (hexValue.isEmpty()) {
                    YoBTSDKLog.w(TAG, "收到数据通知: " + uuidStr + ", 但数据长度为0");
                    return;
                }
                
                // 检查是否是重复数据
                if (!isRecentData(hexValue)) {
                    //YoBTSDKLog.i(TAG, "收到数据通知: " + uuidStr + ", 数据: " + hexValue);
                    notifyDataReceived(hexValue);
                    // 记录此数据已处理
                    markDataAsRecent(hexValue);
                }
            }
        };
    }
    
    /**
     * 检查是否是最近处理过的数据
     * @param data 数据
     * @return 是否是重复数据
     */
    private boolean isRecentData(String data) {
        Long timestamp = recentDataCache.get(data);
        if (timestamp == null) {
            return false;
        }
        
        // 检查数据是否过期
        long currentTime = System.currentTimeMillis();
        if (currentTime - timestamp > BleConstants.DATA_DUPLICATE_FILTER_TIME) {
            recentDataCache.remove(data);
            return false;
        }
        
        return true;
    }
    
    /**
     * 标记数据为最近处理过
     * @param data 数据
     */
    private void markDataAsRecent(String data) {
        recentDataCache.put(data, System.currentTimeMillis());
    }
    
    /**
     * 注册数据回调
     * @param callback 回调接口
     */
    public void registerCallback(BleDataCallback callback) {
        if (callback != null && !callbacks.contains(callback)) {
            callbacks.add(callback);
        }
    }
    
    /**
     * 注销数据回调
     * @param callback 回调接口
     */
    public void unregisterCallback(BleDataCallback callback) {
        if (callback != null) {
            callbacks.remove(callback);
        }
    }
    
    /**
     * 启动服务
     */
    public void start() {
        BleEventDispatcher.getInstance().registerEventListener(bleEventListener);
    }
    
    /**
     * 停止服务
     */
    public void stop() {
        BleEventDispatcher.getInstance().unregisterEventListener(bleEventListener);
    }
    
    /**
     * 判断服务是否正在运行
     * @return 服务是否在运行
     */
    public boolean isRunning() {
        return BleEventDispatcher.getInstance().hasEventListener(bleEventListener);
    }
    
    /**
     * 通知数据发送成功
     * @param data 发送的数据
     */
    private void notifyDataSent(String data) {
        for (BleDataCallback callback : callbacks) {
            mainHandler.post(() -> callback.onDataSent(data));
        }
    }
    
    /**
     * 通知数据接收
     * @param data 接收到的数据
     */
    private void notifyDataReceived(String data) {
        for (BleDataCallback callback : callbacks) {
            mainHandler.post(() -> callback.onDataReceived(data));
        }
    }
    
    /**
     * 通知错误
     * @param errorMsg 错误信息
     */
    private void notifyError(String errorMsg) {
        for (BleDataCallback callback : callbacks) {
            mainHandler.post(() -> callback.onError(errorMsg));
        }
    }
    
    /**
     * 启用通知
     * @return 是否成功
     */
    public boolean enableNotifications() {
        // 如果通知已启用，则不再重复启用
        if (isNotificationEnabled) {
            return true;
        }
        
        BluetoothDevice connectedDevice = BleManager.getInstance().getConnectedDevice();
        if (connectedDevice != null) {
            YoBTSDKLog.i(TAG, "启用数据通知");
            
            // 设置特征值通知
            boolean result = BleManager.getInstance().setCharacteristicNotification(
                BleConstants.SERVICE_UUID, 
                BleConstants.READ_CHARACTERISTIC_UUID, 
                true
            );
            
            if (result) {
                isNotificationEnabled = true;
                // 添加一个短暂延迟，确保蓝牙栈完全准备好接收命令
                // mainHandler.removeCallbacks(deviceReadyRunnable);
                // mainHandler.postDelayed(deviceReadyRunnable, DEVICE_READY_DELAY);
            } else {
                YoBTSDKLog.e(TAG, "通知启用失败");
            }
            
            return result;
        }
        
        return false;
    }
    
    /**
     * 检查设备是否已就绪
     * 设备就绪指已连接、已发现服务且已启用通知
     * @return 是否已就绪
     */
    public boolean isDeviceReady() {
        return isDeviceReady && isNotificationEnabled && BleManager.getInstance().isDeviceConnected();
    }
    
    /**
     * 发送数据
     * @param data 要发送的十六进制字符串数据
     * @return 是否成功
     */
    public boolean sendData(String data) {
        BluetoothDevice connectedDevice = BleManager.getInstance().getConnectedDevice();
        if (connectedDevice != null && isNotificationEnabled) {
            boolean result = BleManager.getInstance().writeCharacteristic(
                BleConstants.SERVICE_UUID,
                BleConstants.WRITE_CHARACTERISTIC_UUID,
                data
            );

            if (result) {
                notifyDataSent(data);
            } else {
                YoBTSDKLog.e(TAG, "数据发送失败: " + data);
            }

            return result;
        } else {
            if (connectedDevice == null) {
                YoBTSDKLog.e(TAG, "发送数据失败: 设备未连接");
            } else if (!isNotificationEnabled) {
                YoBTSDKLog.e(TAG, "发送数据失败: 通知未启用");
            }
            return false;
        }
    }
    
    /**
     * 发送测试数据
     * @return 是否成功
     */
    public boolean sendTestData() {
        return sendData(BleConstants.TEST_DATA);
    }
    
    /**
     * 清除缓存
     */
    public void clearCache() {
        recentDataCache.clear();
    }
} 